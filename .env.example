# OpenRouter API Key
OPENROUTER_API_KEY="sk-or-..."

# Credentials for the dev WooCommerce DB
DEV_SITE_DB_HOST="127.0.0.1"
DEV_SITE_DB_PORT="3306"
DEV_SITE_DB_USER="your_db_user"
DEV_SITE_DB_PASSWORD="your_secret_password"
DEV_SITE_DB_NAME="your_woocommerce_db"

# WordPress table prefix (default: wp_)
WP_TABLE_PREFIX="wp_"

# Logging configuration
LOG_LEVEL="INFO"
LOG_FILE_PATH="./logs/structura-ai.log"

# Local SQLite database path
LOCAL_DB_PATH="./data/structura_ai.db"

# Application configuration
APP_DATA_DIR="./data"
CACHE_TTL_HOURS="24"

# OpenRouter configuration
OPENROUTER_BASE_URL="https://openrouter.ai/api/v1"
OPENROUTER_DEFAULT_MODEL="z-ai/glm-4.5-air:free"
OPENROUTER_FALLBACK_MODEL="mistralai/mistral-7b-instruct"
OPENROUTER_TEMPERATURE="0.0"

# Processing configuration
DEFAULT_BATCH_SIZE="10"
SIMILARITY_THRESHOLD="0.99"
LENGTH_DIFF_THRESHOLD="5"
RETRY_MAX_ATTEMPTS="3"
RETRY_BACKOFF_SECONDS="2,4,8"

# API configuration
API_HOST="0.0.0.0"
API_PORT="8000"
API_DEBUG="false"
API_CORS_ORIGINS="http://localhost:3000,http://127.0.0.1:3000"

# JWT configuration
JWT_SECRET_KEY="your-secret-jwt-key-change-this-in-production"
JWT_ALGORITHM="HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES="60"
JWT_REFRESH_TOKEN_EXPIRE_DAYS="7"

# Redis configuration
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_DB="0"