# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Environment Setup

**CRITICAL**: Always activate the virtual environment before any Python work:
```bash
source .venv/bin/activate
```

**Package Installation** (required for CLI commands):
```bash
# Install in editable mode (recommended for development)
pip install -e .

# Or with dev dependencies
pip install -e .[dev]
```

**Port Configuration**: Use port 8003 (port 8000 is already in use)

## Core Architecture

**Structura AI** is an AI-powered HTML formatter for WooCommerce products using LLM models via OpenRouter.

### System Components

- **CLI Layer** (`src/structura_ai/cli/`): Click-based command interface with system, site, profile, and processing commands
- **API Layer** (`src/structura_ai/api/`): FastAPI REST API with authentication, routers for all operations, and middleware
- **Services Layer** (`src/structura_ai/services/`): Core business logic including AI service, content processor, HTML sanitizer, and repository patterns
- **Models Layer** (`src/structura_ai/models/`): SQLAlchemy models for site, profile, user, job_run, and AI cache
- **Database Layer** (`src/structura_ai/database/`): Dual database setup - MySQL (WooCommerce) + SQLite (local cache)

### Key Dependencies

- **SQLAlchemy 2.0+**: ORM with async support
- **FastAPI + Uvicorn**: API framework and ASGI server
- **Click**: CLI framework
- **OpenRouter**: LLM API integration
- **BeautifulSoup4 + Bleach**: HTML processing and sanitization
- **Pydantic**: Settings and data validation

## Development Commands

### Environment
```bash
# Activate environment (ALWAYS FIRST)
source .venv/bin/activate

# Install package
pip install -e .
```

### Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=structura_ai

# Run current phase tests
python -m pytest tests/test_phase3.py -v

# Run specific test file
pytest tests/test_config.py
```

### Code Quality (run after changes)
```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/
```

### Application Commands
```bash
# System operations
structura-ai system init
structura-ai system status
structura-ai system check

# Site management
structura-ai site list
structura-ai site add mysite --host localhost --user dbuser --password dbpass --database woocommerce_db
structura-ai site test mysite

# Profile management
structura-ai profile list
structura-ai profile create default

# Content processing
structura-ai preview --site mysite --profile default
structura-ai run --site mysite --profile default --confirm
```

### API Server
```bash
# Start FastAPI server (uses port 8003)
structura-ai-api
# or
python -m structura_ai.api.main
```

## Project Configuration

### Environment Variables (.env)
```bash
# OpenRouter API Key (required)
OPENROUTER_API_KEY="sk-or-your-key-here"

# Database credentials
DEV_SITE_DB_HOST="127.0.0.1"
DEV_SITE_DB_USER="your_db_user"
DEV_SITE_DB_PASSWORD="your_db_password"
DEV_SITE_DB_NAME="your_woocommerce_db"
```

### Code Style Configuration
- **Black**: line-length=88, target Python 3.11
- **isort**: profile="black", line_length=88
- **MyPy**: disallow_untyped_defs=true, python_version="3.11"

## Current Development Status

- ✅ **FASE 1**: Setup Progetto e Configurazione Base
- ✅ **FASE 2**: Connettività Database WooCommerce  
- ✅ **FASE 3**: Integrazione LLM e Guard-rail
- ✅ **FASE 4**: Sanitizzazione HTML e Validazione
- ✅ **FASE 5**: Pipeline Processing  
- ✅ **FASE 6**: CLI e Gestione Operativa

**Status**: Progetto completato e funzionale

## Security Features

- Content similarity guard-rails (threshold 0.99)
- Content length validation (minimum 5 characters)
- HTML whitelist sanitization
- Write-once backup system
- File locking for concurrent execution prevention
- Circuit breaker pattern for API resilience

## Notes
- Non usare il tool mcp "serena" per la cartella .bmad-core
- in caso di errori usa context7 per la documentazione aggiornata su api o framework