# Structura AI

HTML Formatter per prodotti WooCommerce con AI-powered formatting.

## Descrizione

Structura AI è uno strumento CLI interno per formattare automaticamente il contenuto HTML dei prodotti WooCommerce utilizzando modelli LLM (Large Language Models) tramite OpenRouter.

### Caratteristiche principali

- ✅ Formattazione automatica HTML via LLM
- ✅ Backup automatico del contenuto originale 
- ✅ Guard-rail per sicurezza contenuti (similarità, lunghezza)
- ✅ Cache intelligente per ottimizzazione
- ✅ Modalità Preview e Run
- ✅ Filtri per categorie e post ID
- ✅ Reporting JSON/CSV
- ✅ Sistema di retry con backoff esponenziale
- ✅ File locking per prevenire esecuzioni concorrenti

## Installazione

### Prerequisiti

- Python 3.11+
- Accesso al database MySQL WooCommerce
- Chiave API OpenRouter

### Setup ambiente

```bash
# Clona il repository
git clone <repository-url> structura-ai
cd structura-ai

# Crea e attiva un virtualenv (consigliato)
python3.11 -m venv .venv
source .venv/bin/activate

# Copia la configurazione di esempio e personalizzala
cp .env.example .env
# Modifica .env inserendo le tue credenziali
```

### Installazione del pacchetto (consigliata)

Usa l'installazione in modalità editable per abilitare lo script `structura-ai` e lavorare direttamente sul codice sorgente:

```bash
pip install -e .
# Per includere anche le dipendenze di sviluppo:
# pip install -e .[dev]
```

Verifica che il comando sia disponibile:

```bash
structura-ai --help
```

In alternativa (non consigliato), puoi installare solo le dipendenze:

```bash
pip install -r requirements.txt
```

### Disinstallazione

Per rimuovere il pacchetto:

```bash
pip uninstall structura-ai
```

Per rimuovere anche l'ambiente virtuale (opzionale):

```bash
deactivate  # se attivo
rm -rf .venv
```

### Configurazione .env

```bash
# OpenRouter API Key
OPENROUTER_API_KEY="sk-or-your-key-here"

# Database WooCommerce (esempio per un sito di sviluppo)
DEV_SITE_DB_HOST="127.0.0.1"
DEV_SITE_DB_USER="your_db_user"
DEV_SITE_DB_PASSWORD="your_db_password"
DEV_SITE_DB_NAME="your_woocommerce_db"
```

## Uso

Assicurati di avere l'ambiente attivo e il pacchetto installato con `pip install -e .`.

```bash
source .venv/bin/activate
structura-ai --help
```

### Comandi principali

- Inizializzazione e diagnostica

```bash
# Inizializza sistema (DB locale, profilo default, ecc.)
structura-ai system init

# Stato e verifica
structura-ai system status
structura-ai system check
structura-ai system info

# Reset DB locale (richiede conferma)
structura-ai system reset
```

- Gestione Siti

```bash
# Elencare siti
structura-ai site list

# Aggiungere sito
structura-ai site add mysite \
  --host localhost \
  --user dbuser \
  --password dbpass \
  --database woocommerce_db

# Test connessione sito
structura-ai site test mysite

# Aggiornare metadati sito (esempi)
structura-ai site update mysite --active
structura-ai site update mysite --display-name "Woo Dev"

# Rimuovere sito
structura-ai site remove mysite
```

- Gestione Profili

```bash
# Elencare profili
structura-ai profile list

# Creare profilo
structura-ai profile create default

# Eliminare profilo
structura-ai profile delete old_profile
```

- Preview (Anteprima)

```bash
# Preview completa
structura-ai preview --site mysite --profile default

# Preview con filtri
structura-ai preview \
  --site mysite \
  --profile default \
  --categories "12,15,20" \
  --post-ids "100,101,102"

# Preview con report CSV
structura-ai preview \
  --site mysite \
  --profile default \
  --report-format csv \
  --output ./reports/preview.csv
```

- Run (Esecuzione)

```bash
# Esecuzione con conferma
structura-ai run --site mysite --profile default

# Esecuzione automatica (senza conferma)
structura-ai run \
  --site mysite \
  --profile default \
  --confirm

# Esecuzione su prodotti specifici
structura-ai run \
  --site mysite \
  --profile default \
  --post-ids "100,101,102" \
  --confirm
```

### Alternative (senza console script)

Se preferisci non installare il pacchetto, puoi invocare il modulo principale direttamente:

```bash
python -m structura_ai.main --help
python -m structura_ai.main site list
```

## Architettura

```
structura-ai/
├── src/structura_ai/
│   ├── config/          # Configurazione e settings
│   ├── database/        # Database SQLite locale
│   ├── models/          # Modelli SQLAlchemy
│   ├── services/        # Servizi (file lock, etc.)
│   └── main.py          # Entry point CLI
├── tests/               # Test suite
├── dev-docs/           # Documentazione sviluppo
└── requirements.txt    # Dipendenze Python
```

## Sviluppo

### Test

```bash
# Eseguire tutti i test
pytest

# Test con coverage
pytest --cov=structura_ai

# Test specifici
pytest tests/test_config.py
```

### Linting

```bash
# Format code
black src/ tests/

# Sort imports
isort src/ tests/

# Type checking
mypy src/
```

## Sicurezza

- ✅ Guard-rail di similarità (soglia 0.99)
- ✅ Guard-rail di lunghezza (soglia 5 caratteri)
- ✅ Whitelist HTML per tag/attributi consentiti
- ✅ Backup write-once del contenuto originale
- ✅ Validazione input e sanitizzazione output
- ✅ File locking per prevenire concorrenza

## Status Sviluppo

- ✅ **FASE 1**: Setup Progetto e Configurazione Base
- ⏳ **FASE 2**: Connettività Database WooCommerce  
- ⏳ **FASE 3**: Integrazione LLM e Guard-rail
- ⏳ **FASE 4**: Sanitizzazione HTML e Validazione
- ⏳ **FASE 5**: Pipeline Processing
- ⏳ **FASE 6**: CLI e Gestione Operativa

## Licenza

Progetto interno - Tutti i diritti riservati.