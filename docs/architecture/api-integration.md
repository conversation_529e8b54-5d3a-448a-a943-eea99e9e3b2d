# API Integration

## Service Template

```typescript
// services/api/sites.ts
import { apiClient } from './client'
import type { 
  Site, 
  CreateSiteRequest, 
  UpdateSiteRequest,
  SiteConnectionTest 
} from '@/types/sites'

export const siteApi = {
  // Get all sites
  getAll: async (): Promise<Site[]> => {
    const response = await apiClient.get('/sites/')
    return response.data
  },

  // Get site by ID
  getById: async (siteId: string): Promise<Site> => {
    const response = await apiClient.get(`/sites/${siteId}`)
    return response.data
  },

  // Create new site
  create: async (data: CreateSiteRequest): Promise<Site> => {
    const response = await apiClient.post('/sites/', data)
    return response.data
  },

  // Test site connection
  testConnection: async (siteId: string): Promise<SiteConnectionTest> => {
    const response = await apiClient.post(`/sites/${siteId}/test`)
    return response.data
  }
}
```

## API Client Configuration

```typescript
// services/api/client.ts
import axios, { AxiosError, AxiosResponse } from 'axios'
import { toast } from '@/components/ui/use-toast'

// Create axios instance
export const apiClient = axios.create({
  baseURL: 'http://localhost:8003/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor for authentication
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    // Handle 401 unauthorized with token refresh
    if (error.response?.status === 401) {
      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          const response = await axios.post('/api/auth/refresh', {
            refresh_token: refreshToken
          })
          
          const { access_token } = response.data
          localStorage.setItem('auth_token', access_token)
          
          // Retry original request
          return apiClient(error.config!)
        }
      } catch {
        localStorage.clear()
        window.location.href = '/login'
      }
    }

    // Show error toast
    const message = error.response?.data?.message || 'An unexpected error occurred'
    toast({
      title: 'Error',
      description: message,
      variant: 'destructive',
    })

    return Promise.reject(error)
  }
)
```
