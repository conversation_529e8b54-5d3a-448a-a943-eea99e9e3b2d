# Component Standards

## Component Template

```typescript
import React from 'react'
import { cn } from '@/lib/utils'

interface ComponentNameProps {
  /**
   * Brief description of the prop
   */
  propName: string
  /**
   * Optional prop with default value
   */
  variant?: 'default' | 'secondary' | 'outline'
  /**
   * Handler function
   */
  onAction?: (value: string) => void
  /**
   * Additional CSS classes
   */
  className?: string
  /**
   * Child elements
   */
  children?: React.ReactNode
}

export const ComponentName: React.FC<ComponentNameProps> = ({
  propName,
  variant = 'default',
  onAction,
  className,
  children,
  ...props
}) => {
  const [localState, setLocalState] = React.useState<string>('')

  const handleAction = React.useCallback(
    (value: string) => {
      setLocalState(value)
      onAction?.(value)
    },
    [onAction]
  )

  return (
    <div
      className={cn(
        'base-styles',
        {
          'variant-default': variant === 'default',
          'variant-secondary': variant === 'secondary',
          'variant-outline': variant === 'outline',
        },
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

ComponentName.displayName = 'ComponentName'
```

## Naming Conventions

**File Naming:**
- **Components**: PascalCase (e.g., `SiteForm.tsx`, `ProfileCard.tsx`)
- **Hooks**: camelCase with "use" prefix (e.g., `useAuth.ts`, `useSites.ts`)
- **Services**: camelCase (e.g., `apiClient.ts`, `authService.ts`)
- **Types**: PascalCase for interfaces, camelCase for files (e.g., `sites.ts` containing `SiteConfig`)
- **Constants**: SCREAMING_SNAKE_CASE (e.g., `API_ENDPOINTS`, `DEFAULT_TIMEOUT`)

**Component Naming:**
- **Pages**: `[Feature]Page.tsx` (e.g., `SitesPage.tsx`, `ProfilesPage.tsx`)
- **Forms**: `[Feature]Form.tsx` (e.g., `SiteForm.tsx`, `ProfileForm.tsx`)
- **Layout**: Descriptive names (e.g., `AppShell.tsx`, `Sidebar.tsx`)
- **UI Components**: Match shadcn/ui conventions (e.g., `Button.tsx`, `Dialog.tsx`)

**TypeScript Conventions:**
- **Props Interfaces**: `[ComponentName]Props` (e.g., `SiteFormProps`)
- **API Types**: Match backend schema names (e.g., `Site`, `Profile`, `JobRun`)
- **Hook Return Types**: `Use[FeatureName]Return` (e.g., `UseAuthReturn`)
- **Enum Values**: PascalCase (e.g., `JobStatus.InProgress`, `SiteStatus.Connected`)
