# Frontend Developer Standards

## Critical Coding Rules

**React & TypeScript Rules:**
1. **Always Use TypeScript**: Every component, hook, and service must have complete type definitions
2. **Functional Components Only**: No class components, use React.FC with proper prop interfaces
3. **Hook Rules**: Follow Rules of Hooks - only call hooks at the top level, never in loops or conditions
4. **Props Destructuring**: Always destructure props in component parameters for clarity
5. **Key Props**: Always provide unique key props for dynamic lists, never use array index
6. **Effect Dependencies**: Always include all dependencies in useEffect dependency arrays
7. **Error Boundaries**: Wrap route components in error boundaries for graceful error handling

**API & State Management Rules:**
1. **React Query First**: Use React Query for all API state, avoid useState for server data
2. **Optimistic Updates**: Implement optimistic updates for better UX on mutations
3. **Error Handling**: Always handle loading and error states in components
4. **Type Safety**: API responses must have complete TypeScript interfaces
5. **No Direct API Calls**: Always use the service layer, never axios directly in components

**Performance Rules:**
1. **Lazy Loading**: Use React.lazy for route-level code splitting
2. **Bundle Analysis**: Monitor bundle size, keep initial bundle under 500KB
3. **Memoize Expensive Calculations**: Use useMemo for complex computations
4. **Avoid Re-renders**: Minimize unnecessary re-renders with proper state structure

**Security & Best Practices:**
1. **Input Validation**: Validate all user inputs with Zod or similar validation library
2. **XSS Prevention**: Never use dangerouslySetInnerHTML without sanitization
3. **Authentication**: Check authentication status before API calls
4. **Environment Variables**: Use VITE_ prefix for all environment variables
5. **No Console Logs**: Remove console.log statements before production builds

## Quick Reference

**Common Commands:**
```bash