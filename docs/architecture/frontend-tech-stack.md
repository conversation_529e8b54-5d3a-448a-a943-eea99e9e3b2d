# Frontend Tech Stack

## Technology Stack Table

| Category | Technology | Version | Purpose | Rationale |
|----------|------------|---------|---------|-----------|
| Framework | React | ^19.1.1 | UI library and component system | Latest React version with improved performance and features |
| Build Tool | Vite | ^7.1.0 | Development server and bundling | Fast HMR, efficient bundling, excellent development experience |
| Language | TypeScript | ~5.8.3 | Type-safe JavaScript development | Static typing, better IDE support, catch errors at compile time |
| Styling | Tailwind CSS | ^4.1.11 | Utility-first CSS framework | Modern v4 with native CSS support, rapid styling, design consistency |
| UI Components | Custom shadcn/ui-style | Latest | Component library based on Radix UI | Radix UI primitives (@radix-ui/react-dialog, @radix-ui/react-slot) with custom implementations |
| Routing | React Router DOM | ^7.8.0 | Client-side routing and navigation | Latest version with improved features and performance |
| HTTP Client | Axios | ^1.11.0 | API communication and interceptors | Robust HTTP client with automatic token refresh and error handling |
| Form Handling | React Hook Form | ^7.62.0 | Form validation and management | Excellent performance, TypeScript support, minimal re-renders |
| Icon System | Lucide React | ^0.539.0 | Icon library | Comprehensive icon set with React components |
| Utility Libraries | clsx + tailwind-merge | Latest | CSS class management | Conditional classes and Tailwind conflict resolution |
| Testing Framework | Vitest | ^3.2.4 | Unit and integration testing | Fast test runner built for Vite, Jest-compatible API |
| Testing Library | React Testing Library | ^16.3.0 | Component testing utilities | User-centric testing approach, excellent React support |
| Testing Coverage | Vitest Coverage V8 | ^3.2.4 | Code coverage reporting | Built-in coverage reports with V8 engine |
| Code Quality | ESLint + Prettier | Latest | Linting and code formatting | Consistent code style and quality enforcement |
| Class Utilities | class-variance-authority (CVA) | ^0.7.1 | Component variant management | Type-safe component variants and styling |


