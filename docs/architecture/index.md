# Structura AI Frontend Architecture Document

## Table of Contents

- [Structura AI Frontend Architecture Document](#table-of-contents)
  - [Change Log](./change-log.md)
  - [Template and Framework Selection](./template-and-framework-selection.md)
    - [Framework Decision](./template-and-framework-selection.md#framework-decision)
  - [Frontend Tech Stack](./frontend-tech-stack.md)
    - [Technology Stack Table](./frontend-tech-stack.md#technology-stack-table)
  - [Project Structure](./project-structure.md)
  - [Component Standards](./component-standards.md)
    - [Component Template](./component-standards.md#component-template)
    - [Naming Conventions](./component-standards.md#naming-conventions)
  - [State Management](./state-management.md)
    - [Store Structure](./state-management.md#store-structure)
    - [State Management Template](./state-management.md#state-management-template)
  - [API Integration](./api-integration.md)
    - [Service Template](./api-integration.md#service-template)
    - [API Client Configuration](./api-integration.md#api-client-configuration)
  - [Routing](./routing.md)
    - [Route Configuration](./routing.md#route-configuration)
  - [Styling Guidelines](./styling-guidelines.md)
    - [Styling Approach](./styling-guidelines.md#styling-approach)
    - [Global Theme Variables](./styling-guidelines.md#global-theme-variables)
  - [Testing Requirements](./testing-requirements.md)
    - [Component Test Template](./testing-requirements.md#component-test-template)
    - [Testing Best Practices](./testing-requirements.md#testing-best-practices)
  - [Environment Configuration](./environment-configuration.md)
    - [Development Environment](./environment-configuration.md#development-environment)
    - [Production Environment](./environment-configuration.md#production-environment)
  - [Frontend Developer Standards](./frontend-developer-standards.md)
    - [Critical Coding Rules](./frontend-developer-standards.md#critical-coding-rules)
    - [Quick Reference](./frontend-developer-standards.md#quick-reference)
