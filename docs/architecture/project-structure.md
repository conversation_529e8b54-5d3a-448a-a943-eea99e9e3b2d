# Project Structure

Based on the chosen React TypeScript stack and PRD requirements, here's the exact directory structure for AI tools and developers:

```
/home/<USER>/projects/structura-ai/
├── src/structura_ai/          # Existing Python backend (unchanged)
│   ├── api/
│   ├── cli/
│   ├── services/
│   └── models/
├── frontend/                  # New React TypeScript frontend
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── src/
│   │   ├── components/        # Reusable UI components
│   │   │   ├── ui/           # shadcn/ui components
│   │   │   │   ├── button.tsx
│   │   │   │   ├── input.tsx
│   │   │   │   ├── dialog.tsx
│   │   │   │   └── index.ts
│   │   │   ├── layout/       # Layout components
│   │   │   │   ├── AppShell.tsx
│   │   │   │   ├── Sidebar.tsx
│   │   │   │   ├── Header.tsx
│   │   │   │   └── index.ts
│   │   │   ├── forms/        # Form components
│   │   │   │   ├── SiteForm.tsx
│   │   │   │   ├── ProfileForm.tsx
│   │   │   │   └── index.ts
│   │   │   └── common/       # Common components
│   │   │       ├── LoadingSpinner.tsx
│   │   │       ├── ErrorBoundary.tsx
│   │   │       └── index.ts
│   │   ├── pages/            # Page components (one per main route)
│   │   │   ├── auth/
│   │   │   │   ├── LoginPage.tsx
│   │   │   │   └── index.ts
│   │   │   ├── dashboard/
│   │   │   │   ├── DashboardPage.tsx
│   │   │   │   └── index.ts
│   │   │   ├── sites/
│   │   │   │   ├── SitesPage.tsx
│   │   │   │   ├── SiteDetailPage.tsx
│   │   │   │   └── index.ts
│   │   │   ├── profiles/
│   │   │   │   ├── ProfilesPage.tsx
│   │   │   │   ├── ProfileDetailPage.tsx
│   │   │   │   └── index.ts
│   │   │   ├── processing/
│   │   │   │   ├── ProcessingPage.tsx
│   │   │   │   ├── ResultsPage.tsx
│   │   │   │   └── index.ts
│   │   │   └── history/
│   │   │       ├── HistoryPage.tsx
│   │   │       └── index.ts
│   │   ├── services/         # API and business logic
│   │   │   ├── api/         # API client configuration
│   │   │   │   ├── client.ts
│   │   │   │   ├── auth.ts
│   │   │   │   ├── sites.ts
│   │   │   │   ├── profiles.ts
│   │   │   │   ├── processing.ts
│   │   │   │   └── index.ts
│   │   │   └── hooks/       # Custom React Query hooks
│   │   │       ├── useAuth.ts
│   │   │       ├── useSites.ts
│   │   │       ├── useProfiles.ts
│   │   │       ├── useProcessing.ts
│   │   │       └── index.ts
│   │   ├── types/           # TypeScript type definitions
│   │   │   ├── api.ts       # API response types
│   │   │   ├── auth.ts      # Authentication types
│   │   │   ├── sites.ts     # Site management types
│   │   │   ├── profiles.ts  # Profile types
│   │   │   ├── processing.ts # Processing types
│   │   │   └── index.ts
│   │   ├── utils/           # Utility functions
│   │   │   ├── constants.ts
│   │   │   ├── helpers.ts
│   │   │   ├── validation.ts
│   │   │   └── index.ts
│   │   ├── contexts/        # React contexts
│   │   │   ├── AuthContext.tsx
│   │   │   ├── ThemeContext.tsx
│   │   │   └── index.ts
│   │   ├── routes/          # Route configuration
│   │   │   ├── AppRoutes.tsx
│   │   │   ├── ProtectedRoute.tsx
│   │   │   └── index.ts
│   │   ├── styles/          # Global styles and themes
│   │   │   ├── globals.css
│   │   │   ├── components.css
│   │   │   └── variables.css
│   │   ├── App.tsx          # Main App component
│   │   ├── main.tsx         # Application entry point
│   │   └── vite-env.d.ts    # Vite type definitions
│   ├── package.json         # Frontend dependencies
│   ├── tsconfig.json        # TypeScript configuration
│   ├── tailwind.config.js   # Tailwind CSS configuration
│   ├── vite.config.ts       # Vite build configuration
│   ├── eslint.config.js     # ESLint configuration
│   └── components.json      # shadcn/ui configuration
├── docs/
│   ├── prd.md              # Existing PRD
│   └── architecture.md     # This document
└── README.md               # Updated with frontend instructions
```
