# Routing

## Route Configuration

```typescript
// routes/AppRoutes.tsx
import React from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { ProtectedRoute } from './ProtectedRoute'

// Lazy load page components for code splitting
const LoginPage = React.lazy(() => import('@/pages/auth/LoginPage'))
const DashboardPage = React.lazy(() => import('@/pages/dashboard/DashboardPage'))
const SitesPage = React.lazy(() => import('@/pages/sites/SitesPage'))
const SiteDetailPage = React.lazy(() => import('@/pages/sites/SiteDetailPage'))
const ProfilesPage = React.lazy(() => import('@/pages/profiles/ProfilesPage'))
const ProfileDetailPage = React.lazy(() => import('@/pages/profiles/ProfileDetailPage'))
const ProcessingPage = React.lazy(() => import('@/pages/processing/ProcessingPage'))
const ResultsPage = React.lazy(() => import('@/pages/processing/ResultsPage'))
const HistoryPage = React.lazy(() => import('@/pages/history/HistoryPage'))

export const AppRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Public routes */}
      <Route path="/login" element={<LoginPage />} />
      
      {/* Protected routes */}
      <Route path="/" element={<ProtectedRoute />}>
        <Route index element={<Navigate to="/dashboard" replace />} />
        <Route path="dashboard" element={<DashboardPage />} />
        
        {/* Site management routes */}
        <Route path="sites">
          <Route index element={<SitesPage />} />
          <Route path=":siteId" element={<SiteDetailPage />} />
        </Route>
        
        {/* Profile management routes */}
        <Route path="profiles">
          <Route index element={<ProfilesPage />} />
          <Route path=":profileId" element={<ProfileDetailPage />} />
        </Route>
        
        {/* Content processing routes */}
        <Route path="processing">
          <Route index element={<ProcessingPage />} />
          <Route path="results/:jobId" element={<ResultsPage />} />
        </Route>
        
        {/* Job history routes */}
        <Route path="history" element={<HistoryPage />} />
        
        {/* Future expansion routes can be added here */}
        
        {/* 404 fallback */}
        <Route path="*" element={<Navigate to="/dashboard" replace />} />
      </Route>
    </Routes>
  )
}
```
