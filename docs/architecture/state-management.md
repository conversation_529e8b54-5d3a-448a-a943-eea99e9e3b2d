# State Management

## Store Structure

```
frontend/src/
├── contexts/                    # React Context providers
│   ├── AuthContext.tsx         # Authentication state and methods
│   ├── ThemeContext.tsx        # UI theme and preferences
│   └── index.ts                # Context exports
├── services/hooks/             # React Query hooks
│   ├── useAuth.ts              # Authentication API hooks
│   ├── useSites.ts             # Site management API hooks  
│   ├── useProfiles.ts          # Profile management API hooks
│   ├── useProcessing.ts        # Content processing API hooks
│   └── index.ts                # Hook exports
├── services/api/               # API service layer
│   ├── client.ts               # Axios client configuration
│   ├── auth.ts                 # Authentication API calls
│   ├── sites.ts                # Site management API calls
│   ├── profiles.ts             # Profile management API calls
│   ├── processing.ts           # Processing API calls
│   └── index.ts                # API exports
└── providers/                  # Provider composition
    ├── AppProviders.tsx        # Combined provider wrapper
    └── QueryProvider.tsx       # React Query provider setup
```

## State Management Template

```typescript
// Context Example: AuthContext.tsx
import React, { createContext, useContext, useReducer } from 'react'
import type { User } from '@/types/auth'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  clearError: () => void
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

type AuthAction = 
  | { type: 'LOGIN_START' }
  | { type: 'LOGIN_SUCCESS'; payload: User }
  | { type: 'LOGIN_ERROR'; payload: string }
  | { type: 'LOGOUT' }
  | { type: 'CLEAR_ERROR' }

// React Query Hook Example: useSites.ts
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { siteApi } from '@/services/api'
import type { Site, CreateSiteRequest } from '@/types/sites'

export const useSites = () => {
  return useQuery({
    queryKey: ['sites'],
    queryFn: siteApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes,
  })
}

export const useCreateSite = () => {
  const queryClient = useQueryClient()
  
  return useMutation({
    mutationFn: (data: CreateSiteRequest) => siteApi.create(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sites'] })
    },
  })
}
```
