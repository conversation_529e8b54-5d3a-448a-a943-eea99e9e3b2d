# Styling Guidelines

## Styling Approach

**Primary Styling Method**: Tailwind CSS utility classes with shadcn/ui component library
- **Component Styling**: shadcn/ui components for consistency
- **Layout**: Tailwind utilities for responsive grid and flexbox layouts
- **Custom Components**: Tailwind utilities with CSS custom properties for themes
- **Responsive Design**: Mobile-first responsive patterns down to tablet (768px)

## Global Theme Variables

```css
/* styles/globals.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Color system - Professional neutral grays with clear status indicators */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* Status colors for operational confidence */
    --success: 142 76% 36%;
    --success-foreground: 210 40% 98%;
    
    --warning: 38 92% 50%;
    --warning-foreground: 222.2 84% 4.9%;
    
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    
    /* Typography scales optimized for data tables and technical content */
    --font-sans: 'Inter', system-ui, -apple-system, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
    
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
  }
}

@layer base {
  body {
    @apply bg-background text-foreground font-sans antialiased;
  }
  
  /* Status indicator styles */
  .status-connected {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }
  
  .status-failed {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
    @apply bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400;
  }
  
  .status-testing {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }
}
```
