# Template and Framework Selection

## Framework Decision

**Selected Stack**: React 18+ with TypeScript using Vite build tool

**Project Context Analysis:**
- **Backend**: Python FastAPI at `src/structura_ai/` on port 8003
- **Target**: React TypeScript frontend as specified in PRD
- **UI Library**: shadcn/ui explicitly requested in PRD  
- **Users**: 1-2 internal power users initially

**Framework Selection Rationale:**
The PRD clearly specifies React 18+ with TypeScript, and mentions Vite as the build tool. No existing frontend starter is mentioned, so we'll create a new React TypeScript application.

**Recommended Approach:**
- **Vite + React + TypeScript** starter template
- **shadcn/ui** for component library (as specified in PRD)
- **React Router DOM** for multi-page SPA architecture
- **React Query** for API state management (mentioned in PRD)

**Key Decision Trade-offs:**
- **Vite vs Create React App**: Vite chosen for faster builds and better development experience
- **React Query vs Redux**: React Query better for API-heavy application with existing backend
- **Monorepo vs Separate Repos**: Monorepo maintains current structure while enabling future refactoring

**Project Structure Decision:**
Maintain current monorepo structure with `/frontend` directory as specified in PRD, with future migration path to `/backend` + `/frontend` when coordination benefits justify it.
