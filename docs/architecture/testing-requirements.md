# Testing Requirements

## Component Test Template

```typescript
// __tests__/components/SiteForm.test.tsx
import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { SiteForm } from '@/components/forms/SiteForm'
import { siteApi } from '@/services/api'

// Mock API calls
jest.mock('@/services/api', () => ({
  siteApi: {
    create: jest.fn(),
    testConnection: jest.fn(),
  },
}))

// Test wrapper with providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  )
}

describe('SiteForm', () => {
  const mockOnSuccess = jest.fn()

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders create form correctly', () => {
    render(
      <TestWrapper>
        <SiteForm onSuccess={mockOnSuccess} />
      </TestWrapper>
    )

    expect(screen.getByLabelText(/site name/i)).toBeInTheDocument()
    expect(screen.getByLabelText(/host/i)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /create site/i })).toBeInTheDocument()
  })

  it('validates required fields', async () => {
    const user = userEvent.setup()
    
    render(
      <TestWrapper>
        <SiteForm onSuccess={mockOnSuccess} />
      </TestWrapper>
    )

    await user.click(screen.getByRole('button', { name: /create site/i }))
    expect(screen.getByText(/site name is required/i)).toBeInTheDocument()
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    const mockSite = { id: '1', name: 'Test Site', status: 'connected' }
    ;(siteApi.create as jest.Mock).mockResolvedValue(mockSite)

    render(
      <TestWrapper>
        <SiteForm onSuccess={mockOnSuccess} />
      </TestWrapper>
    )

    await user.type(screen.getByLabelText(/site name/i), 'Test Site')
    await user.type(screen.getByLabelText(/host/i), 'localhost')
    await user.click(screen.getByRole('button', { name: /create site/i }))

    await waitFor(() => {
      expect(mockOnSuccess).toHaveBeenCalledWith(mockSite)
    })
  })
})
```

## Testing Best Practices

1. **Unit Tests**: Test individual components in isolation
2. **Integration Tests**: Test component interactions with React Query and routing  
3. **E2E Tests**: Test critical user flows using Cypress/Playwright
4. **Coverage Goals**: Aim for 80% code coverage
5. **Test Structure**: Arrange-Act-Assert pattern
6. **Mock External Dependencies**: API calls, routing, state management
