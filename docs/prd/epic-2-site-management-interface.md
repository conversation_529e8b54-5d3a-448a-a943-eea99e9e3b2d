# Epic 2: Site Management Interface

**Expanded Goal**: Create a comprehensive web interface for WooCommerce site configuration that matches and extends the existing CLI functionality. This epic delivers full site connection management, testing capabilities, and status monitoring through an intuitive dashboard interface, enabling internal users to manage WooCommerce sites without CLI knowledge.

## Story 2.1: Site List and Status Dashboard

As a **internal user**,  
I want **to view all configured WooCommerce sites with their connection status**,  
so that **I can quickly assess which sites are available for processing operations**.

### Acceptance Criteria
1. Sites list page displays all configured sites in a clean table/card layout
2. Each site shows name, host, database name, and real-time connection status
3. Connection status indicators use clear visual design (green=connected, red=failed, amber=testing)
4. Sites list refreshes connection status automatically or with manual refresh button
5. Empty state displays helpful message when no sites are configured
6. Loading states shown during status checks with appropriate shadcn/ui components
7. Error handling displays user-friendly messages for API communication failures

## Story 2.2: Add New Site Configuration

As a **internal user**,  
I want **to add new WooCommerce sites through a guided form interface**,  
so that **I can configure database connections without using CLI commands**.

### Acceptance Criteria
1. "Add Site" button opens modal dialog with multi-step configuration form
2. Form includes fields for site name, host, database name, username, password
3. Form validation ensures all required fields are populated with appropriate formats
4. Password field uses secure input type with optional visibility toggle
5. Form submission creates new site configuration via API call
6. Success state closes modal and refreshes sites list showing new site
7. Error handling displays specific validation or API errors within the modal
8. Form supports cancellation with unsaved changes warning if applicable

## Story 2.3: Site Connection Testing and Management

As a **internal user**,  
I want **to test site connections and manage existing site configurations**,  
so that **I can ensure sites are properly configured before running processing operations**.

### Acceptance Criteria
1. "Test Connection" action available for each site triggers real-time connection verification
2. Connection test results display detailed feedback (success message or specific error details)
3. Edit site functionality opens pre-populated configuration modal for existing sites
4. Delete site functionality includes confirmation dialog to prevent accidental deletion
5. Connection test shows progress indicator during API call execution
6. Test results persist in UI until next test or page refresh
7. Bulk actions available for testing multiple sites simultaneously
8. Site management actions disabled appropriately based on current site status
