# Epic 5: Operations Dashboard & History

**Expanded Goal**: Develop essential operational visibility and audit capabilities optimized for 1-2 internal users. This epic delivers practical monitoring and historical analysis tools needed for confident production usage, focusing on high-value, low-complexity features that leverage existing backend infrastructure.

## Story 5.1: Basic System Overview

As a **internal user**,  
I want **a simple system overview showing current status and active operations**,  
so that **I can quickly assess system health and current activity**.

### Acceptance Criteria
1. System overview page displays overall system health with basic status indicators
2. Active processing jobs list shows currently running operations with basic progress info
3. Site connection overview displays health status of all configured WooCommerce sites
4. Manual refresh button updates all status information on demand
5. Basic error alerts displayed prominently when system issues are detected
6. Page loading states use consistent shadcn/ui feedback components
7. Recent activity summary shows last 10 completed jobs with status indicators

## Story 5.2: Job History and Audit Trail

As a **internal user**,  
I want **detailed history of all processing operations with searchable audit trail**,  
so that **I can track what processing was performed and troubleshoot any issues**.

### Acceptance Criteria
1. Job history table displays all processing operations with key metadata
2. History includes job ID, site, profile used, processing time, status, and timestamp
3. Detailed job view shows complete processing parameters and results summary
4. Search and filtering functionality leverages existing API filtering capabilities
5. Job history supports date range filtering and status-based filtering
6. Failed job details include error messages and troubleshooting information
7. Job history export functionality for external reporting or analysis
8. Pagination handles large job history volumes efficiently
9. Job comparison functionality allows comparing similar processing operations

## Story 5.3: Basic Usage Summary

As a **internal user**,  
I want **simple usage statistics and processing insights**,  
so that **I can understand basic usage patterns and processing performance**.

### Acceptance Criteria
1. Usage summary displays total jobs processed, success rates, and failure counts
2. Basic processing performance shows average completion times by profile type
3. Site usage statistics show processing activity distribution across WooCommerce sites
4. Date range selector enables viewing statistics for specific time periods
5. Simple export functionality allows downloading basic usage reports
6. Processing trend indicators show whether performance is improving or declining
7. Most used profiles and sites highlighted for operational awareness
8. Basic cost estimates displayed if OpenRouter usage data is available
