# Technical Assumptions

## Repository Structure: Monorepo (Current Structure Maintained)

Current structure with `src/structura_ai/` (Python backend) + new `/frontend/` directory. Frontend development works with existing backend structure, with optional future refactoring to `/backend` + `/frontend` when coordination benefits justify the migration effort.

## Service Architecture

**Multi-page React Application** with React Router for navigation between functional areas. Modular page structure that can accommodate future sections without major architectural changes. Direct API communication with existing FastAPI backend on port 8003.

## Testing Requirements

**Unit + Integration Testing**: Component-level testing using React Testing Library + Jest for UI components, plus integration testing for critical API communication flows. Focus on testing foundation that can grow with future complexity.

## Additional Technical Assumptions and Requests

- **Package Manager**: npm (standard) with potential future upgrade to pnpm workspaces if coordination needs increase
- **Build Tool**: Vite for fast development builds and efficient bundling that scales with additional pages/features
- **Router**: React Router DOM with clean route organization supporting future section additions
- **State Management**: React Context for authentication/global state + React Query for API state management (moderate future-proofing for API complexity)
- **Styling**: shadcn/ui with Tailwind CSS for consistent component library and styling system
- **API Client**: Axios with clean service layer organization that can extend for future endpoints
- **Development Environment**: Frontend dev server with proxy to existing backend (localhost:8003), development scripts for coordinated local development
- **Type Safety**: TypeScript with well-structured API type definitions that can grow incrementally
- **Code Quality**: ESLint + Prettier for consistency, configured for moderate project scale
- **Deployment**: Static build output for standard web server deployment, with backend running as separate service
