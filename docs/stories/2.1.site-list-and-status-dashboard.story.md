# Story 2.1: Site List and Status Dashboard

## Status
Approved

## Story
**As a** internal user,  
**I want** to view all configured WooCommerce sites with their connection status,  
**so that** I can quickly assess which sites are available for processing operations.

## Acceptance Criteria
1. Sites list page displays all configured sites in a clean table/card layout
2. Each site shows name, host, database name, and real-time connection status
3. Connection status indicators use clear visual design (green=connected, red=failed, amber=testing)
4. Sites list refreshes connection status automatically or with manual refresh button
5. Empty state displays helpful message when no sites are configured
6. Loading states shown during status checks with appropriate shadcn/ui components
7. Error handling displays user-friendly messages for API communication failures

## Tasks / Subtasks

- [ ] Task 1: Create Sites Page Component Structure (AC: 1, 5, 6)
  - [ ] Create `/frontend/src/pages/sites/SitesPage.tsx` component
  - [ ] Implement responsive layout with table/card view toggle capability
  - [ ] Add page header with title and refresh controls
  - [ ] Create empty state component for when no sites are configured
  - [ ] Implement loading states using shadcn/ui Skeleton components
  - [ ] Add TypeScript interfaces for component props and state

- [ ] Task 2: Implement Sites API Service Integration (AC: 2, 4, 7)
  - [ ] Create `/frontend/src/services/api/sites.ts` API service
  - [ ] Implement `getAll()`, `testConnection()` methods
  - [ ] Add proper TypeScript typing for API responses
  - [ ] Integrate with existing apiClient for authentication and error handling
  - [ ] Add request/response validation with proper error mapping

- [ ] Task 3: Build Sites Data Table Component (AC: 1, 2, 3)
  - [ ] Create reusable SitesTable component in `/frontend/src/components/common/`
  - [ ] Display site name, host, database name, connection status columns
  - [ ] Implement status indicators with proper CSS classes from styling guidelines
  - [ ] Add responsive design for tablet view with horizontal scrolling
  - [ ] Include action buttons column for future operations (test, edit, delete)

- [ ] Task 4: Implement Connection Status Management (AC: 3, 4)
  - [ ] Create connection status state management with React hooks
  - [ ] Implement auto-refresh functionality with proper intervals
  - [ ] Add manual refresh button with loading state feedback
  - [ ] Handle concurrent connection tests with proper state management
  - [ ] Implement status color coding: green=connected, red=failed, amber=testing

- [ ] Task 5: Add Error Handling and User Feedback (AC: 7)
  - [ ] Implement API error handling with user-friendly messages
  - [ ] Add toast notifications for connection test results
  - [ ] Handle network failures and timeout scenarios
  - [ ] Add error boundaries for component-level error handling
  - [ ] Implement retry mechanisms for failed API calls

- [ ] Task 6: Unit Testing for Sites Page and Components (From Testing Requirements)
  - [ ] Create tests for SitesPage component rendering and state management
  - [ ] Test SitesTable component with various site data scenarios
  - [ ] Test connection status management and refresh functionality
  - [ ] Test error handling and empty state rendering
  - [ ] Test API service integration with mock responses
  - [ ] Ensure 80% code coverage target is met

## Dev Notes

### Previous Story Insights
From Story 1.3 completion:
- AppShell layout system fully implemented with responsive sidebar navigation
- React Router DOM v6 configured with lazy loading for all page components
- Existing page components created as placeholders - SitesPage.tsx ready for enhancement
- Authentication system integrated with JWT tokens and ProtectedRoute wrapper
- shadcn/ui components and Tailwind CSS styling system fully configured and working

### Sites API Integration [Source: api/routers/sites.py]
- **Base URL**: `http://localhost:8003/api`
- **Endpoints Available**:
  - `GET /api/sites/` - List all sites with pagination support
  - `POST /api/sites/{site_id}/test` - Test site connection
  - `GET /api/sites/{site_id}/stats` - Get site statistics (for future use)
- **Authentication**: Bearer token via Authorization header (handled by apiClient)
- **Response Format**: JSON with SiteResponse schema structure
- **Error Handling**: Structured error responses with status codes and messages

### Site Data Structure [Source: api/schemas/sites.py]
```typescript
interface Site {
  id: number
  name: string              // Unique site identifier
  display_name: string      // Human-readable site name  
  description?: string      // Optional site description
  db_host: string          // Database host
  db_port: number          // Database port (default: 3306)
  db_name: string          // Database name
  db_user: string          // Database username
  db_password: string      // Database password (sensitive)
  db_table_prefix: string  // Database table prefix (default: "wp_")
  created_at: string       // ISO timestamp
  updated_at?: string      // ISO timestamp
}

interface SiteConnectionTestResult {
  site_id: number
  status: "connected" | "error" | "testing"
  error?: string
  error_type?: string
  tested_at?: string
}
```

### Component Architecture [Source: architecture/project-structure.md]
- **Page Location**: `/frontend/src/pages/sites/SitesPage.tsx`
- **API Service**: `/frontend/src/services/api/sites.ts`
- **Common Components**: Use `/frontend/src/components/common/` for reusable table components
- **Types**: Create `/frontend/src/types/sites.ts` for TypeScript interfaces
- **API Integration**: Use existing `/frontend/src/services/api/client.ts` axiOS instance

### React Patterns and Hooks [Source: architecture/component-standards.md]
- **Component Structure**: React.FC with destructured props and displayName
- **Props Interface**: `SitesPageProps`, `SitesTableProps` pattern for component props
- **State Management**: useState for local component state, useCallback for event handlers
- **API Integration**: Custom hooks pattern with React Query or direct fetch with error handling
- **TypeScript**: Proper interfaces for all props, API responses, and component state

### Styling and Layout [Source: architecture/styling-guidelines.md]
- **Primary Method**: Tailwind CSS utility classes with shadcn/ui components
- **Layout**: Responsive table design with card view fallback for mobile
- **Status Colors**: Use predefined CSS classes from globals.css:
  - `.status-connected` - Green background with dark green text
  - `.status-failed` - Red background with dark red text  
  - `.status-testing` - Amber/yellow background with dark text
- **Table Styling**: Use shadcn/ui Table components with proper responsive behavior

### Technology Stack Requirements [Source: architecture/frontend-tech-stack.md]
- **HTTP Client**: Axios ^1.11.0 with existing apiClient configuration
- **UI Components**: shadcn/ui components (Button, Table, Skeleton, Alert)
- **Icons**: Lucide React ^0.539.0 for status and action icons
- **Form Handling**: React Hook Form ^7.62.0 (for future forms)
- **Utility**: clsx + tailwind-merge for className management

### Authentication Integration [Source: previous story insights]
- **User Context**: Access user data via useAuth() hook from AuthContext
- **API Authentication**: Handled automatically by apiClient interceptor
- **Protected Route**: Page already wrapped in ProtectedRoute component
- **Error Handling**: 401 errors automatically trigger token refresh or redirect to login

### Responsive Design Requirements [Source: architecture/styling-guidelines.md]
- **Desktop**: Full table view with all columns visible
- **Tablet (768px)**: Horizontal scrolling table or card view toggle
- **Mobile**: Card-based layout with stacked information
- **Grid System**: Use CSS Grid or Flexbox for main layout structure
- **Breakpoint**: 768px (md: in Tailwind) for tablet responsive behavior

### Loading States and Performance [Source: architecture/frontend-tech-stack.md]
- **Loading Components**: shadcn/ui Skeleton components for table rows
- **API Loading**: Show loading spinner during initial load and refresh
- **Connection Testing**: Individual loading states per site during connection tests
- **Error Boundaries**: React error boundaries for graceful error handling
- **Performance**: Implement debouncing for auto-refresh to prevent excessive API calls

### File Locations [Source: architecture/project-structure.md]
- **Main Page**: `/frontend/src/pages/sites/SitesPage.tsx`
- **API Service**: `/frontend/src/services/api/sites.ts`
- **Types**: `/frontend/src/types/sites.ts`  
- **Common Components**: `/frontend/src/components/common/SitesTable.tsx`
- **Tests**: `__tests__/` directories alongside each component
- **Page Index**: Update `/frontend/src/pages/sites/index.ts` for exports

## Testing

### Component Testing Strategy [Source: architecture/testing-requirements.md]
- **Framework**: Vitest ^3.2.4 with React Testing Library ^16.3.0
- **Test Wrapper**: QueryClientProvider and BrowserRouter wrapper required
- **Coverage Target**: 80% code coverage with comprehensive testing
- **Test Structure**: Arrange-Act-Assert pattern for test organization

### Testing Requirements for This Story
- SitesPage component rendering with different data states (loading, empty, error, success)
- SitesTable component with various site configurations and status combinations
- API service integration testing with mock responses and error scenarios
- Connection status management and refresh functionality testing
- Error handling and user feedback testing with toast notifications
- Responsive design testing at different viewport sizes

### Test File Locations [Source: architecture/testing-requirements.md]
- `/frontend/src/pages/sites/__tests__/SitesPage.test.tsx`
- `/frontend/src/components/common/__tests__/SitesTable.test.tsx`
- `/frontend/src/services/api/__tests__/sites.test.tsx`

## Change Log

| Date | Version | Description | Author |
|------|---------|-------------|---------|
| 2025-08-10 | 1.0 | Initial story creation with comprehensive site dashboard implementation plan | Bob (Scrum Master) |