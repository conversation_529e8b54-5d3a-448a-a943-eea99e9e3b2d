{"/home/<USER>/projects/structura-ai/frontend/src/App.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/App.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 96}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 29}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 45}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 10}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 85}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 40}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 44}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 75}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 27}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 28}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 21}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 43}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 39}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 62}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 16}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 20}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 60}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 32}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 13}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 30}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 21}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 16}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 69}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 44}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 43}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 36}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 16}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 22}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 13}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 10}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 1}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 18}}}, "s": {"0": 1, "1": 1, "2": 1, "4": 1, "5": 6, "7": 6, "8": 6, "9": 6, "10": 6, "11": 6, "12": 6, "14": 6, "15": 6, "16": 6, "17": 6, "18": 6, "20": 6, "21": 6, "22": 6, "23": 6, "24": 6, "25": 6, "26": 6, "27": 6, "28": 6, "29": 6, "30": 6, "31": 6, "33": 6, "34": 6, "35": 6, "36": 6, "38": 6, "40": 1}, "branchMap": {"0": {"type": "branch", "line": 5, "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 39, "column": 1}}, "locations": [{"start": {"line": 5, "column": 22}, "end": {"line": 39, "column": 1}}]}, "1": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 60}}, "locations": [{"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 60}}]}, "2": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 38}, "end": {"line": 23, "column": 58}}, "locations": [{"start": {"line": 23, "column": 38}, "end": {"line": 23, "column": 58}}]}}, "b": {"0": [6], "1": [2], "2": [2]}, "fnMap": {"0": {"name": "App", "decl": {"start": {"line": 5, "column": 22}, "end": {"line": 39, "column": 1}}, "loc": {"start": {"line": 5, "column": 22}, "end": {"line": 39, "column": 1}}, "line": 5}, "1": {"name": "onClick", "decl": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 60}}, "loc": {"start": {"line": 23, "column": 23}, "end": {"line": 23, "column": 60}}, "line": 23}}, "f": {"0": 6, "1": 2}}, "/home/<USER>/projects/structura-ai/frontend/src/main.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/main.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 34}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 27}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 65}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 52}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 14}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 19}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 20}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 1}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 300}, "end": {"line": 13, "column": -236}}, "locations": [{"start": {"line": 1, "column": 300}, "end": {"line": 13, "column": -236}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 300}, "end": {"line": 13, "column": -236}}, "loc": {"start": {"line": 1, "column": 300}, "end": {"line": 13, "column": -236}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/components/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 20}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 24}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 23}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 24}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 94}, "end": {"line": 4, "column": 24}}, "locations": [{"start": {"line": 1, "column": 94}, "end": {"line": 4, "column": 24}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 94}, "end": {"line": 4, "column": 24}}, "loc": {"start": {"line": 1, "column": 94}, "end": {"line": 4, "column": 24}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/components/common/ErrorBoundary.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/common/ErrorBoundary.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 33}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 96}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 47}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 60}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 25}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 20}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 3}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 63}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 36}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 3}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 64}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 54}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 3}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 32}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 28}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 3}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 19}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 30}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 14}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 89}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 44}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 48}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 63}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 26}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 31}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 32}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 25}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 47}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 61}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 47}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 72}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 46}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 22}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 20}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 69}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 23}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 26}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 17}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 14}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 5}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 30}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 3}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 1}}}, "s": {"0": 1, "2": 1, "3": 1, "14": 1, "15": 9, "16": 9, "17": 9, "19": 9, "20": 0, "21": 0, "23": 9, "24": 0, "25": 0, "27": 9, "28": 0, "29": 0, "31": 9, "32": 9, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "39": 0, "40": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "51": 0, "52": 0, "54": 0, "55": 0, "56": 0, "57": 0, "59": 0, "61": 9, "62": 9, "63": 9}, "branchMap": {"0": {"type": "branch", "line": 15, "loc": {"start": {"line": 15, "column": 59}, "end": {"line": 64, "column": 1}}, "locations": [{"start": {"line": 15, "column": 59}, "end": {"line": 64, "column": 1}}]}, "1": {"type": "branch", "line": 32, "loc": {"start": {"line": 32, "column": 9}, "end": {"line": 63, "column": 3}}, "locations": [{"start": {"line": 32, "column": 9}, "end": {"line": 63, "column": 3}}]}, "2": {"type": "branch", "line": 33, "loc": {"start": {"line": 33, "column": 29}, "end": {"line": 60, "column": 5}}, "locations": [{"start": {"line": 33, "column": 29}, "end": {"line": 60, "column": 5}}]}}, "b": {"0": [9], "1": [9], "2": [0]}, "fnMap": {"0": {"name": "<instance_members_initializer>", "decl": {"start": {"line": 15, "column": 59}, "end": {"line": 64, "column": 1}}, "loc": {"start": {"line": 15, "column": 59}, "end": {"line": 64, "column": 1}}, "line": 15}, "1": {"name": "getDerivedStateFromError", "decl": {"start": {"line": 20, "column": 16}, "end": {"line": 22, "column": 3}}, "loc": {"start": {"line": 20, "column": 16}, "end": {"line": 22, "column": 3}}, "line": 20}, "2": {"name": "componentDidCatch", "decl": {"start": {"line": 24, "column": 9}, "end": {"line": 26, "column": 3}}, "loc": {"start": {"line": 24, "column": 9}, "end": {"line": 26, "column": 3}}, "line": 24}, "3": {"name": "handleReload", "decl": {"start": {"line": 28, "column": 25}, "end": {"line": 30, "column": 3}}, "loc": {"start": {"line": 28, "column": 25}, "end": {"line": 30, "column": 3}}, "line": 28}, "4": {"name": "render", "decl": {"start": {"line": 32, "column": 9}, "end": {"line": 63, "column": 3}}, "loc": {"start": {"line": 32, "column": 9}, "end": {"line": 63, "column": 3}}, "line": 32}}, "f": {"0": 9, "1": 0, "2": 0, "3": 0, "4": 9}}, "/home/<USER>/projects/structura-ai/frontend/src/components/common/LoadingSpinner.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/common/LoadingSpinner.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 25}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 32}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 63}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 12}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 14}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 7}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 23}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 18}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 18}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 20}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 3}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 10}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 8}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 19}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 26}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 20}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 79}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 26}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 17}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 8}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 6}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 1}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 45}}}, "s": {"0": 1, "1": 1, "8": 1, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5, "18": 5, "19": 5, "20": 5, "21": 5, "22": 5, "23": 5, "24": 5, "25": 5, "26": 5, "27": 5, "29": 5, "31": 1}, "branchMap": {"0": {"type": "branch", "line": 9, "loc": {"start": {"line": 9, "column": 61}, "end": {"line": 30, "column": 1}}, "locations": [{"start": {"line": 9, "column": 61}, "end": {"line": 30, "column": 1}}]}}, "b": {"0": [5]}, "fnMap": {"0": {"name": "LoadingSpinner", "decl": {"start": {"line": 9, "column": 61}, "end": {"line": 30, "column": 1}}, "loc": {"start": {"line": 9, "column": 61}, "end": {"line": 30, "column": 1}}, "line": 9}}, "f": {"0": 5}}, "/home/<USER>/projects/structura-ai/frontend/src/components/common/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/common/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}}, "s": {"0": 0, "1": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 97}, "end": {"line": 2, "column": 47}}, "locations": [{"start": {"line": 1, "column": 97}, "end": {"line": 2, "column": 47}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 97}, "end": {"line": 2, "column": 47}}, "loc": {"start": {"line": 1, "column": 97}, "end": {"line": 2, "column": 47}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/components/forms/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/forms/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 128}, "end": {"line": 3, "column": 46}}, "locations": [{"start": {"line": 1, "column": 128}, "end": {"line": 3, "column": 46}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 128}, "end": {"line": 3, "column": 46}}, "loc": {"start": {"line": 1, "column": 128}, "end": {"line": 3, "column": 46}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/components/layout/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/layout/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 160}, "end": {"line": 4, "column": 36}}, "locations": [{"start": {"line": 1, "column": 160}, "end": {"line": 4, "column": 36}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 160}, "end": {"line": 4, "column": 36}}, "loc": {"start": {"line": 1, "column": 160}, "end": {"line": 4, "column": 36}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/components/ui/button.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/ui/button.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 43}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 65}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 32}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 27}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 326}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 3}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 15}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 16}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 74}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 20}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 79}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 16}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 91}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 18}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 73}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 62}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 64}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 8}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 13}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 34}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 34}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 35}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 26}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 8}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 6}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 22}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 25}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 22}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 6}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 3}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 1}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 64}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 69}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 42}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 12}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 11}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 68}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 17}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 18}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 8}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 3}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 1}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 29}}}, "s": {"0": 1, "1": 1, "2": 1, "4": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1, "25": 1, "26": 1, "27": 1, "28": 1, "29": 1, "30": 1, "31": 1, "32": 1, "33": 1, "41": 1, "42": 1, "43": 6, "44": 6, "45": 6, "46": 6, "47": 6, "48": 6, "49": 6, "51": 6, "52": 1, "53": 1}, "branchMap": {"0": {"type": "branch", "line": 43, "loc": {"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 3}}, "locations": [{"start": {"line": 43, "column": 2}, "end": {"line": 52, "column": 3}}]}, "1": {"type": "branch", "line": 44, "loc": {"start": {"line": 44, "column": 17}, "end": {"line": 44, "column": 34}}, "locations": [{"start": {"line": 44, "column": 17}, "end": {"line": 44, "column": 34}}]}}, "b": {"0": [6], "1": [0]}, "fnMap": {}, "f": {}}, "/home/<USER>/projects/structura-ai/frontend/src/components/ui/card.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/ui/card.tsx", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 30}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 37}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 6}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 13}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 18}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 65}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 15}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 6}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 14}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 4}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 2}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 36}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 37}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 89}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 2}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 37}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 35}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 37}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 5}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 13}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 18}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 59}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 15}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 6}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 14}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 4}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 2}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 35}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 41}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 37}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 87}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 2}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 47}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 37}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 37}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 68}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 2}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 39}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 36}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 37}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 86}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 2}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 37}}}, "s": {"0": 1, "2": 1, "4": 1, "7": 1, "8": 6, "9": 6, "10": 6, "11": 6, "12": 6, "13": 6, "14": 6, "15": 6, "16": 6, "17": 1, "19": 1, "22": 1, "23": 6, "24": 1, "25": 1, "27": 1, "30": 1, "31": 6, "32": 6, "33": 6, "34": 6, "35": 6, "36": 6, "37": 6, "38": 6, "39": 6, "40": 1, "42": 1, "45": 1, "46": 6, "47": 1, "48": 1, "50": 1, "53": 1, "54": 6, "55": 1, "56": 1, "58": 1, "61": 1, "62": 0, "63": 1, "64": 1}, "branchMap": {"0": {"type": "branch", "line": 8, "loc": {"start": {"line": 8, "column": 2}, "end": {"line": 17, "column": 2}}, "locations": [{"start": {"line": 8, "column": 2}, "end": {"line": 17, "column": 2}}]}, "1": {"type": "branch", "line": 23, "loc": {"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 89}}, "locations": [{"start": {"line": 23, "column": 2}, "end": {"line": 24, "column": 89}}]}, "2": {"type": "branch", "line": 31, "loc": {"start": {"line": 31, "column": 2}, "end": {"line": 40, "column": 2}}, "locations": [{"start": {"line": 31, "column": 2}, "end": {"line": 40, "column": 2}}]}, "3": {"type": "branch", "line": 46, "loc": {"start": {"line": 46, "column": 2}, "end": {"line": 47, "column": 87}}, "locations": [{"start": {"line": 46, "column": 2}, "end": {"line": 47, "column": 87}}]}, "4": {"type": "branch", "line": 54, "loc": {"start": {"line": 54, "column": 2}, "end": {"line": 55, "column": 68}}, "locations": [{"start": {"line": 54, "column": 2}, "end": {"line": 55, "column": 68}}]}}, "b": {"0": [6], "1": [6], "2": [6], "3": [6], "4": [6]}, "fnMap": {}, "f": {}}, "/home/<USER>/projects/structura-ai/frontend/src/components/ui/dialog.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/ui/dialog.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 57}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 32}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 35}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 45}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 43}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 41}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 39}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 37}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 26}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 13}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 18}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 160}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 15}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 6}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 14}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 4}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 2}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 63}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 39}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 47}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 16}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 21}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 28}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 15}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 20}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 518}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 17}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 8}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 16}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 16}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 311}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 33}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 46}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 30}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 30}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 17}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 2}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 63}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 23}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 12}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 10}}, "56": {"start": {"line": 57, "column": 0}, "end": {"line": 57, "column": 45}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 6}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 18}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 59}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 15}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 6}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 14}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 4}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 41}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 23}}, "68": {"start": {"line": 69, "column": 0}, "end": {"line": 69, "column": 12}}, "69": {"start": {"line": 70, "column": 0}, "end": {"line": 70, "column": 10}}, "70": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 45}}, "71": {"start": {"line": 72, "column": 0}, "end": {"line": 72, "column": 6}}, "72": {"start": {"line": 73, "column": 0}, "end": {"line": 73, "column": 18}}, "73": {"start": {"line": 74, "column": 0}, "end": {"line": 74, "column": 70}}, "74": {"start": {"line": 75, "column": 0}, "end": {"line": 75, "column": 15}}, "75": {"start": {"line": 76, "column": 0}, "end": {"line": 76, "column": 6}}, "76": {"start": {"line": 77, "column": 0}, "end": {"line": 77, "column": 14}}, "77": {"start": {"line": 78, "column": 0}, "end": {"line": 78, "column": 4}}, "79": {"start": {"line": 80, "column": 0}, "end": {"line": 80, "column": 41}}, "81": {"start": {"line": 82, "column": 0}, "end": {"line": 82, "column": 37}}, "84": {"start": {"line": 85, "column": 0}, "end": {"line": 85, "column": 37}}, "85": {"start": {"line": 86, "column": 0}, "end": {"line": 86, "column": 24}}, "86": {"start": {"line": 87, "column": 0}, "end": {"line": 87, "column": 13}}, "87": {"start": {"line": 88, "column": 0}, "end": {"line": 88, "column": 18}}, "88": {"start": {"line": 89, "column": 0}, "end": {"line": 89, "column": 58}}, "89": {"start": {"line": 90, "column": 0}, "end": {"line": 90, "column": 15}}, "90": {"start": {"line": 91, "column": 0}, "end": {"line": 91, "column": 6}}, "91": {"start": {"line": 92, "column": 0}, "end": {"line": 92, "column": 14}}, "92": {"start": {"line": 93, "column": 0}, "end": {"line": 93, "column": 4}}, "93": {"start": {"line": 94, "column": 0}, "end": {"line": 94, "column": 2}}, "94": {"start": {"line": 95, "column": 0}, "end": {"line": 95, "column": 59}}, "96": {"start": {"line": 97, "column": 0}, "end": {"line": 97, "column": 43}}, "99": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 37}}, "100": {"start": {"line": 101, "column": 0}, "end": {"line": 101, "column": 30}}, "101": {"start": {"line": 102, "column": 0}, "end": {"line": 102, "column": 13}}, "102": {"start": {"line": 103, "column": 0}, "end": {"line": 103, "column": 62}}, "103": {"start": {"line": 104, "column": 0}, "end": {"line": 104, "column": 14}}, "104": {"start": {"line": 105, "column": 0}, "end": {"line": 105, "column": 4}}, "105": {"start": {"line": 106, "column": 0}, "end": {"line": 106, "column": 2}}, "106": {"start": {"line": 107, "column": 0}, "end": {"line": 107, "column": 71}}}, "s": {"0": 0, "1": 0, "2": 0, "4": 0, "6": 0, "8": 0, "10": 0, "12": 0, "14": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "29": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "65": 0, "67": 0, "68": 0, "69": 0, "70": 0, "71": 0, "72": 0, "73": 0, "74": 0, "75": 0, "76": 0, "77": 0, "79": 0, "81": 0, "84": 0, "85": 0, "86": 0, "87": 0, "88": 0, "89": 0, "90": 0, "91": 0, "92": 0, "93": 0, "94": 0, "96": 0, "99": 0, "100": 0, "101": 0, "102": 0, "103": 0, "104": 0, "105": 0, "106": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 3834}, "end": {"line": 120, "column": 1}}, "locations": [{"start": {"line": 1, "column": 3834}, "end": {"line": 120, "column": 1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 3834}, "end": {"line": 120, "column": 1}}, "loc": {"start": {"line": 1, "column": 3834}, "end": {"line": 120, "column": 1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/components/ui/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/ui/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 31}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 8}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 94}}}, "s": {"0": 0, "1": 0, "2": 0, "14": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 363}, "end": {"line": 15, "column": 94}}, "locations": [{"start": {"line": 1, "column": 363}, "end": {"line": 15, "column": 94}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 363}, "end": {"line": 15, "column": 94}}, "loc": {"start": {"line": 1, "column": 363}, "end": {"line": 15, "column": 94}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/components/ui/input.tsx": {"path": "/home/<USER>/projects/structura-ai/frontend/src/components/ui/input.tsx", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 30}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 32}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 61}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 43}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 12}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 12}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 19}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 22}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 395}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 19}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 10}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 17}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 18}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 8}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 3}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 1}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 27}}}, "s": {"0": 0, "2": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "19": 0, "20": 0, "21": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 841}, "end": {"line": 24, "column": 16}}, "locations": [{"start": {"line": 1, "column": 841}, "end": {"line": 24, "column": 16}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 841}, "end": {"line": 24, "column": 16}}, "loc": {"start": {"line": 1, "column": 841}, "end": {"line": 24, "column": 16}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/contexts/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/contexts/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 106}, "end": {"line": 3, "column": 33}}, "locations": [{"start": {"line": 1, "column": 106}, "end": {"line": 3, "column": 33}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 106}, "end": {"line": 3, "column": 33}}, "loc": {"start": {"line": 1, "column": 106}, "end": {"line": 3, "column": 33}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/lib/utils.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/lib/utils.ts", "all": false, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 45}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 30}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 1}}}, "s": {"0": 1, "1": 1, "3": 1, "4": 41, "5": 41}, "branchMap": {"0": {"type": "branch", "line": 4, "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 6, "column": 1}}, "locations": [{"start": {"line": 4, "column": 7}, "end": {"line": 6, "column": 1}}]}}, "b": {"0": [41]}, "fnMap": {"0": {"name": "cn", "decl": {"start": {"line": 4, "column": 7}, "end": {"line": 6, "column": 1}}, "loc": {"start": {"line": 4, "column": 7}, "end": {"line": 6, "column": 1}}, "line": 4}}, "f": {"0": 41}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 22}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 27}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 23}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 26}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 28}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 25}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 6, "column": -81}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 6, "column": -81}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 6, "column": -81}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 6, "column": -81}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/auth/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/auth/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 78}, "end": {"line": 2, "column": 42}}, "locations": [{"start": {"line": 1, "column": 78}, "end": {"line": 2, "column": 42}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 78}, "end": {"line": 2, "column": 42}}, "loc": {"start": {"line": 1, "column": 78}, "end": {"line": 2, "column": 42}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/dashboard/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/dashboard/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 91}, "end": {"line": 2, "column": 50}}, "locations": [{"start": {"line": 1, "column": 91}, "end": {"line": 2, "column": 50}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 91}, "end": {"line": 2, "column": 50}}, "loc": {"start": {"line": 1, "column": 91}, "end": {"line": 2, "column": 50}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/history/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/history/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 85}, "end": {"line": 2, "column": 46}}, "locations": [{"start": {"line": 1, "column": 85}, "end": {"line": 2, "column": 46}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 85}, "end": {"line": 2, "column": 46}}, "loc": {"start": {"line": 1, "column": 85}, "end": {"line": 2, "column": 46}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/processing/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/processing/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 141}, "end": {"line": 3, "column": 46}}, "locations": [{"start": {"line": 1, "column": 141}, "end": {"line": 3, "column": 46}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 141}, "end": {"line": 3, "column": 46}}, "loc": {"start": {"line": 1, "column": 141}, "end": {"line": 3, "column": 46}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/profiles/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/profiles/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 147}, "end": {"line": 3, "column": 58}}, "locations": [{"start": {"line": 1, "column": 147}, "end": {"line": 3, "column": 58}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 147}, "end": {"line": 3, "column": 58}}, "loc": {"start": {"line": 1, "column": 147}, "end": {"line": 3, "column": 58}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/pages/sites/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/pages/sites/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 132}, "end": {"line": 3, "column": 52}}, "locations": [{"start": {"line": 1, "column": 132}, "end": {"line": 3, "column": 52}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 132}, "end": {"line": 3, "column": 52}}, "loc": {"start": {"line": 1, "column": 132}, "end": {"line": 3, "column": 52}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/routes/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/routes/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 137}, "end": {"line": 3, "column": 52}}, "locations": [{"start": {"line": 1, "column": 137}, "end": {"line": 3, "column": 52}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 137}, "end": {"line": 3, "column": 52}}, "loc": {"start": {"line": 1, "column": 137}, "end": {"line": 3, "column": 52}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/services/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/services/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 21}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 23}}}, "s": {"0": 0, "1": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 2, "column": -1}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/services/api/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/services/api/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 189}, "end": {"line": 6, "column": 31}}, "locations": [{"start": {"line": 1, "column": 189}, "end": {"line": 6, "column": 31}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 189}, "end": {"line": 6, "column": 31}}, "loc": {"start": {"line": 1, "column": 189}, "end": {"line": 6, "column": 31}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/services/hooks/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/services/hooks/index.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 169}, "end": {"line": 5, "column": 34}}, "locations": [{"start": {"line": 1, "column": 169}, "end": {"line": 5, "column": 34}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 169}, "end": {"line": 5, "column": 34}}, "loc": {"start": {"line": 1, "column": 169}, "end": {"line": 5, "column": 34}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/types/api.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/api.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 397}, "end": {"line": 25, "column": 1}}, "locations": [{"start": {"line": 1, "column": 397}, "end": {"line": 25, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 397}, "end": {"line": 25, "column": 1}}, "loc": {"start": {"line": 1, "column": 397}, "end": {"line": 25, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/types/auth.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/auth.ts", "all": true, "statementMap": {}, "s": {}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 437}, "end": {"line": 27, "column": 1}}, "locations": [{"start": {"line": 1, "column": 437}, "end": {"line": 27, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 437}, "end": {"line": 27, "column": 1}}, "loc": {"start": {"line": 1, "column": 437}, "end": {"line": 27, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/types/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 21}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 22}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 23}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 26}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 28}}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 124}, "end": {"line": 5, "column": 28}}, "locations": [{"start": {"line": 1, "column": 124}, "end": {"line": 5, "column": 28}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 124}, "end": {"line": 5, "column": 28}}, "loc": {"start": {"line": 1, "column": 124}, "end": {"line": 5, "column": 28}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/types/processing.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/processing.ts", "all": true, "statementMap": {"14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 26}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 21}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 28}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 25}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 19}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 24}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 10}}}, "s": {"14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1175}, "end": {"line": 59, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1175}, "end": {"line": 59, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1175}, "end": {"line": 59, "column": 1}}, "loc": {"start": {"line": 1, "column": 1175}, "end": {"line": 59, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/types/profiles.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/profiles.ts", "all": true, "statementMap": {"31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 29}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 16}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 30}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 22}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 21}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 10}}}, "s": {"31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 769}, "end": {"line": 39, "column": 73}}, "locations": [{"start": {"line": 1, "column": 769}, "end": {"line": 39, "column": 73}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 769}, "end": {"line": 39, "column": 73}}, "loc": {"start": {"line": 1, "column": 769}, "end": {"line": 39, "column": 73}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/types/sites.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/types/sites.ts", "all": true, "statementMap": {"13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 27}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 25}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 31}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 21}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 16}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 10}}}, "s": {"13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 783}, "end": {"line": 43, "column": 1}}, "locations": [{"start": {"line": 1, "column": 783}, "end": {"line": 43, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 783}, "end": {"line": 43, "column": 1}}, "loc": {"start": {"line": 1, "column": 783}, "end": {"line": 43, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/utils/constants.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/utils/constants.ts", "all": true, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 34}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 38}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 34}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 30}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 9}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 25}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 27}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 29}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 29}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 4}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 10}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 19}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 21}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 40}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 43}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 43}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 46}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 4}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 13}}, "24": {"start": {"line": 25, "column": 0}, "end": {"line": 25, "column": 22}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 24}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 43}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 46}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 46}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 4}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 15}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 35}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 27}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 61}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 63}}, "35": {"start": {"line": 36, "column": 0}, "end": {"line": 36, "column": 4}}, "36": {"start": {"line": 37, "column": 0}, "end": {"line": 37, "column": 11}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 29}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 29}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 4}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 10}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 36}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 29}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 37}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 43}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 49}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 10}}}, "s": {"1": 0, "4": 0, "5": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "43": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1342}, "end": {"line": 51, "column": 10}}, "locations": [{"start": {"line": 1, "column": 1342}, "end": {"line": 51, "column": 10}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1342}, "end": {"line": 51, "column": 10}}, "loc": {"start": {"line": 1, "column": 1342}, "end": {"line": 51, "column": 10}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/utils/helpers.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/utils/helpers.ts", "all": true, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 60}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 66}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 46}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 20}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 19}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 19}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 20}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 22}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 4}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 1}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 69}}, "14": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 35}}, "16": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 16}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 40}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 43}}, "20": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 53}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 74}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 71}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 40}}, "28": {"start": {"line": 29, "column": 0}, "end": {"line": 29, "column": 45}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 1}}, "32": {"start": {"line": 33, "column": 0}, "end": {"line": 33, "column": 53}}, "33": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 56}}, "34": {"start": {"line": 35, "column": 0}, "end": {"line": 35, "column": 1}}, "37": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 68}}, "38": {"start": {"line": 39, "column": 0}, "end": {"line": 39, "column": 10}}, "39": {"start": {"line": 40, "column": 0}, "end": {"line": 40, "column": 14}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 42}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 43}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 38}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 38}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 51}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 3}}, "46": {"start": {"line": 47, "column": 0}, "end": {"line": 47, "column": 1}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 41}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 48}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 1}}}, "s": {"1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "13": 0, "14": 0, "16": 0, "17": 0, "18": 0, "20": 0, "22": 0, "23": 0, "26": 0, "27": 0, "28": 0, "29": 0, "32": 0, "33": 0, "34": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "49": 0, "50": 0, "51": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1515}, "end": {"line": 52, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1515}, "end": {"line": 52, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1515}, "end": {"line": 52, "column": 1}}, "loc": {"start": {"line": 1, "column": 1515}, "end": {"line": 52, "column": 1}}, "line": 1}}, "f": {"0": 1}}, "/home/<USER>/projects/structura-ai/frontend/src/utils/index.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/utils/index.ts", "all": true, "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 27}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 25}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 28}}}, "s": {"0": 0, "1": 0, "2": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -27}}, "locations": [{"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -27}}]}}, "b": {"0": [0]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -27}}, "loc": {"start": {"line": 1, "column": 0}, "end": {"line": 3, "column": -27}}, "line": 1}}, "f": {"0": 0}}, "/home/<USER>/projects/structura-ai/frontend/src/utils/validation.ts": {"path": "/home/<USER>/projects/structura-ai/frontend/src/utils/validation.ts", "all": true, "statementMap": {"1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 57}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 31}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 1}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 53}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 7}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 16}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 15}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 11}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 16}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 3}}, "13": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 1}}, "15": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 63}}, "17": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 71}}, "18": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 37}}, "19": {"start": {"line": 20, "column": 0}, "end": {"line": 20, "column": 1}}, "21": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 55}}, "22": {"start": {"line": 23, "column": 0}, "end": {"line": 23, "column": 32}}, "23": {"start": {"line": 24, "column": 0}, "end": {"line": 24, "column": 1}}, "25": {"start": {"line": 26, "column": 0}, "end": {"line": 26, "column": 70}}, "26": {"start": {"line": 27, "column": 0}, "end": {"line": 27, "column": 31}}, "27": {"start": {"line": 28, "column": 0}, "end": {"line": 28, "column": 1}}, "29": {"start": {"line": 30, "column": 0}, "end": {"line": 30, "column": 70}}, "30": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 31}}, "31": {"start": {"line": 32, "column": 0}, "end": {"line": 32, "column": 1}}, "40": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 70}}, "41": {"start": {"line": 42, "column": 0}, "end": {"line": 42, "column": 10}}, "42": {"start": {"line": 43, "column": 0}, "end": {"line": 43, "column": 31}}, "43": {"start": {"line": 44, "column": 0}, "end": {"line": 44, "column": 70}}, "44": {"start": {"line": 45, "column": 0}, "end": {"line": 45, "column": 3}}, "45": {"start": {"line": 46, "column": 0}, "end": {"line": 46, "column": 1}}, "47": {"start": {"line": 48, "column": 0}, "end": {"line": 48, "column": 67}}, "48": {"start": {"line": 49, "column": 0}, "end": {"line": 49, "column": 27}}, "49": {"start": {"line": 50, "column": 0}, "end": {"line": 50, "column": 59}}, "50": {"start": {"line": 51, "column": 0}, "end": {"line": 51, "column": 3}}, "51": {"start": {"line": 52, "column": 0}, "end": {"line": 52, "column": 10}}, "52": {"start": {"line": 53, "column": 0}, "end": {"line": 53, "column": 33}}, "53": {"start": {"line": 54, "column": 0}, "end": {"line": 54, "column": 84}}, "54": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 3}}, "55": {"start": {"line": 56, "column": 0}, "end": {"line": 56, "column": 1}}, "57": {"start": {"line": 58, "column": 0}, "end": {"line": 58, "column": 73}}, "58": {"start": {"line": 59, "column": 0}, "end": {"line": 59, "column": 30}}, "59": {"start": {"line": 60, "column": 0}, "end": {"line": 60, "column": 62}}, "60": {"start": {"line": 61, "column": 0}, "end": {"line": 61, "column": 3}}, "61": {"start": {"line": 62, "column": 0}, "end": {"line": 62, "column": 10}}, "62": {"start": {"line": 63, "column": 0}, "end": {"line": 63, "column": 39}}, "63": {"start": {"line": 64, "column": 0}, "end": {"line": 64, "column": 39}}, "64": {"start": {"line": 65, "column": 0}, "end": {"line": 65, "column": 18}}, "65": {"start": {"line": 66, "column": 0}, "end": {"line": 66, "column": 80}}, "66": {"start": {"line": 67, "column": 0}, "end": {"line": 67, "column": 3}}, "67": {"start": {"line": 68, "column": 0}, "end": {"line": 68, "column": 1}}}, "s": {"1": 0, "2": 0, "3": 0, "4": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "15": 0, "17": 0, "18": 0, "19": 0, "21": 0, "22": 0, "23": 0, "25": 0, "26": 0, "27": 0, "29": 0, "30": 0, "31": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0, "66": 0, "67": 0}, "branchMap": {"0": {"type": "branch", "line": 1, "loc": {"start": {"line": 1, "column": 1834}, "end": {"line": 68, "column": 1}}, "locations": [{"start": {"line": 1, "column": 1834}, "end": {"line": 68, "column": 1}}]}}, "b": {"0": [1]}, "fnMap": {"0": {"name": "(empty-report)", "decl": {"start": {"line": 1, "column": 1834}, "end": {"line": 68, "column": 1}}, "loc": {"start": {"line": 1, "column": 1834}, "end": {"line": 68, "column": 1}}, "line": 1}}, "f": {"0": 1}}}