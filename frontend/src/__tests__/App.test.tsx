import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import App from '../App'
import { authService } from '../services/api/auth'

// Mock the auth service
vi.mock('../services/api/auth', () => ({
  authService: {
    isAuthenticated: vi.fn(),
    validateAuthentication: vi.fn(),
    getCurrentUser: vi.fn(),
    login: vi.fn(),
    logout: vi.fn(),
  },
}))

const mockAuthService = vi.mocked(authService)

// Test wrapper with router
const TestApp = () => <App />

describe('App', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders without crashing and shows loading initially', async () => {
    mockAuthService.isAuthenticated.mockReturnValue(false)
    
    render(<TestApp />)
    
    // Initially shows loading spinner while checking auth
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    
    // Wait for auth check to complete and login page to show
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
    })
  })

  it('shows login page for unauthenticated users', async () => {
    mockAuthService.isAuthenticated.mockReturnValue(false)
    
    render(<TestApp />)
    
    await waitFor(() => {
      expect(screen.getByText('Structura AI')).toBeInTheDocument()
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
    })
  })

  it('shows dashboard for authenticated users', async () => {
    const mockUser = {
      id: '1',
      email: '<EMAIL>',
      name: 'Test User',
      role: 'admin',
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-01-01T00:00:00Z',
    }

    mockAuthService.isAuthenticated.mockReturnValue(true)
    mockAuthService.validateAuthentication.mockResolvedValue(true)
    mockAuthService.getCurrentUser.mockResolvedValue(mockUser)

    render(<TestApp />)

    await waitFor(() => {
      expect(screen.getByText('Structura AI Dashboard')).toBeInTheDocument()
      expect(screen.getByText('Welcome, Test User')).toBeInTheDocument()
    })
  })

  it('handles auth check failure gracefully', async () => {
    mockAuthService.isAuthenticated.mockReturnValue(true)
    mockAuthService.validateAuthentication.mockResolvedValue(false)

    render(<TestApp />)

    // Should end up at login page after failed validation
    await waitFor(() => {
      expect(screen.getByText('Sign in to your account')).toBeInTheDocument()
    })
  })
})