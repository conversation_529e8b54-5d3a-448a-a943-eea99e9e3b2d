import React from 'react'
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '../ui'
import type { SiteWithStatus, ConnectionStatus } from '../../types/sites'
import { ConnectionStatus as ConnectionStatusEnum } from '../../types/sites'
import { CheckCircle, XCircle, Loader2, Database, TestTube, Eye, Settings } from 'lucide-react'
import { cn } from '../../lib/utils'

export interface SitesTableProps {
  sites: SiteWithStatus[]
  onTestConnection: (siteId: number) => void
  isRefreshing?: boolean
  className?: string
}

interface ConnectionStatusBadgeProps {
  status: ConnectionStatus
  error?: string
  lastTested?: string
}

const ConnectionStatusBadge: React.FC<ConnectionStatusBadgeProps> = ({ 
  status, 
  error, 
  lastTested 
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case ConnectionStatusEnum.Connected:
        return {
          icon: CheckCircle,
          text: 'Connected',
          className: 'bg-green-100 text-green-800 border-green-200'
        }
      case ConnectionStatusEnum.Error:
        return {
          icon: XCircle,
          text: 'Failed',
          className: 'bg-red-100 text-red-800 border-red-200'
        }
      case ConnectionStatusEnum.Testing:
        return {
          icon: Loader2,
          text: 'Testing...',
          className: 'bg-amber-100 text-amber-800 border-amber-200'
        }
      default:
        return {
          icon: XCircle,
          text: 'Unknown',
          className: 'bg-gray-100 text-gray-800 border-gray-200'
        }
    }
  }

  const config = getStatusConfig()
  const StatusIcon = config.icon

  return (
    <div className="flex flex-col gap-1">
      <div className={cn(
        'inline-flex items-center gap-2 px-2 py-1 rounded-md text-xs font-medium border',
        config.className
      )}>
        <StatusIcon className={cn(
          'h-3 w-3',
          status === ConnectionStatusEnum.Testing && 'animate-spin'
        )} />
        {config.text}
      </div>
      {error && (
        <div className="text-xs text-red-600 max-w-xs truncate" title={error}>
          {error}
        </div>
      )}
      {lastTested && status !== ConnectionStatusEnum.Testing && (
        <div className="text-xs text-gray-500">
          {formatLastTested(lastTested)}
        </div>
      )}
    </div>
  )
}

const formatLastTested = (timestamp: string): string => {
  try {
    const date = new Date(timestamp)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    
    if (diffMins < 1) return 'Just now'
    if (diffMins < 60) return `${diffMins}m ago`
    
    const diffHours = Math.floor(diffMins / 60)
    if (diffHours < 24) return `${diffHours}h ago`
    
    const diffDays = Math.floor(diffHours / 24)
    return `${diffDays}d ago`
  } catch {
    return 'Unknown'
  }
}

/**
 * Sites data table component
 * Displays sites in a responsive table with connection status indicators
 */
export const SitesTable: React.FC<SitesTableProps> = ({
  sites,
  onTestConnection,
  isRefreshing = false,
  className
}) => {
  const handleTestConnection = (siteId: number, event: React.MouseEvent) => {
    event.stopPropagation()
    onTestConnection(siteId)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            WooCommerce Sites ({sites.length})
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        {/* Desktop Table View */}
        <div className="hidden md:block">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Site</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Host</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Database</th>
                  <th className="text-left py-3 px-2 font-medium text-gray-700">Status</th>
                  <th className="text-right py-3 px-2 font-medium text-gray-700">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sites.map((site) => (
                  <tr key={site.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-4 px-2">
                      <div className="flex flex-col">
                        <div className="font-medium text-gray-900">
                          {site.display_name}
                        </div>
                        {site.description && (
                          <div className="text-sm text-gray-500">
                            {site.description}
                          </div>
                        )}
                      </div>
                    </td>
                    <td className="py-4 px-2">
                      <div className="text-sm text-gray-700">
                        {site.db_host}:{site.db_port}
                      </div>
                    </td>
                    <td className="py-4 px-2">
                      <div className="text-sm text-gray-700">
                        {site.db_name}
                      </div>
                      <div className="text-xs text-gray-500">
                        {site.db_user}
                      </div>
                    </td>
                    <td className="py-4 px-2">
                      <ConnectionStatusBadge
                        status={site.connection_status}
                        error={site.connection_error}
                        lastTested={site.last_tested}
                      />
                    </td>
                    <td className="py-4 px-2">
                      <div className="flex items-center justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => handleTestConnection(site.id, e)}
                          disabled={site.connection_status === ConnectionStatusEnum.Testing || isRefreshing}
                          className="flex items-center gap-1"
                        >
                          <TestTube className="h-3 w-3" />
                          Test
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="flex items-center gap-1"
                        >
                          <Eye className="h-3 w-3" />
                          View
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          disabled
                          className="flex items-center gap-1"
                        >
                          <Settings className="h-3 w-3" />
                          Edit
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Mobile Card View */}
        <div className="md:hidden space-y-4">
          {sites.map((site) => (
            <Card key={site.id} className="border-gray-200">
              <CardContent className="pt-4">
                <div className="space-y-3">
                  {/* Site Name and Description */}
                  <div>
                    <div className="font-medium text-gray-900">
                      {site.display_name}
                    </div>
                    {site.description && (
                      <div className="text-sm text-gray-500">
                        {site.description}
                      </div>
                    )}
                  </div>

                  {/* Connection Details */}
                  <div className="grid grid-cols-1 gap-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Host:</span>
                      <span className="text-gray-900">{site.db_host}:{site.db_port}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Database:</span>
                      <span className="text-gray-900">{site.db_name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">User:</span>
                      <span className="text-gray-900">{site.db_user}</span>
                    </div>
                  </div>

                  {/* Status and Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <ConnectionStatusBadge
                      status={site.connection_status}
                      error={site.connection_error}
                      lastTested={site.last_tested}
                    />
                    <div className="flex items-center gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => handleTestConnection(site.id, e)}
                        disabled={site.connection_status === ConnectionStatusEnum.Testing || isRefreshing}
                        className="flex items-center gap-1"
                      >
                        <TestTube className="h-3 w-3" />
                        Test
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Empty State */}
        {sites.length === 0 && (
          <div className="text-center py-8">
            <Database className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No sites available</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}

SitesTable.displayName = 'SitesTable'