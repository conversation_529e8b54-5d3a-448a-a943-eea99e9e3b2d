import { describe, it, expect } from 'vitest'
import { render, screen } from '@/test/test-utils'
import { LoadingSpinner } from '../LoadingSpinner'

describe('LoadingSpinner', () => {
  it('renders with default medium size', () => {
    render(<LoadingSpinner />)
    const spinner = screen.getByRole('status', { hidden: true })
    expect(spinner).toHaveClass('h-8', 'w-8')
  })

  it('renders with small size when specified', () => {
    render(<LoadingSpinner size="sm" />)
    const spinner = screen.getByRole('status', { hidden: true })
    expect(spinner).toHaveClass('h-4', 'w-4')
  })

  it('renders with large size when specified', () => {
    render(<LoadingSpinner size="lg" />)
    const spinner = screen.getByRole('status', { hidden: true })
    expect(spinner).toHaveClass('h-12', 'w-12')
  })

  it('applies custom className', () => {
    render(<LoadingSpinner className="custom-class" />)
    const spinner = screen.getByRole('status', { hidden: true })
    expect(spinner).toHaveClass('custom-class')
  })

  it('has spinning animation', () => {
    render(<LoadingSpinner />)
    const spinner = screen.getByRole('status', { hidden: true })
    expect(spinner).toHaveClass('animate-spin')
  })
})