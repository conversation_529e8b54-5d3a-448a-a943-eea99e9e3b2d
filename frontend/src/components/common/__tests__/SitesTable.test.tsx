import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { SitesTable } from '../SitesTable'
import type { SiteWithStatus } from '../../../types/sites'
import { ConnectionStatus } from '../../../types/sites'

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  CheckCircle: () => <div data-testid="check-circle-icon" />,
  XCircle: () => <div data-testid="x-circle-icon" />,
  Loader2: () => <div data-testid="loader-icon" />,
  Database: () => <div data-testid="database-icon" />,
  TestTube: () => <div data-testid="test-tube-icon" />,
  Eye: () => <div data-testid="eye-icon" />,
  Settings: () => <div data-testid="settings-icon" />
}))

const mockSites: SiteWithStatus[] = [
  {
    id: 1,
    name: 'test-site-1',
    display_name: 'Test Site 1',
    description: 'Test description',
    db_host: 'localhost',
    db_port: 3306,
    db_name: 'woocommerce_test',
    db_user: 'testuser',
    db_password: 'testpass',
    db_table_prefix: 'wp_',
    created_at: '2023-01-01T00:00:00Z',
    updated_at: '2023-01-01T00:00:00Z',
    connection_status: ConnectionStatus.Connected,
    last_tested: '2023-01-01T01:00:00Z',
    connection_error: undefined
  },
  {
    id: 2,
    name: 'test-site-2',
    display_name: 'Test Site 2',
    db_host: 'remote.host',
    db_port: 3306,
    db_name: 'woocommerce_prod',
    db_user: 'produser',
    db_password: 'prodpass',
    db_table_prefix: 'wp_',
    created_at: '2023-01-02T00:00:00Z',
    connection_status: ConnectionStatus.Error,
    last_tested: '2023-01-02T01:00:00Z',
    connection_error: 'Connection timeout'
  }
]

describe('SitesTable', () => {
  const mockOnTestConnection = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders sites table with site data', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    // Check if sites are displayed
    expect(screen.getByText('Test Site 1')).toBeInTheDocument()
    expect(screen.getByText('Test Site 2')).toBeInTheDocument()
    expect(screen.getByText('localhost:3306')).toBeInTheDocument()
    expect(screen.getByText('remote.host:3306')).toBeInTheDocument()
  })

  it('shows connection status indicators', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    // Check for connection status badges
    expect(screen.getByText('Connected')).toBeInTheDocument()
    expect(screen.getByText('Failed')).toBeInTheDocument()
    expect(screen.getByText('Connection timeout')).toBeInTheDocument()
  })

  it('calls onTestConnection when test button is clicked', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    const testButtons = screen.getAllByText('Test')
    fireEvent.click(testButtons[0])

    expect(mockOnTestConnection).toHaveBeenCalledWith(1)
  })

  it('disables test buttons when isRefreshing is true', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
        isRefreshing={true}
      />
    )

    const testButtons = screen.getAllByText('Test')
    expect(testButtons[0]).toBeDisabled()
    expect(testButtons[1]).toBeDisabled()
  })

  it('disables test button for sites with testing status', () => {
    const sitesWithTesting = [
      {
        ...mockSites[0],
        connection_status: ConnectionStatus.Testing
      }
    ]

    render(
      <SitesTable
        sites={sitesWithTesting}
        onTestConnection={mockOnTestConnection}
      />
    )

    const testButton = screen.getByText('Test')
    expect(testButton).toBeDisabled()
  })

  it('shows empty state when no sites provided', () => {
    render(
      <SitesTable
        sites={[]}
        onTestConnection={mockOnTestConnection}
      />
    )

    expect(screen.getByText('No sites available')).toBeInTheDocument()
  })

  it('displays site descriptions when available', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('shows last tested time for connected sites', () => {
    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    // Should show relative time for last tested
    expect(screen.getByText(/ago/)).toBeInTheDocument()
  })

  it('renders mobile card view on small screens', () => {
    // Mock window.innerWidth for mobile
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500
    })

    render(
      <SitesTable
        sites={mockSites}
        onTestConnection={mockOnTestConnection}
      />
    )

    // Mobile view should still show site names
    expect(screen.getByText('Test Site 1')).toBeInTheDocument()
    expect(screen.getByText('Test Site 2')).toBeInTheDocument()
  })
})