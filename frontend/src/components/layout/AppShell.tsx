import React from 'react'
import { cn } from '../../lib/utils'
import { Sidebar } from './Sidebar'

// Props interface for AppShell component
interface AppShellProps {
  children: React.ReactNode
  className?: string
}

/**
 * Main application shell component that provides the layout structure
 * with sidebar navigation and main content area.
 * 
 * Features:
 * - Responsive grid layout with sidebar + main content
 * - Adapts from desktop to tablet breakpoints (768px)
 * - Provides consistent layout for authenticated users
 */
export const AppShell: React.FC<AppShellProps> = ({
  children,
  className
}) => {
  return (
    <div className={cn(
      // Base layout - full viewport height with CSS Grid
      "min-h-screen bg-gray-50",
      "grid grid-cols-[280px_1fr]", // Desktop: fixed sidebar + flexible content
      // Tablet responsive - allow sidebar overlay via fixed positioning in Sidebar
      "md:grid-cols-1 md:relative",
      className
    )}>
      {/* Sidebar Navigation */}
      <Sidebar />
      
      {/* Main Content Area */}
      <main className={cn(
        // Main content styling
        "bg-white",
        "overflow-x-hidden overflow-y-auto",
        // Padding and spacing
        "p-6",
        "min-h-screen",
        // Responsive adjustments
        "md:p-4"
      )}>
        {children}
      </main>
    </div>
  )
}

AppShell.displayName = 'AppShell'

export default AppShell