import React, { useState, useCallback } from 'react'
import { Link, useLocation } from 'react-router-dom'
import {
  Home,
  Server,
  Settings,
  Play,
  Clock,
  User,
  LogOut,
  Menu,
  X
} from 'lucide-react'
import { cn } from '../../lib/utils'
import { useAuth } from '../../contexts/useAuth'
import { Button } from '../ui/button'

// Navigation menu items configuration
interface NavItem {
  name: string
  path: string
  icon: React.ComponentType<{ className?: string }>
}

const navItems: NavItem[] = [
  { name: 'Dashboard', path: '/dashboard', icon: Home },
  { name: 'Sites', path: '/sites', icon: Server },
  { name: 'Profiles', path: '/profiles', icon: Settings },
  { name: 'Processing', path: '/processing', icon: Play },
  { name: 'History', path: '/history', icon: Clock },
]

// Props interface for Sidebar component
interface SidebarProps {
  className?: string
}

/**
 * Sidebar navigation component with responsive design and user profile dropdown.
 * 
 * Features:
 * - Navigation menu with active route highlighting
 * - User profile dropdown with logout functionality
 * - Collapsible design for tablet view
 * - Lucide React icons for consistent styling
 */
export const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const location = useLocation()
  const { user, logout } = useAuth()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [isProfileDropdownOpen, setIsProfileDropdownOpen] = useState(false)

  // Toggle sidebar collapse for responsive design
  const toggleCollapse = useCallback(() => {
    setIsCollapsed(!isCollapsed)
  }, [isCollapsed])

  // Handle logout with profile dropdown close
  const handleLogout = useCallback(async () => {
    try {
      await logout()
      setIsProfileDropdownOpen(false)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }, [logout])

  // Toggle profile dropdown
  const toggleProfileDropdown = useCallback(() => {
    setIsProfileDropdownOpen(!isProfileDropdownOpen)
  }, [isProfileDropdownOpen])

  // Check if route is active
  const isActiveRoute = useCallback((path: string) => {
    return location.pathname === path || 
           (path !== '/dashboard' && location.pathname.startsWith(path))
  }, [location.pathname])

  return (
    <aside className={cn(
      // Base sidebar styling
      "bg-gray-900 text-white",
      "flex flex-col",
      "transition-all duration-300 ease-in-out",
      // Desktop width - fixed
      "w-[280px]",
      // Tablet responsive - collapsible
      "md:w-auto md:fixed md:inset-y-0 md:left-0 md:z-50",
      isCollapsed && "md:w-16",
      !isCollapsed && "md:w-[280px]",
      className
    )}>
      {/* Sidebar Header */}
      <div className="p-6 border-b border-gray-800">
        <div className="flex items-center justify-between">
          {!isCollapsed && (
            <h1 className="text-xl font-bold">Structura AI</h1>
          )}
          {/* Hamburger menu for tablet */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleCollapse}
            className="hidden md:flex text-white hover:bg-gray-800"
            aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
          >
            {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 px-4 py-6">
        <ul className="space-y-2">
          {navItems.map((item) => {
            const Icon = item.icon
            const isActive = isActiveRoute(item.path)
            
            return (
              <li key={item.path}>
                <Link
                  to={item.path}
                  className={cn(
                    // Base link styling
                    "flex items-center px-4 py-3 rounded-lg",
                    "text-sm font-medium transition-colors duration-200",
                    // Hover states
                    "hover:bg-gray-800",
                    // Active states
                    isActive && "bg-blue-600 text-white hover:bg-blue-700",
                    !isActive && "text-gray-300 hover:text-white",
                    // Collapsed state adjustments
                    isCollapsed && "justify-center px-2"
                  )}
                  title={isCollapsed ? item.name : undefined}
                >
                  <Icon className={cn(
                    "h-5 w-5",
                    !isCollapsed && "mr-3"
                  )} />
                  {!isCollapsed && (
                    <span>{item.name}</span>
                  )}
                  {/* Active indicator */}
                  {isActive && !isCollapsed && (
                    <div className="ml-auto w-2 h-2 bg-white rounded-full" />
                  )}
                </Link>
              </li>
            )
          })}
        </ul>
      </nav>

      {/* User Profile Section */}
      <div className="p-4 border-t border-gray-800">
        <div className="relative">
          <button
            onClick={toggleProfileDropdown}
            className={cn(
              "flex items-center w-full px-4 py-3 rounded-lg",
              "text-sm font-medium text-gray-300",
              "hover:bg-gray-800 hover:text-white",
              "transition-colors duration-200",
              isCollapsed && "justify-center px-2"
            )}
            title={isCollapsed ? `User: ${user?.email || 'Unknown'}` : undefined}
          >
            <User className={cn(
              "h-5 w-5",
              !isCollapsed && "mr-3"
            )} />
            {!isCollapsed && (
              <>
                <div className="flex flex-col items-start">
                  <span className="text-white">{user?.name || 'User'}</span>
                  <span className="text-xs text-gray-400">{user?.email}</span>
                </div>
              </>
            )}
          </button>

          {/* Profile Dropdown */}
          {isProfileDropdownOpen && !isCollapsed && (
            <div className="absolute bottom-full left-0 right-0 mb-2 bg-gray-800 rounded-lg border border-gray-700 shadow-lg">
              <button
                onClick={handleLogout}
                className="flex items-center w-full px-4 py-3 text-sm text-gray-300 hover:bg-gray-700 hover:text-white rounded-lg transition-colors duration-200"
              >
                <LogOut className="h-4 w-4 mr-3" />
                <span>Sign Out</span>
              </button>
            </div>
          )}
        </div>
      </div>
    </aside>
  )
}

Sidebar.displayName = 'Sidebar'

export default Sidebar