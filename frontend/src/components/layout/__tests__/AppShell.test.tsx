import React from 'react'
import { render, screen } from '@testing-library/react'
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from 'react-router-dom'
import { vi } from 'vitest'
import { AuthProvider } from '../../../contexts/AuthContext'
import { AppShell } from '../AppShell'

// Mock the useAuth hook
vi.mock('../../../contexts/useAuth', () => ({
  useAuth: () => ({
    user: { email: '<EMAIL>', name: 'Test User' },
    isAuthenticated: true,
    logout: vi.fn()
  })
}))

// Test wrapper component with required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </BrowserRouter>
)

describe('AppShell', () => {
  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <AppShell>
          <div>Test content</div>
        </AppShell>
      </TestWrapper>
    )
    
    expect(screen.getByText('Test content')).toBeInTheDocument()
  })

  it('renders sidebar navigation', () => {
    render(
      <TestWrapper>
        <AppShell>
          <div>Test content</div>
        </AppShell>
      </TestWrapper>
    )
    
    // Check if sidebar brand is present
    expect(screen.getByText('Structura AI')).toBeInTheDocument()
  })

  it('renders main content area', () => {
    render(
      <TestWrapper>
        <AppShell>
          <div data-testid="main-content">Test content</div>
        </AppShell>
      </TestWrapper>
    )
    
    const mainContent = screen.getByTestId('main-content')
    expect(mainContent).toBeInTheDocument()
  })

  it('applies custom className when provided', () => {
    const { container } = render(
      <TestWrapper>
        <AppShell className="custom-class">
          <div>Test content</div>
        </AppShell>
      </TestWrapper>
    )
    
    const appShell = container.firstChild
    expect(appShell).toHaveClass('custom-class')
  })

  it('has responsive grid layout classes', () => {
    const { container } = render(
      <TestWrapper>
        <AppShell>
          <div>Test content</div>
        </AppShell>
      </TestWrapper>
    )
    
    const appShell = container.firstChild
    expect(appShell).toHaveClass('grid')
    expect(appShell).toHaveClass('grid-cols-[280px_1fr]')
    expect(appShell).toHaveClass('md:grid-cols-1')
    expect(appShell).toHaveClass('md:relative')
  })
})