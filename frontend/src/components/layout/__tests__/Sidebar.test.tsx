import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { vi } from 'vitest'
import { AuthProvider } from '../../../contexts/AuthContext'
import { Sidebar } from '../Sidebar'

// Mock the useAuth hook
const mockLogout = vi.fn()
vi.mock('../../../contexts/useAuth', () => ({
  useAuth: () => ({
    user: { email: '<EMAIL>', name: 'Test User' },
    isAuthenticated: true,
    logout: mockLogout
  })
}))

// Test wrapper component with required providers
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </BrowserRouter>
)

// Mock useLocation to control current route
const mockLocation = { pathname: '/dashboard' }
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useLocation: () => mockLocation,
  }
})

describe('Sidebar', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    expect(screen.getByText('Structura AI')).toBeInTheDocument()
  })

  it('renders all navigation menu items', () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    expect(screen.getByText('Sites')).toBeInTheDocument()
    expect(screen.getByText('Profiles')).toBeInTheDocument()
    expect(screen.getByText('Processing')).toBeInTheDocument()
    expect(screen.getByText('History')).toBeInTheDocument()
  })

  it('highlights active route correctly', () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    const dashboardLink = screen.getByText('Dashboard').closest('a')
    expect(dashboardLink).toHaveClass('bg-blue-600')
  })

  it('renders user profile information', () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    expect(screen.getByText('Test User')).toBeInTheDocument()
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument()
  })

  it('shows and hides profile dropdown when clicked', async () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    const profileButton = screen.getByText('Test User').closest('button')
    expect(profileButton).toBeInTheDocument()
    
    // Initially dropdown should not be visible
    expect(screen.queryByText('Sign Out')).not.toBeInTheDocument()
    
    // Click to open dropdown
    if (profileButton) {
      fireEvent.click(profileButton)
    }
    
    await waitFor(() => {
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
    })
  })

  it('calls logout function when sign out is clicked', async () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    const profileButton = screen.getByText('Test User').closest('button')
    if (profileButton) {
      fireEvent.click(profileButton)
    }
    
    await waitFor(() => {
      expect(screen.getByText('Sign Out')).toBeInTheDocument()
    })
    
    const signOutButton = screen.getByText('Sign Out')
    fireEvent.click(signOutButton)
    
    expect(mockLogout).toHaveBeenCalledTimes(1)
  })

  it('toggles sidebar collapse when hamburger is clicked', async () => {
    render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    const hamburgerButton = screen.getByLabelText(/collapse sidebar/i)
    expect(hamburgerButton).toBeInTheDocument()
    
    fireEvent.click(hamburgerButton)
    
    // After clicking, button should show expand label
    await waitFor(() => {
      expect(screen.getByLabelText(/expand sidebar/i)).toBeInTheDocument()
    })
  })

  it('applies custom className when provided', () => {
    const { container } = render(
      <TestWrapper>
        <Sidebar className="custom-sidebar-class" />
      </TestWrapper>
    )
    
    const sidebar = container.querySelector('aside')
    expect(sidebar).toHaveClass('custom-sidebar-class')
  })

  it('has proper responsive classes', () => {
    const { container } = render(
      <TestWrapper>
        <Sidebar />
      </TestWrapper>
    )
    
    const sidebar = container.querySelector('aside')
    expect(sidebar).toHaveClass('md:fixed', 'md:inset-y-0', 'md:left-0', 'md:z-50')
    expect(sidebar).toHaveClass('md:w-[280px]') // Should have width set by default
  })
})