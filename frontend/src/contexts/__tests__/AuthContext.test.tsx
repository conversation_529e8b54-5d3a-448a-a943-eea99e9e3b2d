import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import { renderHook, act, waitFor } from '@testing-library/react'
import { ReactNode } from 'react'
import { AuthProvider, useAuth } from '../AuthContext'
import { authService } from '../../services/api/auth'
import type { User } from '../../types/auth'

// Mock the auth service
vi.mock('../../services/api/auth', () => ({
  authService: {
    login: vi.fn(),
    logout: vi.fn(),
    isAuthenticated: vi.fn(),
    validateAuthentication: vi.fn(),
    getCurrentUser: vi.fn(),
  },
}))

const mockAuthService = authService as {
  login: Mock
  logout: Mock
  isAuthenticated: Mock
  validateAuthentication: Mock
  getCurrentUser: Mock
}

// Test wrapper component
const createWrapper = () => {
  return ({ children }: { children: ReactNode }) => (
    <AuthProvider>{children}</AuthProvider>
  )
}

// Mock user data
const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'admin',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

describe('AuthContext', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('initial state', () => {
    it('should start with loading state when checking existing auth', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Initially loading
      expect(result.current.isLoading).toBe(true)
      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()

      // Wait for auth check to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })
    })

    it('should authenticate user if valid token exists', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true)
      mockAuthService.validateAuthentication.mockResolvedValue(true)
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
        expect(result.current.isAuthenticated).toBe(true)
        expect(result.current.user).toEqual(mockUser)
      })
    })

    it('should logout user if token validation fails', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(true)
      mockAuthService.validateAuthentication.mockResolvedValue(false)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
        expect(result.current.isAuthenticated).toBe(false)
        expect(result.current.user).toBeNull()
      })
    })
  })

  describe('login', () => {
    it('should successfully login user', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false)
      mockAuthService.login.mockResolvedValue({
        access_token: 'token',
        refresh_token: 'refresh',
        expires_in: 3600,
        user: mockUser,
      })

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial loading to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      await act(async () => {
        await result.current.login('<EMAIL>', 'password')
      })

      expect(result.current.isAuthenticated).toBe(true)
      expect(result.current.user).toEqual(mockUser)
      expect(result.current.error).toBeNull()
      expect(mockAuthService.login).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      })
    })

    it('should handle login error', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false)
      const errorMessage = 'Invalid credentials'
      mockAuthService.login.mockRejectedValue(new Error(errorMessage))

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial loading to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'wrong-password')
        } catch (error) {
          // Expected to throw
        }
      })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()
      expect(result.current.error).toBe(errorMessage)
    })

    it('should set loading state during login', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false)
      let resolveLogin: (value: any) => void
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve
      })
      mockAuthService.login.mockReturnValue(loginPromise)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial loading to complete
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Start login
      act(() => {
        result.current.login('<EMAIL>', 'password')
      })

      // Should be loading during login
      expect(result.current.isLoading).toBe(true)

      // Resolve login
      act(() => {
        resolveLogin({
          access_token: 'token',
          refresh_token: 'refresh',
          expires_in: 3600,
          user: mockUser,
        })
      })

      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })
    })
  })

  describe('logout', () => {
    it('should successfully logout user', async () => {
      // Setup authenticated state
      mockAuthService.isAuthenticated.mockReturnValue(true)
      mockAuthService.validateAuthentication.mockResolvedValue(true)
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser)
      mockAuthService.logout.mockResolvedValue(undefined)

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial auth check
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true)
      })

      // Logout
      await act(async () => {
        await result.current.logout()
      })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()
      expect(result.current.error).toBeNull()
      expect(mockAuthService.logout).toHaveBeenCalled()
    })

    it('should logout user even if API call fails', async () => {
      // Setup authenticated state
      mockAuthService.isAuthenticated.mockReturnValue(true)
      mockAuthService.validateAuthentication.mockResolvedValue(true)
      mockAuthService.getCurrentUser.mockResolvedValue(mockUser)
      mockAuthService.logout.mockRejectedValue(new Error('API Error'))

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial auth check
      await waitFor(() => {
        expect(result.current.isAuthenticated).toBe(true)
      })

      // Logout (should not throw)
      await act(async () => {
        await result.current.logout()
      })

      expect(result.current.isAuthenticated).toBe(false)
      expect(result.current.user).toBeNull()
    })
  })

  describe('clearError', () => {
    it('should clear error state', async () => {
      mockAuthService.isAuthenticated.mockReturnValue(false)
      mockAuthService.login.mockRejectedValue(new Error('Login failed'))

      const { result } = renderHook(() => useAuth(), {
        wrapper: createWrapper(),
      })

      // Wait for initial loading
      await waitFor(() => {
        expect(result.current.isLoading).toBe(false)
      })

      // Cause an error
      await act(async () => {
        try {
          await result.current.login('<EMAIL>', 'wrong-password')
        } catch (error) {
          // Expected
        }
      })

      expect(result.current.error).toBeTruthy()

      // Clear error
      act(() => {
        result.current.clearError()
      })

      expect(result.current.error).toBeNull()
    })
  })

  describe('useAuth hook', () => {
    it('should throw error when used outside AuthProvider', () => {
      const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})

      expect(() => {
        renderHook(() => useAuth())
      }).toThrow('useAuth must be used within an AuthProvider')

      consoleError.mockRestore()
    })
  })
})