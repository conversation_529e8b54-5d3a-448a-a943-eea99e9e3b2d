import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { BrowserRouter } from 'react-router-dom'
import { ReactNode } from 'react'
import { LoginPage } from '../LoginPage'
import { useAuth } from '../../../contexts/AuthContext'

// Mock the useAuth hook
vi.mock('../../../contexts/AuthContext', () => ({
  useAuth: vi.fn(),
}))

// Mock react-router-dom
const mockNavigate = vi.fn()
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  }
})

const mockUseAuth = useAuth as Mock

// Test wrapper
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

describe('LoginPage', () => {
  const mockLogin = vi.fn()
  const mockClearError = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
    mockUseAuth.mockReturnValue({
      login: mockLogin,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      clearError: mockClearError,
    })
  })

  describe('rendering', () => {
    it('should render login form', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      expect(screen.getByRole('heading', { name: /structura ai/i })).toBeInTheDocument()
      expect(screen.getByRole('heading', { name: /sign in to your account/i })).toBeInTheDocument()
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/password/i)).toBeInTheDocument()
      expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument()
    })

    it('should render loading spinner when auth is loading', () => {
      mockUseAuth.mockReturnValue({
        login: mockLogin,
        isAuthenticated: false,
        isLoading: true,
        error: null,
        clearError: mockClearError,
      })

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
      expect(screen.queryByRole('form')).not.toBeInTheDocument()
    })

    it('should redirect to dashboard when authenticated', () => {
      mockUseAuth.mockReturnValue({
        login: mockLogin,
        isAuthenticated: true,
        isLoading: false,
        error: null,
        clearError: mockClearError,
      })

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      expect(mockNavigate).toHaveBeenCalledWith('/dashboard', { replace: true })
    })

    it('should display auth error when present', () => {
      const errorMessage = 'Invalid credentials'
      mockUseAuth.mockReturnValue({
        login: mockLogin,
        isAuthenticated: false,
        isLoading: false,
        error: errorMessage,
        clearError: mockClearError,
      })

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      expect(screen.getByText(errorMessage)).toBeInTheDocument()
    })
  })

  describe('form validation', () => {
    it('should require email', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Email address is required')).toBeInTheDocument()
      })

      expect(mockLogin).not.toHaveBeenCalled()
    })

    it('should validate email format', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, 'invalid-email')

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Please enter a valid email address')).toBeInTheDocument()
      })

      expect(mockLogin).not.toHaveBeenCalled()
    })

    it('should require password', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      await user.type(emailInput, '<EMAIL>')

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Password is required')).toBeInTheDocument()
      })

      expect(mockLogin).not.toHaveBeenCalled()
    })

    it('should validate password length', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByLabelText(/password/i)

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, '123') // Less than 6 characters

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(screen.getByText('Password must be at least 6 characters long')).toBeInTheDocument()
      })

      expect(mockLogin).not.toHaveBeenCalled()
    })
  })

  describe('form submission', () => {
    it('should submit login form with valid data', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByLabelText(/password/i)

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        expect(mockLogin).toHaveBeenCalledWith('<EMAIL>', 'password123')
      })
    })

    it('should show loading state during submission', async () => {
      const user = userEvent.setup()
      
      // Mock login to be a pending promise
      let resolveLogin: (value: any) => void
      const loginPromise = new Promise((resolve) => {
        resolveLogin = resolve
      })
      mockLogin.mockReturnValue(loginPromise)

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByLabelText(/password/i)
      const submitButton = screen.getByRole('button', { name: /sign in/i })

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')
      await user.click(submitButton)

      // Should show loading state
      expect(screen.getByText(/signing in/i)).toBeInTheDocument()
      expect(submitButton).toBeDisabled()

      // Resolve the login
      resolveLogin!(undefined)

      await waitFor(() => {
        expect(screen.getByText(/sign in/i)).toBeInTheDocument()
        expect(submitButton).not.toBeDisabled()
      })
    })

    it('should clear errors on successful submission attempt', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByLabelText(/password/i)

      await user.type(emailInput, '<EMAIL>')
      await user.type(passwordInput, 'password123')

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      expect(mockClearError).toHaveBeenCalled()
    })
  })

  describe('accessibility', () => {
    it('should have proper form labels', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      const passwordInput = screen.getByLabelText(/password/i)

      expect(emailInput).toHaveAttribute('type', 'email')
      expect(emailInput).toHaveAttribute('autoComplete', 'email')
      expect(passwordInput).toHaveAttribute('type', 'password')
      expect(passwordInput).toHaveAttribute('autoComplete', 'current-password')
    })

    it('should have autofocus on email input', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const emailInput = screen.getByLabelText(/email address/i)
      expect(emailInput).toHaveAttribute('autoFocus')
    })

    it('should show validation errors with proper styling', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      )

      const submitButton = screen.getByRole('button', { name: /sign in/i })
      await user.click(submitButton)

      await waitFor(() => {
        const emailInput = screen.getByLabelText(/email address/i)
        expect(emailInput).toHaveClass('border-red-500')
      })
    })
  })
})