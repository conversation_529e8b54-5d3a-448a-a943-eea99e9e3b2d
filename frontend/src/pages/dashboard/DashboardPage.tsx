import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'
import { Button } from '../../components/ui/button'
import { useAuth } from '../../contexts/useAuth'

/**
 * Dashboard page component
 * Main dashboard view showing system overview and quick navigation
 */
export const DashboardPage: React.FC = () => {
  const { user } = useAuth()

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">
            Welcome back, {user?.name || user?.email || 'User'}
          </p>
        </div>
      </div>

      {/* Dashboard Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Welcome Card */}
          <Card>
            <CardHeader>
              <CardTitle>Welcome to Structura AI</CardTitle>
              <CardDescription>
                AI-powered HTML formatter for WooCommerce
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600">
                This is your dashboard where you can manage sites, profiles, 
                and processing tasks.
              </p>
            </CardContent>
          </Card>

          {/* Sites Card */}
          <Card>
            <CardHeader>
              <CardTitle>Sites</CardTitle>
              <CardDescription>
                Manage your WooCommerce sites
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Configure and manage your WooCommerce database connections.
              </p>
              <Button variant="outline" size="sm" disabled>
                Manage Sites (Coming Soon)
              </Button>
            </CardContent>
          </Card>

          {/* Profiles Card */}
          <Card>
            <CardHeader>
              <CardTitle>Profiles</CardTitle>
              <CardDescription>
                AI processing profiles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Create and manage AI processing profiles for content formatting.
              </p>
              <Button variant="outline" size="sm" disabled>
                Manage Profiles (Coming Soon)
              </Button>
            </CardContent>
          </Card>

          {/* Processing Card */}
          <Card>
            <CardHeader>
              <CardTitle>Processing</CardTitle>
              <CardDescription>
                Format product content
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                Run AI-powered HTML formatting on your WooCommerce products.
              </p>
              <Button variant="outline" size="sm" disabled>
                Start Processing (Coming Soon)
              </Button>
            </CardContent>
          </Card>

          {/* History Card */}
          <Card>
            <CardHeader>
              <CardTitle>History</CardTitle>
              <CardDescription>
                Processing history & logs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 mb-4">
                View past processing runs, results, and detailed logs.
              </p>
              <Button variant="outline" size="sm" disabled>
                View History (Coming Soon)
              </Button>
            </CardContent>
          </Card>

          {/* Status Card */}
          <Card>
            <CardHeader>
              <CardTitle>System Status</CardTitle>
              <CardDescription>
                System health & information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Authentication:</span>
                  <span className="text-green-600 font-medium">✓ Active</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>API Connection:</span>
                  <span className="text-gray-400">Pending</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>User Role:</span>
                  <span className="font-medium">{user?.role || 'User'}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
    </div>
  )
}

DashboardPage.displayName = 'DashboardPage'

export default DashboardPage