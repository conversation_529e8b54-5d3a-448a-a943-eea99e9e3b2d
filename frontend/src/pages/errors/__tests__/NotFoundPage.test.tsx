import React from 'react'
import { render, screen, fireEvent } from '@testing-library/react'
import { <PERSON>rowserRouter } from 'react-router-dom'
import { vi } from 'vitest'
import { NotFoundPage } from '../NotFoundPage'

// Mock window.history.back
const mockHistoryBack = vi.fn()
Object.defineProperty(window, 'history', {
  value: {
    back: mockHistoryBack,
  },
  writable: true,
})

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

describe('NotFoundPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    expect(screen.getByText('Page Not Found')).toBeInTheDocument()
  })

  it('displays proper 404 message', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    expect(screen.getByText('Page Not Found')).toBeInTheDocument()
    expect(screen.getByText(/The page you're looking for doesn't exist/)).toBeInTheDocument()
  })

  it('renders dashboard link', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    const dashboardLink = screen.getByText('Go to Dashboard').closest('a')
    expect(dashboardLink).toBeInTheDocument()
    expect(dashboardLink).toHaveAttribute('href', '/dashboard')
  })

  it('renders go back button', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    const goBackButton = screen.getByText('Go Back')
    expect(goBackButton).toBeInTheDocument()
  })

  it('calls window.history.back when go back button is clicked', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    const goBackButton = screen.getByText('Go Back')
    fireEvent.click(goBackButton)
    
    expect(mockHistoryBack).toHaveBeenCalledTimes(1)
  })

  it('displays emoji icon', () => {
    render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    expect(screen.getByText('🤔')).toBeInTheDocument()
  })

  it('has proper layout styling classes', () => {
    const { container } = render(
      <TestWrapper>
        <NotFoundPage />
      </TestWrapper>
    )
    
    const mainDiv = container.firstChild
    expect(mainDiv).toHaveClass('min-h-screen', 'bg-gray-50', 'flex', 'items-center', 'justify-center')
  })
})