import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../../components/ui/card'

/**
 * History page component
 * Displays processing job history and detailed logs
 */
export const HistoryPage: React.FC = () => {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">History</h1>
          <p className="text-gray-600 mt-1">
            View past processing runs, results, and detailed logs
          </p>
        </div>
      </div>

      {/* Coming Soon Content */}
      <Card>
        <CardHeader>
          <CardTitle>Processing History</CardTitle>
          <CardDescription>
            Complete history of AI processing jobs with detailed logs and results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="mb-4">
              <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                <span className="text-2xl">📊</span>
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Coming Soon
            </h3>
            <p className="text-gray-600 max-w-sm mx-auto">
              This section will show you detailed history of all processing jobs, 
              including success rates, error logs, and performance metrics.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

HistoryPage.displayName = 'HistoryPage'

export default HistoryPage