import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, Button, Skeleton } from '../../components/ui'
import { sitesApiService } from '../../services/api'
import type { SiteWithStatus } from '../../types/sites'
import { ConnectionStatus } from '../../types/sites'
import { SitesTable } from '../../components/common/SitesTable'
import { RefreshCw, AlertCircle, Database } from 'lucide-react'

export interface SitesPageProps {
  className?: string
}

/**
 * Sites management page component
 * Displays WooCommerce sites with real-time connection status
 */
export const SitesPage: React.FC<SitesPageProps> = ({ className }) => {
  const [sites, setSites] = useState<SiteWithStatus[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load sites data
  const loadSites = useCallback(async () => {
    try {
      setError(null)
      const sitesData = await sitesApiService.getAllWithStatus()
      setSites(sitesData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sites')
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Refresh sites and test connections
  const refreshSites = useCallback(async () => {
    if (sites.length === 0) return
    
    setIsRefreshing(true)
    setError(null)
    
    try {
      // Set all sites to testing status
      setSites(prevSites => 
        prevSites.map(site => ({
          ...site,
          connection_status: ConnectionStatus.Testing
        }))
      )

      // Test all connections
      const siteIds = sites.map(site => site.id)
      const testResults = await sitesApiService.testMultipleConnections(siteIds)
      
      // Update sites with test results
      setSites(prevSites => 
        prevSites.map(site => {
          const testResult = testResults.find(result => result.site_id === site.id)
          return {
            ...site,
            connection_status: testResult?.status || ConnectionStatus.Error,
            last_tested: testResult?.tested_at || new Date().toISOString(),
            connection_error: testResult?.error
          }
        })
      )
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh connections')
    } finally {
      setIsRefreshing(false)
    }
  }, [sites])

  // Test individual site connection
  const testSiteConnection = useCallback(async (siteId: number) => {
    setSites(prevSites => 
      prevSites.map(site => 
        site.id === siteId 
          ? { ...site, connection_status: ConnectionStatus.Testing }
          : site
      )
    )

    try {
      const testResult = await sitesApiService.testConnection(siteId)
      setSites(prevSites => 
        prevSites.map(site => 
          site.id === siteId 
            ? {
                ...site,
                connection_status: testResult.status,
                last_tested: testResult.tested_at || new Date().toISOString(),
                connection_error: testResult.error
              }
            : site
        )
      )
    } catch (err) {
      setSites(prevSites => 
        prevSites.map(site => 
          site.id === siteId 
            ? {
                ...site,
                connection_status: ConnectionStatus.Error,
                last_tested: new Date().toISOString(),
                connection_error: err instanceof Error ? err.message : 'Connection failed'
              }
            : site
        )
      )
    }
  }, [])

  // Load sites on mount
  useEffect(() => {
    loadSites()
  }, [loadSites])

  // Auto-refresh connections after initial load
  useEffect(() => {
    if (!isLoading && sites.length > 0) {
      const timer = setTimeout(() => {
        refreshSites()
      }, 1000) // Refresh connections 1 second after loading

      return () => clearTimeout(timer)
    }
  }, [isLoading, sites.length, refreshSites])

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Sites</h1>
          <p className="text-gray-600 mt-1">
            Manage your WooCommerce database connections and monitor their status
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={refreshSites}
            disabled={isRefreshing || sites.length === 0}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh All
          </Button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertCircle className="h-5 w-5" />
              <span className="font-medium">Error:</span>
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {isLoading && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-9 w-24" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-0">
                  <div className="flex items-center gap-3">
                    <Skeleton className="h-4 w-4 rounded" />
                    <div className="space-y-1">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                  <Skeleton className="h-6 w-16" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {!isLoading && sites.length === 0 && !error && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-12">
              <div className="mb-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <Database className="h-8 w-8 text-gray-400" />
                </div>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No sites configured
              </h3>
              <p className="text-gray-600 max-w-sm mx-auto mb-6">
                Get started by adding your first WooCommerce database connection.
                You'll be able to monitor connection status and manage your sites here.
              </p>
              <Button variant="outline">
                <Database className="h-4 w-4 mr-2" />
                Add Your First Site
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Sites Table */}
      {!isLoading && sites.length > 0 && (
        <SitesTable
          sites={sites}
          onTestConnection={testSiteConnection}
          isRefreshing={isRefreshing}
        />
      )}
    </div>
  )
}

SitesPage.displayName = 'SitesPage'

export default SitesPage