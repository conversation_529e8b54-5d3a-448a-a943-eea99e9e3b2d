import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { SitesPage } from '../SitesPage'
import { sitesApiService } from '../../../services/api'
import type { SiteWithStatus } from '../../../types/sites'
import { ConnectionStatus } from '../../../types/sites'

// Mock the API service
vi.mock('../../../services/api', () => ({
  sitesApiService: {
    getAllWithStatus: vi.fn(),
    testConnection: vi.fn(),
    testMultipleConnections: vi.fn()
  }
}))

// Mock the SitesTable component
vi.mock('../../../components/common/SitesTable', () => ({
  SitesTable: ({ sites, onTestConnection, isRefreshing }: any) => (
    <div data-testid="sites-table">
      <div>Sites count: {sites.length}</div>
      <div>Refreshing: {isRefreshing ? 'true' : 'false'}</div>
      {sites.map((site: any) => (
        <div key={site.id}>
          <span>{site.display_name}</span>
          <button onClick={() => onTestConnection(site.id)}>
            Test {site.id}
          </button>
        </div>
      ))}
    </div>
  )
}))

// Mock lucide-react icons
vi.mock('lucide-react', () => ({
  RefreshCw: () => <div data-testid="refresh-icon" />,
  AlertCircle: () => <div data-testid="alert-icon" />,
  Database: () => <div data-testid="database-icon" />
}))

const mockSitesData: SiteWithStatus[] = [
  {
    id: 1,
    name: 'test-site-1',
    display_name: 'Test Site 1',
    db_host: 'localhost',
    db_port: 3306,
    db_name: 'woocommerce_test',
    db_user: 'testuser',
    db_password: 'testpass',
    db_table_prefix: 'wp_',
    created_at: '2023-01-01T00:00:00Z',
    connection_status: ConnectionStatus.Connected,
    last_tested: '2023-01-01T01:00:00Z'
  },
  {
    id: 2,
    name: 'test-site-2',
    display_name: 'Test Site 2',
    db_host: 'remote.host',
    db_port: 3306,
    db_name: 'woocommerce_prod',
    db_user: 'produser',
    db_password: 'prodpass',
    db_table_prefix: 'wp_',
    created_at: '2023-01-02T00:00:00Z',
    connection_status: ConnectionStatus.Error,
    last_tested: '2023-01-02T01:00:00Z'
  }
]

describe('SitesPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders page header and description', () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue([])

    render(<SitesPage />)

    expect(screen.getByText('Sites')).toBeInTheDocument()
    expect(screen.getByText(/Manage your WooCommerce database connections/)).toBeInTheDocument()
  })

  it('shows loading state initially', () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockImplementation(
      () => new Promise(() => {}) // Never resolves to keep loading
    )

    render(<SitesPage />)

    expect(screen.getByRole('status')).toBeInTheDocument() // Loading spinner
  })

  it('displays empty state when no sites are available', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue([])

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByText('No sites configured')).toBeInTheDocument()
    })

    expect(screen.getByText(/Get started by adding your first WooCommerce/)).toBeInTheDocument()
    expect(screen.getByText('Add Your First Site')).toBeInTheDocument()
  })

  it('displays sites table when sites are available', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue(mockSitesData)
    vi.mocked(sitesApiService.testMultipleConnections).mockResolvedValue([
      { site_id: 1, status: ConnectionStatus.Connected, tested_at: new Date().toISOString() },
      { site_id: 2, status: ConnectionStatus.Error, tested_at: new Date().toISOString() }
    ])

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByTestId('sites-table')).toBeInTheDocument()
    })

    expect(screen.getByText('Sites count: 2')).toBeInTheDocument()
    expect(screen.getByText('Test Site 1')).toBeInTheDocument()
    expect(screen.getByText('Test Site 2')).toBeInTheDocument()
  })

  it('handles API errors gracefully', async () => {
    const errorMessage = 'Failed to load sites'
    vi.mocked(sitesApiService.getAllWithStatus).mockRejectedValue(new Error(errorMessage))

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByText(`Error: ${errorMessage}`)).toBeInTheDocument()
    })
  })

  it('refreshes all sites when refresh button is clicked', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue(mockSitesData)
    vi.mocked(sitesApiService.testMultipleConnections).mockResolvedValue([
      { site_id: 1, status: ConnectionStatus.Connected, tested_at: new Date().toISOString() }
    ])

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByTestId('sites-table')).toBeInTheDocument()
    })

    const refreshButton = screen.getByText('Refresh All')
    fireEvent.click(refreshButton)

    await waitFor(() => {
      expect(screen.getByText('Refreshing: true')).toBeInTheDocument()
    })

    expect(sitesApiService.testMultipleConnections).toHaveBeenCalledWith([1, 2])
  })

  it('tests individual site connection', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue([mockSitesData[0]])
    vi.mocked(sitesApiService.testConnection).mockResolvedValue({
      site_id: 1,
      status: ConnectionStatus.Connected,
      tested_at: new Date().toISOString()
    })

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByTestId('sites-table')).toBeInTheDocument()
    })

    const testButton = screen.getByText('Test 1')
    fireEvent.click(testButton)

    expect(sitesApiService.testConnection).toHaveBeenCalledWith(1)
  })

  it('disables refresh button when no sites available', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue([])

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByText('No sites configured')).toBeInTheDocument()
    })

    const refreshButton = screen.getByText('Refresh All')
    expect(refreshButton).toBeDisabled()
  })

  it('auto-refreshes connections after loading sites', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue(mockSitesData)
    vi.mocked(sitesApiService.testMultipleConnections).mockResolvedValue([])

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByTestId('sites-table')).toBeInTheDocument()
    })

    // Wait for auto-refresh to trigger
    await waitFor(() => {
      expect(sitesApiService.testMultipleConnections).toHaveBeenCalled()
    }, { timeout: 2000 })
  })

  it('handles connection test errors for individual sites', async () => {
    vi.mocked(sitesApiService.getAllWithStatus).mockResolvedValue([mockSitesData[0]])
    vi.mocked(sitesApiService.testConnection).mockRejectedValue(new Error('Connection failed'))

    render(<SitesPage />)

    await waitFor(() => {
      expect(screen.getByTestId('sites-table')).toBeInTheDocument()
    })

    const testButton = screen.getByText('Test 1')
    fireEvent.click(testButton)

    await waitFor(() => {
      expect(sitesApiService.testConnection).toHaveBeenCalledWith(1)
    })

    // The error should be handled gracefully and site status updated
    expect(screen.getByTestId('sites-table')).toBeInTheDocument()
  })
})