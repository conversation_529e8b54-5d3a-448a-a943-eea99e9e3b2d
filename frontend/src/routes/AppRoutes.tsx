import React, { Suspense } from 'react'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ProtectedRoute } from './ProtectedRoute'
import { LoadingSpinner } from '../components/common/LoadingSpinner'
import { AppShell } from '../components/layout/AppShell'

// Lazy load components for code splitting
const LoginPage = React.lazy(() => import('../pages/auth/LoginPage'))
const DashboardPage = React.lazy(() => import('../pages/dashboard/DashboardPage'))
const SitesPage = React.lazy(() => import('../pages/sites/SitesPage'))
const ProfilesPage = React.lazy(() => import('../pages/profiles/ProfilesPage'))
const ProcessingPage = React.lazy(() => import('../pages/processing/ProcessingPage'))
const HistoryPage = React.lazy(() => import('../pages/history/HistoryPage'))
const NotFoundPage = React.lazy(() => import('../pages/errors/NotFoundPage'))

// Loading fallback component for full page loading
const PageLoader = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner size="lg" />
  </div>
)

// Loading fallback component for app shell content
const AppShellLoader = () => (
  <div className="flex items-center justify-center min-h-[400px]">
    <LoadingSpinner size="lg" />
  </div>
)

// Props interface - currently no props needed

export const AppRoutes: React.FC = () => {
  return (
    <BrowserRouter>
      <Routes>
        {/* Public Routes */}
        <Route 
          path="/login" 
          element={
            <Suspense fallback={<PageLoader />}>
              <LoginPage />
            </Suspense>
          } 
        />
        
        {/* Protected Routes with AppShell */}
        <Route
          path="/dashboard"
          element={
            <ProtectedRoute>
              <AppShell>
                <Suspense fallback={<AppShellLoader />}>
                  <DashboardPage />
                </Suspense>
              </AppShell>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/sites"
          element={
            <ProtectedRoute>
              <AppShell>
                <Suspense fallback={<AppShellLoader />}>
                  <SitesPage />
                </Suspense>
              </AppShell>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/profiles"
          element={
            <ProtectedRoute>
              <AppShell>
                <Suspense fallback={<AppShellLoader />}>
                  <ProfilesPage />
                </Suspense>
              </AppShell>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/processing"
          element={
            <ProtectedRoute>
              <AppShell>
                <Suspense fallback={<AppShellLoader />}>
                  <ProcessingPage />
                </Suspense>
              </AppShell>
            </ProtectedRoute>
          }
        />
        
        <Route
          path="/history"
          element={
            <ProtectedRoute>
              <AppShell>
                <Suspense fallback={<AppShellLoader />}>
                  <HistoryPage />
                </Suspense>
              </AppShell>
            </ProtectedRoute>
          }
        />

        {/* Redirects */}
        <Route path="/" element={<Navigate to="/dashboard" replace />} />
        
        {/* 404 Page */}
        <Route 
          path="*" 
          element={
            <Suspense fallback={<PageLoader />}>
              <NotFoundPage />
            </Suspense>
          } 
        />
      </Routes>
    </BrowserRouter>
  )
}

AppRoutes.displayName = 'AppRoutes'

export default AppRoutes