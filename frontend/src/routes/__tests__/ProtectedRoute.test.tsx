import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import { render, screen } from '@testing-library/react'
import { BrowserRouter } from 'react-router-dom'
import { ReactNode } from 'react'
import { ProtectedRoute } from '../ProtectedRoute'
import { useAuth } from '../../contexts/AuthContext'

// Mock the useAuth hook
vi.mock('../../contexts/AuthContext', () => ({
  useAuth: vi.fn(),
}))

// Mock react-router-dom Navigate component and useLocation
const mockNavigate = vi.fn()
const mockLocation = {
  pathname: '/dashboard',
  search: '',
  hash: '',
  state: null,
  key: 'default',
}

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom')
  return {
    ...actual,
    Navigate: ({ to, state, replace }: { to: string; state?: any; replace?: boolean }) => {
      mockNavigate(to, state, replace)
      return <div data-testid="navigate">{`Navigating to ${to}`}</div>
    },
    useLocation: () => mockLocation,
  }
})

const mockUseAuth = useAuth as Mock

// Test wrapper
const TestWrapper = ({ children }: { children: ReactNode }) => (
  <BrowserRouter>{children}</BrowserRouter>
)

// Test child component
const TestChild = () => <div data-testid="protected-content">Protected Content</div>

describe('ProtectedRoute', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('when user is authenticated', () => {
    it('should render children', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
      })

      render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('protected-content')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument()
    })
  })

  describe('when user is not authenticated', () => {
    it('should redirect to login page', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
      })

      render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('navigate')).toBeInTheDocument()
      expect(screen.getByText('Navigating to /login')).toBeInTheDocument()
      expect(mockNavigate).toHaveBeenCalledWith('/login', expect.any(Object), true)
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })

    it('should pass current location as state to login redirect', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
      })

      // Mock useLocation to return a specific location
      const mockLocation = { pathname: '/dashboard', search: '?tab=1' }
      vi.mock('react-router-dom', async () => {
        const actual = await vi.importActual('react-router-dom')
        return {
          ...actual,
          useLocation: () => mockLocation,
          Navigate: ({ to, state, replace }: { to: string; state?: any; replace?: boolean }) => {
            mockNavigate(to, state, replace)
            return <div data-testid="navigate">{`Navigating to ${to}`}</div>
          },
        }
      })

      render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(mockNavigate).toHaveBeenCalledWith(
        '/login',
        { from: mockLocation },
        true
      )
    })
  })

  describe('when authentication is loading', () => {
    it('should show loading spinner', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
      })

      render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
      expect(screen.queryByTestId('navigate')).not.toBeInTheDocument()
    })
  })

  describe('authentication state transitions', () => {
    it('should handle loading to authenticated transition', () => {
      const { rerender } = render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      // Initially loading
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
      })

      rerender(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()

      // Then authenticated
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
      })

      rerender(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('protected-content')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
    })

    it('should handle loading to unauthenticated transition', () => {
      const { rerender } = render(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      // Initially loading
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: true,
      })

      rerender(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()

      // Then not authenticated
      mockUseAuth.mockReturnValue({
        isAuthenticated: false,
        isLoading: false,
      })

      rerender(
        <TestWrapper>
          <ProtectedRoute>
            <TestChild />
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('navigate')).toBeInTheDocument()
      expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument()
      expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument()
    })
  })

  describe('component behavior', () => {
    it('should have correct displayName', () => {
      expect(ProtectedRoute.displayName).toBe('ProtectedRoute')
    })

    it('should render multiple children when authenticated', () => {
      mockUseAuth.mockReturnValue({
        isAuthenticated: true,
        isLoading: false,
      })

      render(
        <TestWrapper>
          <ProtectedRoute>
            <div data-testid="child-1">Child 1</div>
            <div data-testid="child-2">Child 2</div>
          </ProtectedRoute>
        </TestWrapper>
      )

      expect(screen.getByTestId('child-1')).toBeInTheDocument()
      expect(screen.getByTestId('child-2')).toBeInTheDocument()
    })
  })
})