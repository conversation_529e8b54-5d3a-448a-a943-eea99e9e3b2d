import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import { AxiosResponse, AxiosError } from 'axios'
import { authService } from '../auth'
import { apiClient } from '../client'
import type { LoginRequest, AuthResponse, User } from '../../../types/auth'

// Mock the API client
vi.mock('../client', () => ({
  apiClient: {
    post: vi.fn(),
    get: vi.fn(),
    setTokens: vi.fn(),
    getRefreshToken: vi.fn(),
    clearTokens: vi.fn(),
    isTokenAvailable: vi.fn(),
  },
}))

const mockApiClient = apiClient as {
  post: Mock
  get: Mock
  setTokens: Mock
  getRefreshToken: Mock
  clearTokens: Mock
  isTokenAvailable: Mock
}

// Mock data
const mockUser: User = {
  id: '1',
  email: '<EMAIL>',
  name: 'Test User',
  role: 'admin',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
}

const mockAuthResponse: AuthResponse = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  expires_in: 3600,
  user: mockUser,
}

const mockLoginRequest: LoginRequest = {
  email: '<EMAIL>',
  password: 'password123',
}

// Helper to create mock axios response
const createMockResponse = <T>(data: T): AxiosResponse<T> => ({
  data,
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {} as any,
})

// Helper to create mock axios error
const createMockError = (status: number, message: string): AxiosError => ({
  name: 'AxiosError',
  message,
  response: {
    status,
    statusText: status === 401 ? 'Unauthorized' : 'Error',
    data: { message },
    headers: {},
    config: {} as any,
  },
  config: {} as any,
  code: undefined,
  isAxiosError: true,
  toJSON: () => ({}),
})

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('login', () => {
    it('should login successfully and store tokens', async () => {
      const mockResponse = createMockResponse(mockAuthResponse)
      mockApiClient.post.mockResolvedValue(mockResponse)

      const result = await authService.login(mockLoginRequest)

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/login', mockLoginRequest)
      expect(mockApiClient.setTokens).toHaveBeenCalledWith(
        mockAuthResponse.access_token,
        mockAuthResponse.refresh_token
      )
      expect(result).toEqual(mockAuthResponse)
    })

    it('should handle 401 unauthorized error', async () => {
      const mockError = createMockError(401, 'Unauthorized')
      mockApiClient.post.mockRejectedValue(mockError)

      await expect(authService.login(mockLoginRequest)).rejects.toThrow(
        'Invalid email or password'
      )
    })

    it('should handle 422 validation error', async () => {
      const mockError = createMockError(422, 'Validation error')
      mockApiClient.post.mockRejectedValue(mockError)

      await expect(authService.login(mockLoginRequest)).rejects.toThrow(
        'Please check your email and password format'
      )
    })

    it('should handle 500 server error', async () => {
      const mockError = createMockError(500, 'Internal server error')
      mockApiClient.post.mockRejectedValue(mockError)

      await expect(authService.login(mockLoginRequest)).rejects.toThrow(
        'Server error. Please try again later.'
      )
    })

    it('should handle generic error', async () => {
      const mockError = createMockError(400, 'Bad request')
      mockApiClient.post.mockRejectedValue(mockError)

      await expect(authService.login(mockLoginRequest)).rejects.toThrow(
        'Login failed. Please try again.'
      )
    })

    it('should handle network error', async () => {
      const networkError = new Error('Network Error')
      mockApiClient.post.mockRejectedValue(networkError)

      await expect(authService.login(mockLoginRequest)).rejects.toThrow(
        'Login failed. Please try again.'
      )
    })
  })

  describe('logout', () => {
    it('should logout successfully and clear tokens', async () => {
      mockApiClient.post.mockResolvedValue(createMockResponse({}))

      await authService.logout()

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/logout')
      expect(mockApiClient.clearTokens).toHaveBeenCalled()
    })

    it('should clear tokens even if API call fails', async () => {
      const mockError = createMockError(500, 'Server error')
      mockApiClient.post.mockRejectedValue(mockError)

      // Should not throw
      await expect(authService.logout()).resolves.toBeUndefined()
      expect(mockApiClient.clearTokens).toHaveBeenCalled()
    })
  })

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      mockApiClient.getRefreshToken.mockReturnValue('mock-refresh-token')
      mockApiClient.post.mockResolvedValue(createMockResponse(mockAuthResponse))

      const result = await authService.refreshToken()

      expect(mockApiClient.post).toHaveBeenCalledWith('/auth/refresh', {
        refresh_token: 'mock-refresh-token',
      })
      expect(mockApiClient.setTokens).toHaveBeenCalledWith(
        mockAuthResponse.access_token,
        mockAuthResponse.refresh_token
      )
      expect(result).toEqual(mockAuthResponse)
    })

    it('should throw error when no refresh token available', async () => {
      mockApiClient.getRefreshToken.mockReturnValue(null)

      await expect(authService.refreshToken()).rejects.toThrow(
        'No refresh token available'
      )

      expect(mockApiClient.post).not.toHaveBeenCalled()
    })

    it('should clear tokens on refresh failure', async () => {
      mockApiClient.getRefreshToken.mockReturnValue('mock-refresh-token')
      const mockError = createMockError(401, 'Invalid refresh token')
      mockApiClient.post.mockRejectedValue(mockError)

      await expect(authService.refreshToken()).rejects.toThrow()
      expect(mockApiClient.clearTokens).toHaveBeenCalled()
    })
  })

  describe('getCurrentUser', () => {
    it('should get current user successfully', async () => {
      mockApiClient.get.mockResolvedValue(createMockResponse(mockUser))

      const result = await authService.getCurrentUser()

      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/me')
      expect(result).toEqual(mockUser)
    })

    it('should handle 401 error', async () => {
      const mockError = createMockError(401, 'Unauthorized')
      mockApiClient.get.mockRejectedValue(mockError)

      await expect(authService.getCurrentUser()).rejects.toThrow(
        'Authentication required'
      )
    })

    it('should handle generic error', async () => {
      const mockError = createMockError(500, 'Server error')
      mockApiClient.get.mockRejectedValue(mockError)

      await expect(authService.getCurrentUser()).rejects.toThrow(
        'Failed to get user profile'
      )
    })
  })

  describe('isAuthenticated', () => {
    it('should return true when token is available', () => {
      mockApiClient.isTokenAvailable.mockReturnValue(true)

      const result = authService.isAuthenticated()

      expect(result).toBe(true)
      expect(mockApiClient.isTokenAvailable).toHaveBeenCalled()
    })

    it('should return false when no token is available', () => {
      mockApiClient.isTokenAvailable.mockReturnValue(false)

      const result = authService.isAuthenticated()

      expect(result).toBe(false)
      expect(mockApiClient.isTokenAvailable).toHaveBeenCalled()
    })
  })

  describe('validateAuthentication', () => {
    it('should return true for valid authentication', async () => {
      mockApiClient.isTokenAvailable.mockReturnValue(true)
      mockApiClient.get.mockResolvedValue(createMockResponse(mockUser))

      const result = await authService.validateAuthentication()

      expect(result).toBe(true)
      expect(mockApiClient.get).toHaveBeenCalledWith('/auth/me')
    })

    it('should return false when no token is available', async () => {
      mockApiClient.isTokenAvailable.mockReturnValue(false)

      const result = await authService.validateAuthentication()

      expect(result).toBe(false)
      expect(mockApiClient.get).not.toHaveBeenCalled()
    })

    it('should return false and clear tokens when user request fails', async () => {
      mockApiClient.isTokenAvailable.mockReturnValue(true)
      const mockError = createMockError(401, 'Token invalid')
      mockApiClient.get.mockRejectedValue(mockError)

      const result = await authService.validateAuthentication()

      expect(result).toBe(false)
      expect(mockApiClient.clearTokens).toHaveBeenCalled()
    })
  })
})