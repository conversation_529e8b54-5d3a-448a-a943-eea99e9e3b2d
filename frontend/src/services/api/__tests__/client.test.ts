import { describe, it, expect, beforeEach, vi, Mock } from 'vitest'
import axios, { AxiosResponse, AxiosError } from 'axios'
import { ApiClient } from '../client'

// Mock axios
vi.mock('axios', () => ({
  default: {
    create: vi.fn(),
    post: vi.fn(),
  },
}))
const mockAxios = vi.mocked(axios)

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}
Object.defineProperty(window, 'localStorage', { value: mockLocalStorage })

// Mock window.location
Object.defineProperty(window, 'location', {
  value: { href: '' },
  writable: true,
})

describe('ApiClient', () => {
  let apiClient: ApiClient
  let mockAxiosInstance: any

  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockClear()
    mockLocalStorage.setItem.mockClear()
    mockLocalStorage.removeItem.mockClear()

    // Mock axios instance
    mockAxiosInstance = {
      interceptors: {
        request: { use: vi.fn() },
        response: { use: vi.fn() },
      },
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
      patch: vi.fn(),
    }

    mockAxios.create.mockReturnValue(mockAxiosInstance)
    mockAxios.post.mockImplementation(mockAxiosInstance.post)
  })

  // Only create the client after the mocks are set up
  const createClient = () => new ApiClient()

  describe('initialization', () => {
    it('should create axios instance with correct config', () => {
      createClient()
      expect(mockAxios.create).toHaveBeenCalledWith({
        baseURL: 'http://localhost:8003/api',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      })
    })

    it('should set up request and response interceptors', () => {
      createClient()
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled()
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled()
    })
  })

  describe('token management', () => {
    beforeEach(() => {
      apiClient = createClient()
    })

    it('should set tokens in localStorage', () => {
      const authToken = 'auth-token'
      const refreshToken = 'refresh-token'

      apiClient.setTokens(authToken, refreshToken)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('auth_token', authToken)
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refresh_token', refreshToken)
    })

    it('should get auth token from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('stored-token')

      const token = apiClient.getAuthToken()

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('auth_token')
      expect(token).toBe('stored-token')
    })

    it('should get refresh token from localStorage', () => {
      mockLocalStorage.getItem.mockReturnValue('stored-refresh-token')

      const token = apiClient.getRefreshToken()

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith('refresh_token')
      expect(token).toBe('stored-refresh-token')
    })

    it('should clear tokens from localStorage', () => {
      apiClient.clearTokens()

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth_token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token')
    })

    it('should check if token is available', () => {
      mockLocalStorage.getItem.mockReturnValue('token')
      expect(apiClient.isTokenAvailable()).toBe(true)

      mockLocalStorage.getItem.mockReturnValue(null)
      expect(apiClient.isTokenAvailable()).toBe(false)
    })
  })

  describe('HTTP methods', () => {
    beforeEach(() => {
      apiClient = createClient()
    })

    it('should call get method', async () => {
      const mockResponse = { data: { test: 'data' } }
      mockAxiosInstance.get.mockResolvedValue(mockResponse)

      const result = await apiClient.get('/test')

      expect(mockAxiosInstance.get).toHaveBeenCalledWith('/test', undefined)
      expect(result).toEqual(mockResponse)
    })

    it('should call post method', async () => {
      const mockResponse = { data: { created: true } }
      const postData = { name: 'test' }
      mockAxiosInstance.post.mockResolvedValue(mockResponse)

      const result = await apiClient.post('/test', postData)

      expect(mockAxiosInstance.post).toHaveBeenCalledWith('/test', postData, undefined)
      expect(result).toEqual(mockResponse)
    })

    it('should call put method', async () => {
      const mockResponse = { data: { updated: true } }
      const putData = { id: 1, name: 'updated' }
      mockAxiosInstance.put.mockResolvedValue(mockResponse)

      const result = await apiClient.put('/test/1', putData)

      expect(mockAxiosInstance.put).toHaveBeenCalledWith('/test/1', putData, undefined)
      expect(result).toEqual(mockResponse)
    })

    it('should call delete method', async () => {
      const mockResponse = { data: { deleted: true } }
      mockAxiosInstance.delete.mockResolvedValue(mockResponse)

      const result = await apiClient.delete('/test/1')

      expect(mockAxiosInstance.delete).toHaveBeenCalledWith('/test/1', undefined)
      expect(result).toEqual(mockResponse)
    })

    it('should call patch method', async () => {
      const mockResponse = { data: { patched: true } }
      const patchData = { name: 'patched' }
      mockAxiosInstance.patch.mockResolvedValue(mockResponse)

      const result = await apiClient.patch('/test/1', patchData)

      expect(mockAxiosInstance.patch).toHaveBeenCalledWith('/test/1', patchData, undefined)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('request interceptor', () => {
    it('should add auth token to request headers', () => {
      // Get the request interceptor
      const requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0]
      
      mockLocalStorage.getItem.mockReturnValue('test-token')

      const config = {
        headers: {},
      }

      const result = requestInterceptor(config)

      expect(result.headers.Authorization).toBe('Bearer test-token')
    })

    it('should not add auth header when no token available', () => {
      const requestInterceptor = mockAxiosInstance.interceptors.request.use.mock.calls[0][0]
      
      mockLocalStorage.getItem.mockReturnValue(null)

      const config = {
        headers: {},
      }

      const result = requestInterceptor(config)

      expect(result.headers.Authorization).toBeUndefined()
    })
  })

  describe('response interceptor', () => {
    let responseInterceptor: any
    let responseErrorInterceptor: any

    beforeEach(() => {
      // Get the response interceptors
      const interceptorCall = mockAxiosInstance.interceptors.response.use.mock.calls[0]
      responseInterceptor = interceptorCall[0]
      responseErrorInterceptor = interceptorCall[1]
    })

    it('should pass through successful responses', () => {
      const mockResponse = { data: { test: 'data' }, status: 200 }

      const result = responseInterceptor(mockResponse)

      expect(result).toEqual(mockResponse)
    })

    it('should handle non-401 errors normally', async () => {
      const mockError = {
        response: { status: 500 },
        config: {},
      }

      await expect(responseErrorInterceptor(mockError)).rejects.toEqual(mockError)
    })

    it('should attempt token refresh on 401 error', async () => {
      // Setup refresh token
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'refresh_token') return 'refresh-token'
        return null
      })

      // Mock successful refresh response
      const refreshResponse = {
        data: {
          access_token: 'new-access-token',
          refresh_token: 'new-refresh-token',
        },
      }
      mockAxios.post.mockResolvedValue(refreshResponse)

      // Mock successful retry
      const retryResponse = { data: { success: true } }
      mockAxiosInstance.post.mockResolvedValue(retryResponse)

      const mockError = {
        response: { status: 401 },
        config: {
          headers: {},
        },
      }

      const result = await responseErrorInterceptor(mockError)

      expect(mockAxios.post).toHaveBeenCalledWith('http://localhost:8003/api/auth/refresh', {
        refresh_token: 'refresh-token',
      })
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('auth_token', 'new-access-token')
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('refresh_token', 'new-refresh-token')
    })

    it('should redirect to login when refresh fails', async () => {
      mockLocalStorage.getItem.mockReturnValue(null) // No refresh token

      const mockError = {
        response: { status: 401 },
        config: { headers: {} },
      }

      await expect(responseErrorInterceptor(mockError)).rejects.toBeDefined()
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('auth_token')
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith('refresh_token')
      expect(window.location.href).toBe('/login')
    })
  })
})