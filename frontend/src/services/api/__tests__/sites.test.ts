import { describe, it, expect, beforeEach, vi } from 'vitest'
import { sitesApiService, SitesApiService } from '../sites'
import { apiClient } from '../client'
import type { Site, SiteCreateRequest, SiteUpdateRequest } from '../../../types/sites'
import { ConnectionStatus } from '../../../types/sites'

// Mock the API client
vi.mock('../client', () => ({
  apiClient: {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn()
  }
}))

const mockSite: Site = {
  id: 1,
  name: 'test-site',
  display_name: 'Test Site',
  description: 'Test description',
  db_host: 'localhost',
  db_port: 3306,
  db_name: 'test_db',
  db_user: 'test_user',
  db_password: 'test_password',
  db_table_prefix: 'wp_',
  created_at: '2023-01-01T00:00:00Z',
  updated_at: '2023-01-01T01:00:00Z'
}

const mockSitesResponse = {
  sites: [mockSite],
  total: 1,
  page: 1,
  per_page: 50
}

describe('SitesApiService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('getAll', () => {
    it('fetches all sites with default pagination', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockSitesResponse })

      const result = await sitesApiService.getAll()

      expect(apiClient.get).toHaveBeenCalledWith('/sites/?page=1&per_page=50')
      expect(result).toEqual(mockSitesResponse)
    })

    it('fetches sites with custom pagination', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockSitesResponse })

      await sitesApiService.getAll(2, 25)

      expect(apiClient.get).toHaveBeenCalledWith('/sites/?page=2&per_page=25')
    })

    it('handles API errors', async () => {
      const errorMessage = 'Network error'
      vi.mocked(apiClient.get).mockRejectedValue(new Error(errorMessage))

      await expect(sitesApiService.getAll()).rejects.toThrow('Failed to fetch sites')
    })
  })

  describe('getAllWithStatus', () => {
    it('returns sites with default connection status', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockSitesResponse })

      const result = await sitesApiService.getAllWithStatus()

      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        ...mockSite,
        connection_status: ConnectionStatus.Testing,
        last_tested: undefined,
        connection_error: undefined
      })
    })
  })

  describe('testConnection', () => {
    it('tests connection for a specific site', async () => {
      const mockTestResult = {
        site_id: 1,
        status: ConnectionStatus.Connected,
        tested_at: '2023-01-01T02:00:00Z'
      }
      vi.mocked(apiClient.post).mockResolvedValue({ data: mockTestResult })

      const result = await sitesApiService.testConnection(1)

      expect(apiClient.post).toHaveBeenCalledWith('/sites/1/test')
      expect(result).toEqual(mockTestResult)
    })

    it('handles connection test errors', async () => {
      vi.mocked(apiClient.post).mockRejectedValue(new Error('Connection timeout'))

      await expect(sitesApiService.testConnection(1)).rejects.toThrow('Failed to test connection for site 1')
    })
  })

  describe('testMultipleConnections', () => {
    it('tests connections for multiple sites', async () => {
      const mockTestResults = [
        { site_id: 1, status: ConnectionStatus.Connected, tested_at: '2023-01-01T02:00:00Z' },
        { site_id: 2, status: ConnectionStatus.Error, tested_at: '2023-01-01T02:00:00Z' }
      ]
      
      vi.mocked(apiClient.post)
        .mockResolvedValueOnce({ data: mockTestResults[0] })
        .mockResolvedValueOnce({ data: mockTestResults[1] })

      const result = await sitesApiService.testMultipleConnections([1, 2])

      expect(apiClient.post).toHaveBeenCalledTimes(2)
      expect(result).toEqual(mockTestResults)
    })

    it('handles individual test failures gracefully', async () => {
      vi.mocked(apiClient.post)
        .mockResolvedValueOnce({ data: { site_id: 1, status: ConnectionStatus.Connected } })
        .mockRejectedValueOnce(new Error('Connection failed'))

      const result = await sitesApiService.testMultipleConnections([1, 2])

      expect(result).toHaveLength(2)
      expect(result[0].status).toBe(ConnectionStatus.Connected)
      expect(result[1].status).toBe(ConnectionStatus.Error)
      expect(result[1].error).toBe('Failed to test connection for site 2')
    })
  })

  describe('getById', () => {
    it('fetches a site by ID', async () => {
      vi.mocked(apiClient.get).mockResolvedValue({ data: mockSite })

      const result = await sitesApiService.getById(1)

      expect(apiClient.get).toHaveBeenCalledWith('/sites/1')
      expect(result).toEqual(mockSite)
    })

    it('handles not found errors', async () => {
      vi.mocked(apiClient.get).mockRejectedValue(new Error('Site not found'))

      await expect(sitesApiService.getById(1)).rejects.toThrow('Failed to fetch site 1')
    })
  })

  describe('create', () => {
    it('creates a new site', async () => {
      const createRequest: SiteCreateRequest = {
        name: 'new-site',
        display_name: 'New Site',
        db_host: 'localhost',
        db_name: 'new_db',
        db_user: 'new_user',
        db_password: 'new_password'
      }
      vi.mocked(apiClient.post).mockResolvedValue({ data: mockSite })

      const result = await sitesApiService.create(createRequest)

      expect(apiClient.post).toHaveBeenCalledWith('/sites', createRequest)
      expect(result).toEqual(mockSite)
    })

    it('handles creation errors', async () => {
      const createRequest: SiteCreateRequest = {
        name: 'new-site',
        display_name: 'New Site',
        db_host: 'localhost',
        db_name: 'new_db',
        db_user: 'new_user',
        db_password: 'new_password'
      }
      vi.mocked(apiClient.post).mockRejectedValue(new Error('Validation failed'))

      await expect(sitesApiService.create(createRequest)).rejects.toThrow('Failed to create site')
    })
  })

  describe('update', () => {
    it('updates an existing site', async () => {
      const updateRequest: SiteUpdateRequest = {
        display_name: 'Updated Site Name'
      }
      const updatedSite = { ...mockSite, display_name: 'Updated Site Name' }
      vi.mocked(apiClient.put).mockResolvedValue({ data: updatedSite })

      const result = await sitesApiService.update(1, updateRequest)

      expect(apiClient.put).toHaveBeenCalledWith('/sites/1', updateRequest)
      expect(result).toEqual(updatedSite)
    })

    it('handles update errors', async () => {
      const updateRequest: SiteUpdateRequest = { display_name: 'Updated Name' }
      vi.mocked(apiClient.put).mockRejectedValue(new Error('Site not found'))

      await expect(sitesApiService.update(1, updateRequest)).rejects.toThrow('Failed to update site 1')
    })
  })

  describe('delete', () => {
    it('deletes a site', async () => {
      vi.mocked(apiClient.delete).mockResolvedValue({ data: {} })

      await sitesApiService.delete(1)

      expect(apiClient.delete).toHaveBeenCalledWith('/sites/1')
    })

    it('handles deletion errors', async () => {
      vi.mocked(apiClient.delete).mockRejectedValue(new Error('Site not found'))

      await expect(sitesApiService.delete(1)).rejects.toThrow('Failed to delete site 1')
    })
  })

  describe('handleApiError', () => {
    it('extracts message from response data', async () => {
      const errorWithMessage = {
        response: {
          data: {
            message: 'Custom error message'
          }
        }
      }
      vi.mocked(apiClient.get).mockRejectedValue(errorWithMessage)

      await expect(sitesApiService.getAll()).rejects.toThrow('Custom error message')
    })

    it('extracts detail from response data', async () => {
      const errorWithDetail = {
        response: {
          data: {
            detail: 'Validation error detail'
          }
        }
      }
      vi.mocked(apiClient.get).mockRejectedValue(errorWithDetail)

      await expect(sitesApiService.getAll()).rejects.toThrow('Validation error detail')
    })

    it('falls back to default message for plain errors', async () => {
      const errorWithMessage = new Error('Network timeout')
      vi.mocked(apiClient.get).mockRejectedValue(errorWithMessage)

      await expect(sitesApiService.getAll()).rejects.toThrow('Failed to fetch sites')
    })

    it('uses default message for unknown errors', async () => {
      vi.mocked(apiClient.get).mockRejectedValue({})

      await expect(sitesApiService.getAll()).rejects.toThrow('Failed to fetch sites')
    })
  })
})