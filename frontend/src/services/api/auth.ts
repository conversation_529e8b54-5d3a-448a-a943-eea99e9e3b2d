import { apiClient } from './client'
import type { LoginRequest, AuthResponse, User } from '../../types/auth'

export class AuthService {
  /**
   * Login with email and password
   */
  async login(credentials: LoginRequest): Promise<AuthResponse> {
    try {
      const response = await apiClient.post<AuthResponse>('/auth/login', credentials)
      
      // Store tokens after successful login
      const { access_token, refresh_token } = response.data
      apiClient.setTokens(access_token, refresh_token)
      
      return response.data
    } catch (error: unknown) {
      // Handle different error types - check if error has axios response structure
      const hasResponse = error && typeof error === 'object' && 'response' in error
      const status = hasResponse ? (error as any).response?.status : null
      
      if (status === 401) {
        throw new Error('Invalid email or password')
      } else if (status === 422) {
        throw new Error('Please check your email and password format')
      } else if (status && status >= 500) {
        throw new Error('Server error. Please try again later.')
      } else {
        throw new Error('<PERSON><PERSON> failed. Please try again.')
      }
    }
  }

  /**
   * Logout - clear tokens and notify backend
   */
  async logout(): Promise<void> {
    try {
      // Attempt to notify backend about logout
      await apiClient.post('/auth/logout')
    } catch (error) {
      // Ignore logout API errors - still clear local tokens
      console.warn('Logout API call failed, but continuing with local cleanup', error)
    } finally {
      // Always clear local tokens
      apiClient.clearTokens()
    }
  }

  /**
   * Refresh authentication token
   */
  async refreshToken(): Promise<AuthResponse> {
    const refreshToken = apiClient.getRefreshToken()
    if (!refreshToken) {
      throw new Error('No refresh token available')
    }

    try {
      const response = await apiClient.post<AuthResponse>('/auth/refresh', {
        refresh_token: refreshToken,
      })

      // Store new tokens
      const { access_token, refresh_token: newRefreshToken } = response.data
      apiClient.setTokens(access_token, newRefreshToken)

      return response.data
    } catch (error) {
      // Clear tokens on refresh failure
      apiClient.clearTokens()
      throw error
    }
  }

  /**
   * Get current user profile
   */
  async getCurrentUser(): Promise<User> {
    try {
      const response = await apiClient.get<User>('/auth/me')
      return response.data
    } catch (error: unknown) {
      const hasResponse = error && typeof error === 'object' && 'response' in error
      const status = hasResponse ? (error as any).response?.status : null
      
      if (status === 401) {
        throw new Error('Authentication required')
      }
      throw new Error('Failed to get user profile')
    }
  }

  /**
   * Check if user is currently authenticated
   */
  isAuthenticated(): boolean {
    return apiClient.isTokenAvailable()
  }

  /**
   * Validate current authentication status
   */
  async validateAuthentication(): Promise<boolean> {
    if (!this.isAuthenticated()) {
      return false
    }

    try {
      await this.getCurrentUser()
      return true
    } catch {
      // Token invalid or expired
      apiClient.clearTokens()
      return false
    }
  }
}

// Create and export singleton instance
export const authService = new AuthService()