import type { AxiosResponse } from 'axios'
import { apiClient } from './client'
import type { 
  Site, 
  SiteWithStatus, 
  SiteConnectionTestResult, 
  SiteCreateRequest, 
  SiteUpdateRequest
} from '../../types/sites'
import { ConnectionStatus } from '../../types/sites'

export interface SitesResponse {
  sites: Site[]
  total: number
  page: number
  per_page: number
}

export class SitesApiService {
  private readonly basePath = '/sites'

  /**
   * Get all sites with optional pagination
   */
  async getAll(page: number = 1, perPage: number = 50): Promise<SitesResponse> {
    try {
      const response: AxiosResponse<SitesResponse> = await apiClient.get(
        `${this.basePath}/?page=${page}&per_page=${perPage}`
      )
      return response.data
    } catch (error) {
      throw this.handleApiError(error, 'Failed to fetch sites')
    }
  }

  /**
   * Get sites with connection status for dashboard display
   */
  async getAllWithStatus(): Promise<SiteWithStatus[]> {
    try {
      const sitesResponse = await this.getAll()
      const sites = sitesResponse.sites
      
      // Initialize sites with default connection status
      const sitesWithStatus: SiteWithStatus[] = sites.map(site => ({
        ...site,
        connection_status: ConnectionStatus.Testing,
        last_tested: undefined,
        connection_error: undefined
      }))

      return sitesWithStatus
    } catch (error) {
      throw this.handleApiError(error, 'Failed to fetch sites with status')
    }
  }

  /**
   * Test connection for a specific site
   */
  async testConnection(siteId: number): Promise<SiteConnectionTestResult> {
    try {
      const response: AxiosResponse<SiteConnectionTestResult> = await apiClient.post(
        `${this.basePath}/${siteId}/test`
      )
      return response.data
    } catch (error) {
      throw this.handleApiError(error, `Failed to test connection for site ${siteId}`)
    }
  }

  /**
   * Test connections for multiple sites
   */
  async testMultipleConnections(siteIds: number[]): Promise<SiteConnectionTestResult[]> {
    try {
      const testPromises = siteIds.map(siteId => 
        this.testConnection(siteId).catch(error => ({
          site_id: siteId,
          status: ConnectionStatus.Error,
          error: error.message,
          tested_at: new Date().toISOString()
        } as SiteConnectionTestResult))
      )

      const results = await Promise.all(testPromises)
      return results
    } catch (error) {
      throw this.handleApiError(error, 'Failed to test multiple connections')
    }
  }

  /**
   * Get site by ID
   */
  async getById(siteId: number): Promise<Site> {
    try {
      const response: AxiosResponse<Site> = await apiClient.get(`${this.basePath}/${siteId}`)
      return response.data
    } catch (error) {
      throw this.handleApiError(error, `Failed to fetch site ${siteId}`)
    }
  }

  /**
   * Create a new site
   */
  async create(siteData: SiteCreateRequest): Promise<Site> {
    try {
      const response: AxiosResponse<Site> = await apiClient.post(this.basePath, siteData)
      return response.data
    } catch (error) {
      throw this.handleApiError(error, 'Failed to create site')
    }
  }

  /**
   * Update an existing site
   */
  async update(siteId: number, siteData: SiteUpdateRequest): Promise<Site> {
    try {
      const response: AxiosResponse<Site> = await apiClient.put(`${this.basePath}/${siteId}`, siteData)
      return response.data
    } catch (error) {
      throw this.handleApiError(error, `Failed to update site ${siteId}`)
    }
  }

  /**
   * Delete a site
   */
  async delete(siteId: number): Promise<void> {
    try {
      await apiClient.delete(`${this.basePath}/${siteId}`)
    } catch (error) {
      throw this.handleApiError(error, `Failed to delete site ${siteId}`)
    }
  }

  /**
   * Handle API errors with consistent error structure
   */
  private handleApiError(error: any, defaultMessage: string): Error {
    if (error.response?.data?.message) {
      return new Error(error.response.data.message)
    }
    
    if (error.response?.data?.detail) {
      return new Error(error.response.data.detail)
    }

    return new Error(defaultMessage)
  }
}

// Create and export singleton instance
export const sitesApiService = new SitesApiService()