// Processing and job management types
export interface JobRun {
  id: string
  siteId: string
  profileId: string
  status: JobStatus
  productsProcessed: number
  productsTotal: number
  startTime: string
  endTime?: string
  errorMessage?: string
  results?: ProcessingResults
}

export const JobStatus = {
  Pending: 'pending',
  InProgress: 'in_progress',
  Completed: 'completed',
  Failed: 'failed',
  Cancelled: 'cancelled'
} as const

export type JobStatus = typeof JobStatus[keyof typeof JobStatus]

export interface ProcessingResults {
  processed: number
  updated: number
  errors: number
  warnings: number
  processingTime: number
  summary: string
}

export interface ProcessingPreviewRequest {
  siteId: string
  profileId: string
  productIds?: string[]
  limit?: number
}

export interface ProcessingRunRequest {
  siteId: string
  profileId: string
  productIds?: string[]
  dryRun?: boolean
}

export interface ProcessingPreview {
  productId: string
  originalContent: string
  processedContent: string
  changes: ContentChange[]
}

export interface ContentChange {
  type: 'addition' | 'deletion' | 'modification'
  content: string
  position: number
}