// Profile management types
export interface Profile {
  id: string
  name: string
  description?: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt?: string
  createdAt: string
  updatedAt: string
}

export interface ProfileCreateRequest {
  name: string
  description?: string
  model: string
  temperature: number
  maxTokens: number
  systemPrompt?: string
}

export interface ProfileUpdateRequest {
  name?: string
  description?: string
  model?: string
  temperature?: number
  maxTokens?: number
  systemPrompt?: string
}

export const ProfileModel = {
  GPT4: 'gpt-4',
  GPT35Turbo: 'gpt-3.5-turbo',
  Claude3: 'claude-3',
  Claude2: 'claude-2'
} as const

export type ProfileModel = typeof ProfileModel[keyof typeof ProfileModel]