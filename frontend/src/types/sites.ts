// Site management types - matches API schema from backend
export interface Site {
  id: number
  name: string              // Unique site identifier
  display_name: string      // Human-readable site name  
  description?: string      // Optional site description
  db_host: string          // Database host
  db_port: number          // Database port (default: 3306)
  db_name: string          // Database name
  db_user: string          // Database username
  db_password: string      // Database password (sensitive)
  db_table_prefix: string  // Database table prefix (default: "wp_")
  created_at: string       // ISO timestamp
  updated_at?: string      // ISO timestamp
}

export interface SiteConnectionTestResult {
  site_id: number
  status: ConnectionStatus
  error?: string
  error_type?: string
  tested_at?: string
}

export const ConnectionStatus = {
  Connected: 'connected',
  Error: 'error', 
  Testing: 'testing'
} as const

export type ConnectionStatus = typeof ConnectionStatus[keyof typeof ConnectionStatus]

// UI-specific site type with connection status
export interface SiteWithStatus extends Site {
  connection_status: ConnectionStatus
  last_tested?: string
  connection_error?: string
}

export interface SiteCreateRequest {
  name: string
  display_name: string
  description?: string
  db_host: string
  db_port?: number
  db_name: string
  db_user: string
  db_password: string
  db_table_prefix?: string
}

export interface SiteUpdateRequest {
  name?: string
  display_name?: string
  description?: string
  db_host?: string
  db_port?: number
  db_name?: string
  db_user?: string
  db_password?: string
  db_table_prefix?: string
}