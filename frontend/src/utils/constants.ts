// API Configuration
export const API_BASE_URL = '/api'

// Application Constants
export const APP_NAME = 'Structura AI'
export const APP_VERSION = '1.0.0'

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    REFRESH: '/auth/refresh',
    PROFILE: '/auth/profile',
  },
  SITES: {
    LIST: '/sites',
    CREATE: '/sites',
    GET: (id: string) => `/sites/${id}`,
    UPDATE: (id: string) => `/sites/${id}`,
    DELETE: (id: string) => `/sites/${id}`,
    TEST: (id: string) => `/sites/${id}/test`,
  },
  PROFILES: {
    LIST: '/profiles',
    CREATE: '/profiles',
    GET: (id: string) => `/profiles/${id}`,
    UPDATE: (id: string) => `/profiles/${id}`,
    DELETE: (id: string) => `/profiles/${id}`,
  },
  PROCESSING: {
    PREVIEW: '/processing/preview',
    RUN: '/processing/run',
    STATUS: (jobId: string) => `/processing/status/${jobId}`,
    RESULTS: (jobId: string) => `/processing/results/${jobId}`,
  },
  SYSTEM: {
    STATUS: '/system/status',
    HEALTH: '/system/health',
  },
} as const

// Default Timeout (in milliseconds)
export const DEFAULT_TIMEOUT = 10000

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'structura_auth_token',
  REFRESH_TOKEN: 'structura_refresh_token',
  USER_PREFERENCES: 'structura_user_preferences',
} as const