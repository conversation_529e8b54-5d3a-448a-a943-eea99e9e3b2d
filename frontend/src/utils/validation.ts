// Basic validation utilities
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const isValidUrl = (url: string): boolean => {
  try {
    new URL(url)
    return true
  } catch {
    return false
  }
}

export const isValidPassword = (password: string): boolean => {
  // At least 8 characters, one letter, one number
  const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/
  return passwordRegex.test(password)
}

export const isRequired = (value: string): boolean => {
  return value.trim().length > 0
}

export const minLength = (value: string, length: number): boolean => {
  return value.length >= length
}

export const maxLength = (value: string, length: number): boolean => {
  return value.length <= length
}

// Validation result type
export interface ValidationResult {
  isValid: boolean
  message?: string
}

// Validation utilities with messages
export const validateRequired = (value: string): ValidationResult => {
  return {
    isValid: isRequired(value),
    message: isRequired(value) ? undefined : 'This field is required',
  }
}

export const validateEmail = (email: string): ValidationResult => {
  if (!isRequired(email)) {
    return { isValid: false, message: 'Email is required' }
  }
  return {
    isValid: isValidEmail(email),
    message: isValidEmail(email) ? undefined : 'Please enter a valid email address',
  }
}

export const validatePassword = (password: string): ValidationResult => {
  if (!isRequired(password)) {
    return { isValid: false, message: 'Password is required' }
  }
  return {
    isValid: isValidPassword(password),
    message: isValidPassword(password) 
      ? undefined 
      : 'Password must be at least 8 characters with one letter and one number',
  }
}