[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "structura-ai"
version = "1.0.0"
description = "HTML Formatter per prodotti WooCommerce"
readme = "README.md"
requires-python = ">=3.11"
authors = [
    { name = "Structura AI Team" }
]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "PyMySQL>=1.1.0",
    "SQLAlchemy>=2.0.21",
    "python-dotenv>=1.0.0",
    "httpx>=0.24.1",
    "beautifulsoup4>=4.12.2",
    "bleach>=6.0.0",
    "portalocker>=2.7.0",
    "pydantic>=2.3.0",
    "pydantic-settings>=2.0.3",
    "click>=8.1.7",
    "fastapi>=0.104.0",
    "uvicorn>=0.24.0",
    "python-multipart>=0.0.6",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "python-json-logger>=2.0.7",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.2",
    "pytest-asyncio>=0.21.1",
    "black>=23.7.0",
    "isort>=5.12.0",
    "mypy>=1.5.1",
]

[project.scripts]
structura-ai = "structura_ai.main:main"
structura-ai-api = "structura_ai.api.main:run"

[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true