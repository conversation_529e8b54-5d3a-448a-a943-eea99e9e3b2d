# API Error Handling Documentation

## Overview

The StructuraAI API implements a comprehensive error handling system with structured error responses, centralized exception handling, and correlation IDs for request tracking.

## Error Response Format

All error responses follow a standardized format:

```json
{
    "error": true,
    "message": "Human-readable error message",
    "error_code": "MACHINE_READABLE_CODE",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "details": [
        {
            "field": "field_name",
            "message": "Field-specific error message",
            "code": "FIELD_ERROR_CODE"
        }
    ]
}
```

## Error Types

### Validation Errors (422)
```json
{
    "error": true,
    "message": "Validation error",
    "error_code": "VALIDATION_ERROR",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "field_errors": {
        "email": "Invalid email format",
        "password": "Password must be at least 8 characters"
    }
}
```

### Resource Not Found (404)
```json
{
    "error": true,
    "message": "Site not found with ID: 123",
    "error_code": "RESOURCE_NOT_FOUND",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "resource_type": "Site",
    "resource_id": "123"
}
```

### Resource Conflict (409)
```json
{
    "error": true,
    "message": "Site with name 'my-site' already exists",
    "error_code": "RESOURCE_CONFLICT",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "resource_type": "Site"
}
```

### Rate Limiting (429)
```json
{
    "error": true,
    "message": "Rate limit exceeded",
    "error_code": "RATE_LIMIT_EXCEEDED",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "retry_after": 60
}
```

### Service Unavailable (503)
```json
{
    "error": true,
    "message": "AI service temporarily unavailable",
    "error_code": "SERVICE_UNAVAILABLE",
    "correlation_id": "req-123456789",
    "timestamp": "2023-12-01T12:00:00.000Z",
    "service_name": "OpenRouter AI"
}
```

## Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `VALIDATION_ERROR` | Request validation failed | 422 |
| `RESOURCE_NOT_FOUND` | Requested resource not found | 404 |
| `RESOURCE_CONFLICT` | Resource already exists or conflict | 409 |
| `RATE_LIMIT_EXCEEDED` | Rate limit exceeded | 429 |
| `DATABASE_ERROR` | Database operation failed | 503 |
| `EXTERNAL_SERVICE_ERROR` | External service unavailable | 503 |
| `PROCESSING_ERROR` | Content processing failed | 500 |
| `CONFIGURATION_ERROR` | System configuration error | 500 |
| `INTERNAL_SERVER_ERROR` | Unexpected server error | 500 |

## Correlation IDs

Every request receives a unique correlation ID that can be used to track requests across logs and systems:

- **Request Header**: `X-Correlation-ID` (optional, generated if not provided)
- **Response Header**: `X-Correlation-ID` (always present)
- **Error Response**: `correlation_id` field (always present)

## Structured Logging

The API logs all requests with structured JSON format including:

```json
{
    "timestamp": "2023-12-01T12:00:00.000Z",
    "level": "INFO",
    "service": "structura-ai-api",
    "version": "1.0.0",
    "correlation_id": "req-123456789",
    "module": "middleware",
    "function": "dispatch",
    "line": 48,
    "message": "Request started",
    "method": "GET",
    "url": "http://api.example.com/api/sites",
    "user_agent": "curl/7.68.0",
    "client_ip": "*************"
}
```

## Exception Handling

### Custom Exception Classes

- `APIException`: Base API exception with correlation ID support
- `ValidationException`: Validation errors with field-level details
- `ResourceNotFoundException`: Resource not found errors
- `ResourceConflictException`: Resource conflict errors
- `DatabaseException`: Database-related errors
- `ExternalServiceException`: External service errors
- `RateLimitException`: Rate limiting errors
- `ProcessingException`: Content processing errors
- `ConfigurationException`: Configuration errors

### Middleware Components

1. **CorrelationIDMiddleware**: Adds correlation IDs to requests/responses
2. **RequestLoggingMiddleware**: Structured request/response logging
3. **ExceptionHandlerMiddleware**: Centralized exception handling

## Best Practices

1. **Always include correlation ID** in error reports and support requests
2. **Use appropriate HTTP status codes** for different error types
3. **Provide clear, actionable error messages** for users
4. **Include field-level validation errors** for form submissions
5. **Log errors with sufficient context** for debugging
6. **Implement retry logic** for transient errors (503, 429)
7. **Monitor error rates** and alert on unusual patterns

## Example Client Error Handling

```python
import requests

def api_request(url, **kwargs):
    try:
        response = requests.get(url, **kwargs)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.HTTPError as e:
        error_data = e.response.json()
        correlation_id = error_data.get('correlation_id')
        error_code = error_data.get('error_code')
        
        if error_code == 'RATE_LIMIT_EXCEEDED':
            retry_after = error_data.get('retry_after', 60)
            print(f"Rate limited. Retry after {retry_after} seconds")
            # Implement retry logic
        elif error_code == 'RESOURCE_NOT_FOUND':
            print(f"Resource not found: {error_data['message']}")
            # Handle not found
        else:
            print(f"API error [{correlation_id}]: {error_data['message']}")
            # Handle other errors
```