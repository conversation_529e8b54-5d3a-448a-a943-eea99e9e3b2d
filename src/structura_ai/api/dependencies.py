"""FastAPI dependency injection utilities."""

from typing import Generator

from sqlalchemy.orm import Session

from ..config.settings import get_settings
from ..database.session import get_session


def get_db() -> Generator[Session, None, None]:
    """
    Get database session dependency.
    
    Yields:
        Database session that gets closed automatically after use.
    """
    session_manager = get_session()
    db = session_manager.get_session()
    try:
        yield db
    finally:
        db.close()


def get_settings_dependency():
    """Get application settings dependency."""
    return get_settings()