"""Custom API exception classes."""

from typing import Optional, Dict, Any
from fastapi import HTTPException, status


class APIException(HTTPException):
    """Base API exception class."""
    
    def __init__(
        self,
        status_code: int,
        detail: str,
        headers: Optional[Dict[str, Any]] = None,
        error_code: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        super().__init__(status_code=status_code, detail=detail, headers=headers)
        self.error_code = error_code
        self.correlation_id = correlation_id


class ValidationException(APIException):
    """Exception for validation errors."""
    
    def __init__(
        self,
        detail: str = "Validation error",
        field_errors: Optional[Dict[str, str]] = None,
        correlation_id: Optional[str] = None
    ):
        super().__init__(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=detail,
            error_code="VALIDATION_ERROR",
            correlation_id=correlation_id
        )
        self.field_errors = field_errors or {}


class ResourceNotFoundException(APIException):
    """Exception for resource not found errors."""
    
    def __init__(
        self,
        resource_type: str,
        resource_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        detail = f"{resource_type} not found"
        if resource_id:
            detail += f" with ID: {resource_id}"
            
        super().__init__(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=detail,
            error_code="RESOURCE_NOT_FOUND",
            correlation_id=correlation_id
        )
        self.resource_type = resource_type
        self.resource_id = resource_id


class ResourceConflictException(APIException):
    """Exception for resource conflict errors (e.g., duplicate names)."""
    
    def __init__(
        self,
        resource_type: str,
        detail: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        if not detail:
            detail = f"{resource_type} already exists"
            
        super().__init__(
            status_code=status.HTTP_409_CONFLICT,
            detail=detail,
            error_code="RESOURCE_CONFLICT",
            correlation_id=correlation_id
        )
        self.resource_type = resource_type


class DatabaseException(APIException):
    """Exception for database-related errors."""
    
    def __init__(
        self,
        detail: str = "Database operation failed",
        correlation_id: Optional[str] = None
    ):
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail,
            error_code="DATABASE_ERROR",
            correlation_id=correlation_id
        )


class ExternalServiceException(APIException):
    """Exception for external service errors (AI service, etc.)."""
    
    def __init__(
        self,
        service_name: str,
        detail: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        if not detail:
            detail = f"{service_name} service unavailable"
            
        super().__init__(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail=detail,
            error_code="EXTERNAL_SERVICE_ERROR",
            correlation_id=correlation_id
        )
        self.service_name = service_name


class RateLimitException(APIException):
    """Exception for rate limiting."""
    
    def __init__(
        self,
        detail: str = "Rate limit exceeded",
        retry_after: Optional[int] = None,
        correlation_id: Optional[str] = None
    ):
        headers = {}
        if retry_after:
            headers["Retry-After"] = str(retry_after)
            
        super().__init__(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=detail,
            headers=headers,
            error_code="RATE_LIMIT_EXCEEDED",
            correlation_id=correlation_id
        )
        self.retry_after = retry_after


class ProcessingException(APIException):
    """Exception for content processing errors."""
    
    def __init__(
        self,
        detail: str = "Processing operation failed",
        job_id: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="PROCESSING_ERROR",
            correlation_id=correlation_id
        )
        self.job_id = job_id


class ConfigurationException(APIException):
    """Exception for configuration-related errors."""
    
    def __init__(
        self,
        detail: str = "Configuration error",
        config_key: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="CONFIGURATION_ERROR",
            correlation_id=correlation_id
        )
        self.config_key = config_key


class SystemErrorException(APIException):
    """Exception for system-level errors."""
    
    def __init__(
        self,
        detail: str = "System error",
        system_component: Optional[str] = None,
        correlation_id: Optional[str] = None
    ):
        super().__init__(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=detail,
            error_code="SYSTEM_ERROR",
            correlation_id=correlation_id
        )
        self.system_component = system_component