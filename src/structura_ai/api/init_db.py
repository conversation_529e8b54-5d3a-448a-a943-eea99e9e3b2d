"""Database initialization utilities."""

from sqlalchemy.orm import Session

from ..database.session import get_session
from ..models import User, UserRole
from .auth import get_password_hash


def create_default_admin_user() -> None:
    """Create default admin user if not exists."""
    session_manager = get_session()
    
    with session_manager.session_scope() as db:
        # Check if admin user already exists
        admin_user = db.query(User).filter(User.role == UserRole.ADMIN.value).first()
        
        if admin_user is None:
            # Create default admin user
            admin = User(
                username="admin",
                email="<EMAIL>",
                hashed_password=get_password_hash("admin123"),
                full_name="System Administrator",
                role=UserRole.ADMIN.value,
                is_active=True
            )
            
            db.add(admin)
            print("Default admin user created:")
            print("Username: admin")
            print("Password: admin123")
            print("IMPORTANT: Change this password in production!")
        else:
            print("Admin user already exists.")


def init_database() -> None:
    """Initialize database with default data."""
    print("Initializing database...")
    
    # Ensure tables are created
    from ..database.engine import get_engine
    engine = get_engine()
    engine.create_tables()
    
    # Create default admin user
    create_default_admin_user()
    
    print("Database initialization complete.")


if __name__ == "__main__":
    init_database()