"""API logging configuration."""

import logging
import sys
from typing import Dict, Any
from datetime import datetime

from pythonjsonlogger import jsonlogger


class CorrelationIDFilter(logging.Filter):
    """Filter to add correlation ID to log records."""
    
    def filter(self, record):
        # Add correlation ID if not present
        if not hasattr(record, 'correlation_id'):
            record.correlation_id = None
        return True


class APIJsonFormatter(jsonlogger.JsonFormatter):
    """Custom JSON formatter for API logging."""
    
    def add_fields(self, log_record: Dict[str, Any], record: logging.LogRecord, message_dict: Dict[str, Any]):
        super().add_fields(log_record, record, message_dict)
        
        # Add standard fields
        log_record['timestamp'] = datetime.utcnow().isoformat() + 'Z'
        log_record['service'] = 'structura-ai-api'
        log_record['version'] = '1.0.0'
        
        # Add log level
        log_record['level'] = record.levelname
        
        # Add module information
        log_record['module'] = record.module
        log_record['function'] = record.funcName
        log_record['line'] = record.lineno
        
        # Ensure correlation_id is present
        if not log_record.get('correlation_id'):
            log_record['correlation_id'] = getattr(record, 'correlation_id', None)


def setup_api_logging(log_level: str = "INFO") -> None:
    """
    Setup structured logging for the API.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
    """
    # Create root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear existing handlers
    root_logger.handlers.clear()
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    # Create formatter
    formatter = APIJsonFormatter(
        '%(timestamp)s %(level)s %(service)s %(version)s %(correlation_id)s %(module)s %(function)s %(line)d %(message)s'
    )
    
    console_handler.setFormatter(formatter)
    
    # Add correlation ID filter
    correlation_filter = CorrelationIDFilter()
    console_handler.addFilter(correlation_filter)
    
    # Add handler to root logger
    root_logger.addHandler(console_handler)
    
    # Configure specific loggers
    configure_api_loggers(log_level)


def configure_api_loggers(log_level: str) -> None:
    """Configure specific API component loggers."""
    
    # API logger
    api_logger = logging.getLogger("structura_ai.api")
    api_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Database logger
    db_logger = logging.getLogger("sqlalchemy.engine")
    db_logger.setLevel(logging.WARNING)  # Reduce database logging noise
    
    # FastAPI access logger
    uvicorn_access = logging.getLogger("uvicorn.access")
    uvicorn_access.setLevel(logging.WARNING)  # We handle request logging in middleware
    
    # External service loggers
    requests_logger = logging.getLogger("requests")
    requests_logger.setLevel(logging.WARNING)
    
    httpx_logger = logging.getLogger("httpx")
    httpx_logger.setLevel(logging.WARNING)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the specified name.
    
    Args:
        name: Logger name (usually __name__)
        
    Returns:
        Configured logger instance.
    """
    return logging.getLogger(name)