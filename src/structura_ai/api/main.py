"""FastAPI main application entry point."""

import uvicorn
from fastapi import <PERSON><PERSON><PERSON>, Depends
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

from ..config.settings import get_settings
from .dependencies import get_db
from .utils import check_database_health
from .routers import auth, sites, profiles, processing, system
from .middleware import CorrelationIDMiddleware, RequestLoggingMiddleware, ExceptionHandlerMiddleware
from .logging_config import setup_api_logging


def create_app() -> FastAPI:
    """
    Create and configure FastAPI application.
    
    Returns:
        Configured FastAPI application instance.
    """
    settings = get_settings()
    
    # Setup logging
    setup_api_logging(settings.log_level)
    
    # Create FastAPI app
    app = FastAPI(
        title="StructuraAI API",
        description="""
API for StructuraAI HTML formatting system for WooCommerce products.

This API provides comprehensive functionality for:
- **Authentication**: JWT-based user authentication and authorization
- **Sites Management**: WooCommerce site configuration and database connections
- **Profiles Management**: AI processing profiles with LLM configurations
- **Processing**: Content processing with background job support
- **System Management**: System health, statistics, and administration

## Authentication

All endpoints (except /api/health) require Bearer token authentication.
Use the `/api/auth/login` endpoint to obtain access tokens.
        """,
        version="1.0.0",
        debug=settings.api_debug,
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        tags_metadata=[
            {
                "name": "auth",
                "description": "Authentication and authorization endpoints. Manage users, login, and JWT tokens."
            },
            {
                "name": "sites", 
                "description": "WooCommerce site management. Configure database connections and site settings."
            },
            {
                "name": "profiles",
                "description": "AI processing profile management. Configure LLM models, prompts, and processing parameters."
            },
            {
                "name": "processing",
                "description": "Content processing operations. Run background jobs and monitor processing status."
            },
            {
                "name": "system",
                "description": "System administration and monitoring. Health checks, statistics, and system management."
            }
        ]
    )
    
    # Add middleware in correct order (last added is executed first)
    app.add_middleware(ExceptionHandlerMiddleware)
    app.add_middleware(RequestLoggingMiddleware)
    app.add_middleware(CorrelationIDMiddleware)
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.cors_origins_list,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(auth.router)
    app.include_router(sites.router)
    app.include_router(profiles.router)
    app.include_router(processing.router)
    app.include_router(system.router)
    
    # Health check endpoint
    @app.get("/api/health")
    async def health_check():
        """
        Comprehensive health check endpoint.
        
        Returns:
            Health status including database connectivity.
        """
        db_health = check_database_health()
        
        return {
            "status": "healthy" if db_health["database"] == "healthy" else "unhealthy",
            "service": "StructuraAI API",
            "version": "1.0.0",
            **db_health
        }
    
    return app


# Create the app instance
app = create_app()


def run() -> None:
    """Run the API server."""
    settings = get_settings()
    uvicorn.run(
        "structura_ai.api.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_debug,
        log_level="info"
    )