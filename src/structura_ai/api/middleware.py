"""API middleware components."""

import uuid
import time
import logging
from typing import Callable
from contextlib import asynccontextmanager

from fastapi import Request, Response, HTTPException, status
from fastapi.exceptions import RequestValidationError
from fastapi.responses import J<PERSON>NResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import <PERSON><PERSON><PERSON><PERSON>
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

from .exceptions import APIException, ValidationException, DatabaseException
from .schemas.errors import ErrorResponse, ValidationErrorResponse

logger = logging.getLogger(__name__)


class CorrelationIDMiddleware(BaseHTTPMiddleware):
    """Middleware to add correlation IDs to requests."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Generate or extract correlation ID
        correlation_id = request.headers.get("X-Correlation-ID", str(uuid.uuid4()))
        request.state.correlation_id = correlation_id
        
        # Process request
        response = await call_next(request)
        
        # Add correlation ID to response headers
        response.headers["X-Correlation-ID"] = correlation_id
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """Middleware for structured request/response logging."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        correlation_id = getattr(request.state, "correlation_id", str(uuid.uuid4()))
        
        # Log request
        logger.info(
            "Request started",
            extra={
                "correlation_id": correlation_id,
                "method": request.method,
                "url": str(request.url),
                "user_agent": request.headers.get("user-agent"),
                "client_ip": request.client.host if request.client else None,
            }
        )
        
        try:
            response = await call_next(request)
            
            # Calculate response time
            process_time = time.time() - start_time
            
            # Log successful response
            logger.info(
                "Request completed",
                extra={
                    "correlation_id": correlation_id,
                    "status_code": response.status_code,
                    "process_time": round(process_time * 1000, 2),  # ms
                    "response_size": response.headers.get("content-length"),
                }
            )
            
            # Add performance headers
            response.headers["X-Process-Time"] = str(round(process_time * 1000, 2))
            
            return response
            
        except Exception as exc:
            # Calculate response time for errors too
            process_time = time.time() - start_time
            
            # Log error
            logger.error(
                "Request failed",
                extra={
                    "correlation_id": correlation_id,
                    "process_time": round(process_time * 1000, 2),  # ms
                    "error_type": type(exc).__name__,
                    "error_message": str(exc),
                },
                exc_info=True
            )
            
            raise


class ExceptionHandlerMiddleware(BaseHTTPMiddleware):
    """Centralized exception handling middleware."""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            return await call_next(request)
        except Exception as exc:
            return await self.handle_exception(request, exc)
    
    async def handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """Handle different types of exceptions and return appropriate responses."""
        correlation_id = getattr(request.state, "correlation_id", str(uuid.uuid4()))
        
        # Handle FastAPI HTTPException
        if isinstance(exc, HTTPException):
            return await self.handle_http_exception(exc, correlation_id)
        
        # Handle our custom API exceptions
        if isinstance(exc, APIException):
            return await self.handle_api_exception(exc, correlation_id)
        
        # Handle Pydantic validation errors
        if isinstance(exc, (RequestValidationError, ValidationError)):
            return await self.handle_validation_error(exc, correlation_id)
        
        # Handle database errors
        if isinstance(exc, SQLAlchemyError):
            return await self.handle_database_error(exc, correlation_id)
        
        # Handle unexpected errors
        return await self.handle_unexpected_error(exc, correlation_id)
    
    async def handle_http_exception(self, exc: HTTPException, correlation_id: str) -> JSONResponse:
        """Handle FastAPI HTTPException."""
        error_response = ErrorResponse(
            message=exc.detail,
            error_code="HTTP_ERROR",
            correlation_id=correlation_id
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response.model_dump(),
            headers=exc.headers
        )
    
    async def handle_api_exception(self, exc: APIException, correlation_id: str) -> JSONResponse:
        """Handle custom API exceptions."""
        error_response = ErrorResponse(
            message=exc.detail,
            error_code=exc.error_code,
            correlation_id=correlation_id
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content=error_response.model_dump(),
            headers=exc.headers
        )
    
    async def handle_validation_error(
        self, 
        exc: Exception, 
        correlation_id: str
    ) -> JSONResponse:
        """Handle validation errors."""
        field_errors = {}
        
        if isinstance(exc, RequestValidationError):
            for error in exc.errors():
                field_path = ".".join(str(loc) for loc in error["loc"])
                field_errors[field_path] = error["msg"]
        
        error_response = ValidationErrorResponse(
            message="Validation error",
            correlation_id=correlation_id,
            field_errors=field_errors
        )
        
        return JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=error_response.model_dump()
        )
    
    async def handle_database_error(self, exc: SQLAlchemyError, correlation_id: str) -> JSONResponse:
        """Handle database errors."""
        logger.error(
            "Database error occurred",
            extra={"correlation_id": correlation_id},
            exc_info=True
        )
        
        error_response = ErrorResponse(
            message="Database operation failed",
            error_code="DATABASE_ERROR",
            correlation_id=correlation_id
        )
        
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content=error_response.model_dump()
        )
    
    async def handle_unexpected_error(self, exc: Exception, correlation_id: str) -> JSONResponse:
        """Handle unexpected errors."""
        logger.error(
            "Unexpected error occurred",
            extra={
                "correlation_id": correlation_id,
                "error_type": type(exc).__name__,
                "error_message": str(exc),
            },
            exc_info=True
        )
        
        error_response = ErrorResponse(
            message="Internal server error",
            error_code="INTERNAL_SERVER_ERROR",
            correlation_id=correlation_id
        )
        
        return JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=error_response.model_dump()
        )