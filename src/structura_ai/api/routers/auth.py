"""Authentication API endpoints."""

from datetime import <PERSON><PERSON><PERSON>
from typing import List

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from ...config.settings import get_settings
from ...models import User, UserRole
from ..auth import (
    authenticate_user, 
    create_access_token, 
    create_refresh_token,
    get_current_active_user,
    get_password_hash,
    require_admin,
    verify_token,
    AuthenticationError
)
from ..dependencies import get_db
from ..schemas.auth import (
    LoginRequest,
    TokenResponse,
    RefreshTokenRequest,
    UserResponse,
    UserCreateRequest,
    UserUpdateRequest,
    ChangePasswordRequest
)


router = APIRouter(prefix="/api/auth", tags=["Authentication"])


@router.post("/login", response_model=TokenResponse)
async def login(
    login_data: LoginRequest,
    db: Session = Depends(get_db)
):
    """
    User login endpoint.
    
    Args:
        login_data: Login credentials.
        db: Database session.
        
    Returns:
        JWT tokens for authentication.
        
    Raises:
        HTTPException: If authentication fails.
    """
    user = authenticate_user(db, login_data.username, login_data.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled",
        )
    
    settings = get_settings()
    access_token_expires = timedelta(minutes=settings.jwt_access_token_expire_minutes)
    
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=access_token_expires
    )
    
    refresh_token = create_refresh_token(
        data={"sub": user.username}
    )
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.jwt_access_token_expire_minutes * 60
    )


@router.post("/refresh", response_model=TokenResponse)
async def refresh_token(
    refresh_data: RefreshTokenRequest,
    db: Session = Depends(get_db)
):
    """
    Refresh JWT token.
    
    Args:
        refresh_data: Refresh token data.
        db: Database session.
        
    Returns:
        New JWT tokens.
        
    Raises:
        HTTPException: If refresh token is invalid.
    """
    payload = verify_token(refresh_data.refresh_token)
    if payload is None or payload.get("type") != "refresh":
        raise AuthenticationError("Invalid refresh token")
    
    username = payload.get("sub")
    if username is None:
        raise AuthenticationError("Invalid refresh token")
    
    user = db.query(User).filter(User.username == username).first()
    if user is None or not user.is_active:
        raise AuthenticationError("User not found or inactive")
    
    settings = get_settings()
    access_token_expires = timedelta(minutes=settings.jwt_access_token_expire_minutes)
    
    access_token = create_access_token(
        data={"sub": user.username},
        expires_delta=access_token_expires
    )
    
    refresh_token = create_refresh_token(
        data={"sub": user.username}
    )
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.jwt_access_token_expire_minutes * 60
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
):
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user.
        
    Returns:
        Current user information.
    """
    return UserResponse.model_validate(current_user)


@router.post("/logout")
async def logout():
    """
    User logout endpoint.
    
    Note: In a stateless JWT system, logout is handled client-side
    by removing the token. This endpoint exists for API completeness.
    """
    return {"message": "Successfully logged out"}


@router.post("/register", response_model=UserResponse)
async def register_user(
    user_data: UserCreateRequest,
    db: Session = Depends(get_db),
    _: User = Depends(require_admin)  # Only admins can create users
):
    """
    Register new user (admin only).
    
    Args:
        user_data: User creation data.
        db: Database session.
        _: Current admin user (for authorization).
        
    Returns:
        Created user information.
        
    Raises:
        HTTPException: If username already exists.
    """
    # Check if username already exists
    existing_user = db.query(User).filter(User.username == user_data.username).first()
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username already registered"
        )
    
    # Check if email already exists (if provided)
    if user_data.email:
        existing_email = db.query(User).filter(User.email == user_data.email).first()
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
    
    # Create new user
    hashed_password = get_password_hash(user_data.password)
    
    db_user = User(
        username=user_data.username,
        email=user_data.email,
        hashed_password=hashed_password,
        full_name=user_data.full_name,
        role=user_data.role.value,
        is_active=user_data.is_active
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    return UserResponse.model_validate(db_user)


@router.get("/users", response_model=List[UserResponse])
async def list_users(
    db: Session = Depends(get_db),
    _: User = Depends(require_admin)  # Only admins can list users
):
    """
    List all users (admin only).
    
    Args:
        db: Database session.
        _: Current admin user (for authorization).
        
    Returns:
        List of all users.
    """
    users = db.query(User).all()
    return [UserResponse.model_validate(user) for user in users]


@router.put("/users/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    db: Session = Depends(get_db),
    _: User = Depends(require_admin)  # Only admins can update users
):
    """
    Update user (admin only).
    
    Args:
        user_id: User ID to update.
        user_data: User update data.
        db: Database session.
        _: Current admin user (for authorization).
        
    Returns:
        Updated user information.
        
    Raises:
        HTTPException: If user not found.
    """
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    
    # Update user fields
    if user_data.email is not None:
        user.email = user_data.email
    if user_data.full_name is not None:
        user.full_name = user_data.full_name
    if user_data.is_active is not None:
        user.is_active = user_data.is_active
    if user_data.role is not None:
        user.role = user_data.role.value
    
    db.commit()
    db.refresh(user)
    
    return UserResponse.model_validate(user)


@router.post("/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Change user password.
    
    Args:
        password_data: Password change data.
        current_user: Current authenticated user.
        db: Database session.
        
    Returns:
        Success message.
        
    Raises:
        HTTPException: If current password is incorrect.
    """
    from ..auth import verify_password
    
    if not verify_password(password_data.current_password, current_user.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Incorrect current password"
        )
    
    # Update password
    current_user.hashed_password = get_password_hash(password_data.new_password)
    db.commit()
    
    return {"message": "Password changed successfully"}