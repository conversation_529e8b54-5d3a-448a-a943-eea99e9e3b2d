"""Processing API router."""

import uuid
import time
import logging
from typing import List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from ..dependencies import get_db
from ..auth import get_current_user
from ..schemas.processing import (
    ProcessingRequest, 
    ProcessingResponse, 
    ProcessingPreviewResponse,
    ProcessingRunResponse,
    JobResponse
)
from ..exceptions import ResourceNotFoundException, ValidationException, SystemErrorException
from ...models.user import User
from ...models.job_run import JobRun, JobStatus, JobMode
from ...models import Site, Profile
from ...services.processing_pipeline import ProcessingPipeline, ProcessingMode
from ...services.ai_service import AIService
from ...services.content_processor import ContentProcessor
from ...config.settings import get_settings

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/processing",
    tags=["processing"],
    dependencies=[Depends(get_current_user)]
)


@router.post("/preview", response_model=ProcessingPreviewResponse)
async def preview_processing(
    request: ProcessingRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Preview processing results without actually processing content.
    
    This endpoint performs a synchronous preview showing what would be processed
    without making any changes to the database.
    
    Args:
        request: Processing request data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Preview of processing results
    """
    start_time = time.time()
    
    try:
        # Validate site exists
        site = db.query(Site).filter(Site.id == request.site_id).first()
        if not site:
            raise ResourceNotFoundException(
                resource_type="Site",
                resource_id=str(request.site_id)
            )
        
        # Validate profile exists
        profile = db.query(Profile).filter(Profile.id == request.profile_id).first()
        if not profile:
            raise ResourceNotFoundException(
                resource_type="Profile",
                resource_id=str(request.profile_id)
            )
        
        # Initialize services
        settings = get_settings()
        ai_service = AIService()
        content_processor = ContentProcessor()
        
        # Create processing pipeline
        pipeline = ProcessingPipeline(
            site=site,
            profile=profile,
            ai_service=ai_service,
            content_processor=content_processor,
            skip_if_processed=request.skip_processed
        )
        
        # Run preview processing
        result = pipeline.process_batch(
            mode=ProcessingMode.PREVIEW,
            categories=request.categories,
            post_ids=request.post_ids,
            batch_size=min(request.batch_size, 5),  # Limit preview to 5 items max
            offset=0
        )
        
        # Convert product results to preview sample
        preview_sample = []
        warnings = []
        
        for product_result in result.product_results:
            sample_item = {
                "product_id": product_result.product_id,
                "product_title": product_result.product_title,
                "status": product_result.status.value,
                "content_changed": product_result.content_changed,
                "cache_hit": product_result.cache_hit
            }
            
            if product_result.error_message:
                sample_item["error_message"] = product_result.error_message
                warnings.append(f"Product {product_result.product_id}: {product_result.error_message}")
            
            if product_result.skip_reason:
                sample_item["skip_reason"] = product_result.skip_reason
            
            preview_sample.append(sample_item)
        
        # Add general warnings
        if result.guard_rail_rejections > 0:
            warnings.append(f"{result.guard_rail_rejections} products would be rejected by guard-rails")
        
        processing_time = time.time() - start_time
        
        logger.info(
            f"Preview completed for site {request.site_id}, profile {request.profile_id}: "
            f"{result.total_processed} products previewed in {processing_time:.2f}s"
        )
        
        return ProcessingPreviewResponse(
            site_id=request.site_id,
            profile_id=request.profile_id,
            estimated_products=result.total_processed,
            preview_sample=preview_sample,
            warnings=warnings,
            processing_time_seconds=processing_time
        )
        
    except (ResourceNotFoundException, ValidationException):
        raise
    except Exception as e:
        logger.error(f"Preview processing failed: {e}")
        raise SystemErrorException(
            detail=f"Preview processing failed: {str(e)}"
        )


@router.post("/run", response_model=ProcessingRunResponse)
async def run_processing(
    request: ProcessingRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Run processing synchronously (MVP mode).
    
    This endpoint performs synchronous processing, directly processing content
    and updating the database like the CLI tool does.
    
    Args:
        request: Processing request data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Processing execution results
    """
    start_time = time.time()
    
    try:
        # Validate site exists
        site = db.query(Site).filter(Site.id == request.site_id).first()
        if not site:
            raise ResourceNotFoundException(
                resource_type="Site",
                resource_id=str(request.site_id)
            )
        
        # Validate profile exists
        profile = db.query(Profile).filter(Profile.id == request.profile_id).first()
        if not profile:
            raise ResourceNotFoundException(
                resource_type="Profile",
                resource_id=str(request.profile_id)
            )
        
        # Initialize services
        settings = get_settings()
        ai_service = AIService()
        content_processor = ContentProcessor()
        
        # Create processing pipeline
        pipeline = ProcessingPipeline(
            site=site,
            profile=profile,
            ai_service=ai_service,
            content_processor=content_processor,
            skip_if_processed=request.skip_processed
        )
        
        # Run processing in RUN mode (makes actual database changes)
        result = pipeline.process_batch(
            mode=ProcessingMode.RUN,
            categories=request.categories,
            post_ids=request.post_ids,
            batch_size=request.batch_size,
            offset=0
        )
        
        # Prepare detailed product results (optional)
        product_results = None
        if result.total_processed <= 50:  # Only include details for smaller batches
            product_results = []
            for product_result in result.product_results:
                result_item = {
                    "product_id": product_result.product_id,
                    "product_title": product_result.product_title,
                    "status": product_result.status.value,
                    "content_changed": product_result.content_changed,
                    "cache_hit": product_result.cache_hit,
                    "backup_created": product_result.backup_created
                }
                
                if product_result.error_message:
                    result_item["error_message"] = product_result.error_message
                
                if product_result.skip_reason:
                    result_item["skip_reason"] = product_result.skip_reason
                
                product_results.append(result_item)
        
        processing_time = time.time() - start_time
        
        logger.info(
            f"Processing completed for site {request.site_id}, profile {request.profile_id}: "
            f"{result.successful}/{result.total_processed} successful in {processing_time:.2f}s"
        )
        
        return ProcessingRunResponse(
            site_id=request.site_id,
            profile_id=request.profile_id,
            mode="run",
            total_processed=result.total_processed,
            successful=result.successful,
            errors=result.errors,
            skipped=result.skipped,
            cache_hits=result.cache_hits,
            guard_rail_rejections=result.guard_rail_rejections,
            backup_failures=result.backup_failures,
            success_rate=result.success_rate,
            processing_time_seconds=processing_time,
            product_results=product_results
        )
        
    except (ResourceNotFoundException, ValidationException):
        raise
    except Exception as e:
        logger.error(f"Processing failed: {e}")
        raise SystemErrorException(
            detail=f"Processing failed: {str(e)}"
        )


@router.get("/jobs", response_model=List[JobResponse])
async def list_jobs(
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List processing jobs.
    
    Args:
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        status_filter: Filter jobs by status
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        List of job records
    """
    query = db.query(JobRun)
    
    if status_filter:
        # Convert string to JobStatus enum for filtering
        try:
            status_enum = JobStatus(status_filter)
            query = query.filter(JobRun.status == status_enum)
        except ValueError:
            # Invalid status filter, ignore it
            pass
    
    jobs = query.offset(skip).limit(limit).all()
    return jobs


@router.get("/jobs/{job_id}", response_model=JobResponse)
async def get_job(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get job details by ID.
    
    Args:
        job_id: Job ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Job details
    """
    job = db.query(JobRun).filter(JobRun.id == job_id).first()
    if not job:
        raise ResourceNotFoundException(
            resource_type="Job",
            resource_id=str(job_id)
        )
    return job


@router.post("/jobs/{job_id}/cancel")
async def cancel_job(
    job_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Cancel a running job.
    
    Args:
        job_id: Job ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Cancellation status
    """
    job = db.query(JobRun).filter(JobRun.id == job_id).first()
    if not job:
        raise ResourceNotFoundException(
            resource_type="Job",
            resource_id=str(job_id)
        )
    
    if job.status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
        raise ValidationException(
            detail=f"Cannot cancel job with status: {job.status.value}"
        )
    
    # TODO: Implement actual job cancellation with Celery
    job.status = JobStatus.CANCELLED
    db.commit()
    
    return {
        "job_id": job_id,
        "status": "cancelled",
        "message": "Job cancellation requested"
    }