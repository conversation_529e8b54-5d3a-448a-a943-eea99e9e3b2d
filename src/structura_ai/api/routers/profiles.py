"""Profiles management API router."""

from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text

from ..dependencies import get_db
from ..auth import get_current_user
from ..schemas.profiles import ProfileCreate, ProfileUpdate, ProfileResponse
from ..schemas.errors import NotFoundErrorResponse, ConflictErrorResponse
from ..exceptions import ResourceNotFoundException, ResourceConflictException
from ...models.user import User
from ...models.profile import Profile

router = APIRouter(
    prefix="/api/profiles",
    tags=["profiles"],
    dependencies=[Depends(get_current_user)]
)


@router.get("/", response_model=List[ProfileResponse])
async def list_profiles(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List all profiles accessible to current user.
    
    Args:
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        List of Profile objects
    """
    profiles = db.query(Profile).offset(skip).limit(limit).all()
    return profiles


@router.post(
    "/", 
    response_model=ProfileResponse, 
    status_code=status.HTTP_201_CREATED,
    responses={
        409: {"model": ConflictErrorResponse, "description": "Profile name already exists"}
    }
)
async def create_profile(
    profile: ProfileCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new profile.
    
    Args:
        profile: Profile creation data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Created Profile object
    """
    # Check if profile name already exists
    existing_profile = db.query(Profile).filter(Profile.name == profile.name).first()
    if existing_profile:
        raise ResourceConflictException(
            resource_type="Profile",
            detail=f"Profile with name '{profile.name}' already exists"
        )
    
    # Create new profile
    db_profile = Profile(**profile.model_dump())
    db.add(db_profile)
    db.commit()
    db.refresh(db_profile)
    
    return db_profile


@router.get(
    "/{profile_id}", 
    response_model=ProfileResponse,
    responses={
        404: {"model": NotFoundErrorResponse, "description": "Profile not found"}
    }
)
async def get_profile(
    profile_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get profile details by ID.
    
    Args:
        profile_id: Profile ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Profile object
    """
    profile = db.query(Profile).filter(Profile.id == profile_id).first()
    if not profile:
        raise ResourceNotFoundException(
            resource_type="Profile",
            resource_id=str(profile_id)
        )
    return profile


@router.put(
    "/{profile_id}", 
    response_model=ProfileResponse,
    responses={
        404: {"model": NotFoundErrorResponse, "description": "Profile not found"}
    }
)
async def update_profile(
    profile_id: int,
    profile_update: ProfileUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update profile by ID.
    
    Args:
        profile_id: Profile ID
        profile_update: Profile update data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Updated Profile object
    """
    profile = db.query(Profile).filter(Profile.id == profile_id).first()
    if not profile:
        raise ResourceNotFoundException(
            resource_type="Profile",
            resource_id=str(profile_id)
        )
    
    # Update profile with provided fields
    update_data = profile_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(profile, field, value)
    
    db.commit()
    db.refresh(profile)
    
    return profile


@router.delete(
    "/{profile_id}", 
    status_code=status.HTTP_204_NO_CONTENT,
    responses={
        404: {"model": NotFoundErrorResponse, "description": "Profile not found"}
    }
)
async def delete_profile(
    profile_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete profile by ID.
    
    Args:
        profile_id: Profile ID
        db: Database session
        current_user: Current authenticated user
    """
    profile = db.query(Profile).filter(Profile.id == profile_id).first()
    if not profile:
        raise ResourceNotFoundException(
            resource_type="Profile",
            resource_id=str(profile_id)
        )
    
    db.delete(profile)
    db.commit()


@router.post(
    "/{profile_id}/validate",
    responses={
        404: {"model": NotFoundErrorResponse, "description": "Profile not found"}
    }
)
async def validate_profile(
    profile_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Validate profile configuration.
    
    Args:
        profile_id: Profile ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Profile validation results with detailed analysis
    """
    profile = db.query(Profile).filter(Profile.id == profile_id).first()
    if not profile:
        raise ResourceNotFoundException(
            resource_type="Profile",
            resource_id=str(profile_id)
        )
    
    # Import here to avoid circular imports
    from ...services.profile_service import get_profile_service
    
    try:
        # Get profile service and validate
        profile_service = get_profile_service()
        
        # Convert profile to dict for validation
        profile_data = profile.to_dict()
        
        # Validate the profile
        validation_errors = profile_service.validate_profile(profile_data)
        
        if validation_errors:
            return {
                "profile_id": profile_id,
                "profile_name": profile.name,
                "validation_status": "error",
                "message": f"Profile validation failed with {len(validation_errors)} errors",
                "errors": validation_errors,
                "warnings": [],
                "validated_at": db.execute(text("SELECT NOW()")).scalar().isoformat()
            }
        else:
            # Additional checks for LLM model availability could be added here
            warnings = []
            
            # Check if LLM model is supported (basic check)
            common_models = [
                "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
                "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", 
                "claude-3.5-sonnet", "claude-3.5-haiku"
            ]
            
            if profile.llm_model not in common_models:
                warnings.append(f"LLM model '{profile.llm_model}' is not in the common models list. Verify it's supported by OpenRouter.")
            
            return {
                "profile_id": profile_id,
                "profile_name": profile.name,
                "display_name": profile.display_name,
                "validation_status": "success",
                "message": "Profile configuration is valid",
                "errors": [],
                "warnings": warnings,
                "validated_at": db.execute(text("SELECT NOW()")).scalar().isoformat(),
                "validation_details": {
                    "allowed_tags_count": len(profile.allowed_tags),
                    "allowed_attributes_count": len(profile.allowed_attributes),
                    "llm_model": profile.llm_model,
                    "temperature": profile.llm_temperature,
                    "batch_size": profile.batch_size,
                    "similarity_threshold": profile.similarity_threshold,
                    "retry_attempts": profile.retry_max_attempts
                }
            }
            
    except Exception as e:
        return {
            "profile_id": profile_id,
            "profile_name": getattr(profile, 'name', 'unknown'),
            "validation_status": "error",
            "message": "Validation failed due to system error",
            "errors": [f"System error: {str(e)}"],
            "warnings": [],
            "validated_at": None
        }