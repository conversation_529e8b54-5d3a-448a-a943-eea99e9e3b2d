"""Sites management API router."""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text

from ..dependencies import get_db
from ..auth import get_current_user
from ..schemas.sites import SiteCreate, SiteUpdate, SiteResponse
from ..schemas.errors import NotFoundErrorResponse, ConflictErrorResponse
from ..exceptions import ResourceNotFoundException, ResourceConflictException
from ...models.user import User
from ...models.site import Site

router = APIRouter(
    prefix="/api/sites",
    tags=["sites"],
    dependencies=[Depends(get_current_user)]
)


@router.get("/", response_model=List[SiteResponse])
async def list_sites(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    List all sites accessible to current user.
    
    Args:
        skip: Number of records to skip (pagination)
        limit: Maximum number of records to return
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        List of Site objects
    """
    sites = db.query(Site).offset(skip).limit(limit).all()
    return sites


@router.post(
    "/", 
    response_model=SiteResponse, 
    status_code=status.HTTP_201_CREATED,
    responses={
        409: {"model": ConflictErrorResponse, "description": "Site name already exists"}
    }
)
async def create_site(
    site: SiteCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new site.
    
    Args:
        site: Site creation data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Created Site object
    """
    # Check if site name already exists
    existing_site = db.query(Site).filter(Site.name == site.name).first()
    if existing_site:
        raise ResourceConflictException(
            resource_type="Site",
            detail=f"Site with name '{site.name}' already exists"
        )
    
    # Create new site
    db_site = Site(**site.model_dump())
    db.add(db_site)
    db.commit()
    db.refresh(db_site)
    
    return db_site


@router.get(
    "/{site_id}", 
    response_model=SiteResponse,
    responses={
        404: {"model": NotFoundErrorResponse, "description": "Site not found"}
    }
)
async def get_site(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get site details by ID.
    
    Args:
        site_id: Site ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Site object
    """
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise ResourceNotFoundException(
            resource_type="Site",
            resource_id=str(site_id)
        )
    return site


@router.put("/{site_id}", response_model=SiteResponse)
async def update_site(
    site_id: int,
    site_update: SiteUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update site by ID.
    
    Args:
        site_id: Site ID
        site_update: Site update data
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Updated Site object
    """
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise ResourceNotFoundException(
            resource_type="Site",
            resource_id=str(site_id)
        )
    
    # Update site with provided fields
    update_data = site_update.model_dump(exclude_unset=True)
    for field, value in update_data.items():
        setattr(site, field, value)
    
    db.commit()
    db.refresh(site)
    
    return site


@router.delete("/{site_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_site(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete site by ID.
    
    Args:
        site_id: Site ID
        db: Database session
        current_user: Current authenticated user
    """
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise ResourceNotFoundException(
            resource_type="Site",
            resource_id=str(site_id)
        )
    
    db.delete(site)
    db.commit()


@router.post("/{site_id}/test")
async def test_site_connection(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Test database connection for a site.
    
    Args:
        site_id: Site ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Connection test results
    """
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise ResourceNotFoundException(
            resource_type="Site",
            resource_id=str(site_id)
        )
    
    # Import here to avoid circular imports
    from ...services.woocommerce_repository import WooCommerceRepository
    
    try:
        # Create WooCommerce repository for the site
        wc_repo = WooCommerceRepository(site)
        test_result = wc_repo.test_connection()
        
        return {
            "site_id": site_id,
            **test_result
        }
        
    except Exception as e:
        # Return error in consistent format
        return {
            "site_id": site_id,
            "status": "error",
            "error": str(e),
            "error_type": type(e).__name__,
            "tested_at": None
        }


@router.get("/{site_id}/stats")
async def get_site_stats(
    site_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get site statistics.
    
    Args:
        site_id: Site ID
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Site statistics
    """
    site = db.query(Site).filter(Site.id == site_id).first()
    if not site:
        raise ResourceNotFoundException(
            resource_type="Site",
            resource_id=str(site_id)
        )
    
    # Import here to avoid circular imports
    from ...services.woocommerce_repository import WooCommerceRepository
    
    try:
        # Create WooCommerce repository for the site
        wc_repo = WooCommerceRepository(site)
        
        # Get basic statistics
        total_products = wc_repo.count_products()
        categories = wc_repo.get_categories()
        categories_count = len(categories) if categories else 0
        
        # Get processed products count (count products with _structura_processed meta)
        # Use a custom query since the method might not exist yet
        processed_query = f"""
            SELECT COUNT(DISTINCT p.ID) 
            FROM {site.db_table_prefix}posts p
            JOIN {site.db_table_prefix}postmeta pm ON p.ID = pm.post_id
            WHERE p.post_type = 'product' 
              AND p.post_status IN ('publish', 'draft')
              AND pm.meta_key = '_structura_processed'
        """
        try:
            # Use the connection from wc_repo
            processed_result = wc_repo.connection.execute_with_retry(processed_query)
            processed_products = processed_result[0][0] if processed_result else 0
        except Exception:
            processed_products = 0
        
        # Get last processing info from job runs (if available)
        # TODO: This could be enhanced with actual job history when background processing is implemented
        last_processing = None  
        
        return {
            "site_id": site_id,
            "site_name": site.name,
            "display_name": site.display_name,
            "total_products": total_products,
            "processed_products": processed_products,
            "unprocessed_products": total_products - processed_products,
            "categories": categories_count,
            "last_processing": last_processing,
            "statistics_generated_at": db.execute(text("SELECT NOW()")).scalar().isoformat()
        }
        
    except Exception as e:
        # Return error in consistent format, but still include basic site info
        return {
            "site_id": site_id,
            "site_name": site.name,
            "display_name": site.display_name,
            "total_products": 0,
            "processed_products": 0,
            "unprocessed_products": 0,
            "categories": 0,
            "last_processing": None,
            "error": str(e),
            "error_type": type(e).__name__,
            "statistics_generated_at": None
        }