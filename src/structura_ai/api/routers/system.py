"""System management API router."""

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Dict, Any, List
import logging

from ..dependencies import get_db
from ..auth import get_current_user, require_admin
from ..utils import check_database_health
from ..exceptions import ResourceNotFoundException, SystemErrorException
from ..schemas.errors import NotFoundErrorResponse, SystemErrorResponse
from ...models.user import User
from ...config.settings import get_settings
from ...services.ai_service import AIService
from ...database.engine import get_engine
from ..init_db import init_database, create_default_admin_user

router = APIRouter(
    prefix="/api/system",
    tags=["system"]
)


@router.get("/status")
async def system_status(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get comprehensive system health status.
    
    Args:
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Detailed system health information including database, AI service, and configuration
    """
    settings = get_settings()
    
    # Check database health
    db_health = check_database_health()
    db_status = db_health.get("database", "unknown")
    
    # Check AI service availability (basic check for OpenRouter API key)
    ai_service_status = "configured" if settings.openrouter_api_key else "not_configured"
    ai_service_healthy = settings.openrouter_api_key and settings.openrouter_api_key != "sk-or-..."
    
    # Get system metrics
    try:
        # Count total users
        total_users = db.execute(text("SELECT COUNT(*) FROM users")).scalar() or 0
        
        # Count total sites  
        total_sites = db.execute(text("SELECT COUNT(*) FROM sites")).scalar() or 0
        
        # Count total profiles
        total_profiles = db.execute(text("SELECT COUNT(*) FROM profiles")).scalar() or 0
        
    except Exception as e:
        logging.warning(f"Could not get system metrics: {e}")
        total_users = total_sites = total_profiles = 0
    
    # Determine overall system status
    overall_healthy = (
        db_status == "healthy" and 
        ai_service_healthy
    )
    
    return {
        "status": "healthy" if overall_healthy else "degraded",
        "timestamp": db.execute(text("SELECT NOW()")).scalar().isoformat(),
        "version": "1.0.0",
        "components": {
            "database": {
                "status": db_status,
                "details": db_health
            },
            "ai_service": {
                "status": "healthy" if ai_service_healthy else "unhealthy",
                "provider": "openrouter",
                "configured": ai_service_status == "configured"
            },
            "api": {
                "status": "healthy",
                "host": settings.api_host,
                "port": settings.api_port,
                "debug": settings.api_debug
            }
        },
        "metrics": {
            "total_users": total_users,
            "total_sites": total_sites, 
            "total_profiles": total_profiles
        },
        "environment": {
            "debug": settings.api_debug,
            "log_level": settings.log_level
        }
    }


@router.get("/stats")
async def system_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get AI service statistics including cache performance and processing metrics.
    
    Args:
        db: Database session
        current_user: Current authenticated user
    
    Returns:
        Comprehensive AI service statistics including cache hits/misses, 
        request counts, error rates, and performance metrics
    """
    try:
        # Initialize AI service to get statistics
        ai_service = AIService()
        
        # Get service statistics from runtime cache
        service_stats = ai_service.get_service_stats()
        
        # Get detailed cache statistics from database
        cache_stats = await ai_service.get_cache_stats(session=db)
        
        # Calculate additional metrics
        total_requests = service_stats.get("total_requests", 0)
        cache_hits = service_stats.get("hits", 0)
        cache_misses = service_stats.get("misses", 0)
        api_failures = service_stats.get("api_failures", 0)
        guard_rail_failures = service_stats.get("guard_rail_failures", 0)
        
        # Calculate success rate (requests that completed successfully)
        successful_requests = max(0, total_requests - api_failures - guard_rail_failures)
        success_rate = (successful_requests / total_requests * 100) if total_requests > 0 else 100
        
        # Get database cache statistics
        db_cache_total = cache_stats.get("total_entries", 0)
        db_cache_size_bytes = cache_stats.get("total_size_bytes", 0)
        avg_hit_count = cache_stats.get("average_hit_count", 0)
        
        return {
            # Request statistics
            "total_requests": total_requests,
            "successful_requests": successful_requests,
            "failed_requests": api_failures + guard_rail_failures,
            "success_rate_percent": round(success_rate, 2),
            
            # Cache performance statistics
            "cache_hits": cache_hits,
            "cache_misses": cache_misses,
            "cache_hit_rate_percent": service_stats.get("cache_hit_rate_percent", 0),
            
            # Error breakdown
            "api_failures": api_failures,
            "guard_rail_failures": guard_rail_failures,
            
            # Database cache statistics
            "database_cache": {
                "total_entries": db_cache_total,
                "total_size_bytes": db_cache_size_bytes,
                "total_size_mb": round(db_cache_size_bytes / (1024 * 1024), 2),
                "average_hit_count": round(avg_hit_count, 1),
                "expired_entries": cache_stats.get("expired_entries", 0)
            },
            
            # Service configuration
            "service_config": {
                "openrouter_client": service_stats.get("openrouter_client_info", {}),
                "guard_rail_config": service_stats.get("guard_rail_config", {})
            },
            
            # Performance notes
            "notes": {
                "cache_explanation": "High cache hit rates indicate efficient content reuse",
                "guard_rail_explanation": "Guard-rail failures indicate content that didn't meet quality/similarity thresholds",
                "api_failures_explanation": "API failures indicate OpenRouter service issues or rate limiting"
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get AI service statistics: {str(e)}")
        raise SystemErrorException(
            detail="Failed to retrieve AI service statistics",
            system_component="ai_service"
        )


@router.get("/info")
async def system_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get system version and configuration information.
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        Comprehensive system configuration and version information
    """
    settings = get_settings()
    
    return {
        "application": {
            "name": "StructuraAI API",
            "version": "1.0.0",
            "description": "HTML formatting system for WooCommerce products",
            "api_version": "v1"
        },
        "environment": {
            "mode": "development" if settings.api_debug else "production",
            "debug": settings.api_debug,
            "log_level": settings.log_level
        },
        "api_configuration": {
            "host": settings.api_host,
            "port": settings.api_port,
            "cors_enabled": bool(settings.cors_origins_list),
            "docs_enabled": True,
            "rate_limiting": False  # TODO: implement when added
        },
        "features": {
            "authentication": "JWT Bearer",
            "authorization": "Role-based (admin, user, viewer)",
            "database": "SQLAlchemy with SQLite/MySQL support",
            "ai_integration": "OpenRouter API",
            "background_processing": "Planned (Celery + Redis)",
            "websockets": "Planned",
            "caching": "Planned (Redis)",
            "logging": "Structured JSON logging",
            "error_handling": "Centralized with correlation IDs",
            "validation": "Pydantic schemas",
            "documentation": "OpenAPI/Swagger"
        },
        "integrations": {
            "ai_provider": "OpenRouter",
            "ai_configured": bool(settings.openrouter_api_key and settings.openrouter_api_key != "sk-or-..."),
            "supported_models": [
                "gpt-3.5-turbo", "gpt-4", "gpt-4-turbo", "gpt-4o", "gpt-4o-mini",
                "claude-3-haiku", "claude-3-sonnet", "claude-3-opus", 
                "claude-3.5-sonnet", "claude-3.5-haiku"
            ]
        },
        "security": {
            "jwt_enabled": True,
            "password_hashing": "bcrypt",
            "cors_configured": bool(settings.cors_origins_list),
            "https_recommended": True
        }
    }


@router.post("/init")
async def system_init(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Initialize system database and create default admin user (admin only).
    
    Creates all database tables and ensures default admin user exists.
    Safe to run multiple times - will not recreate existing data.
    
    Args:
        db: Database session
        current_user: Current authenticated admin user
    
    Returns:
        Initialization status with details of what was created/verified
    """
    try:
        logger.info(f"Admin {current_user.username} initiated system initialization")
        
        # Get database engine for table operations
        db_engine = get_engine()
        
        # Track what was done
        actions_performed = []
        
        # Ensure all tables exist
        db_engine.create_tables()
        actions_performed.append("Database tables verified/created")
        
        # Count existing users before default admin creation
        existing_users_count = db.execute(text("SELECT COUNT(*) FROM users")).scalar() or 0
        existing_admin_count = db.execute(
            text("SELECT COUNT(*) FROM users WHERE role = 'admin'")
        ).scalar() or 0
        
        # Create default admin user if needed
        try:
            create_default_admin_user()
            
            # Check if admin was created
            new_admin_count = db.execute(
                text("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            ).scalar() or 0
            
            if new_admin_count > existing_admin_count:
                actions_performed.append("Default admin user created (username: admin, password: admin123)")
            else:
                actions_performed.append("Default admin user already exists - skipped creation")
                
        except Exception as e:
            logger.warning(f"Default admin user creation issue: {str(e)}")
            actions_performed.append("Default admin user creation skipped (may already exist)")
        
        # Get final counts
        final_users_count = db.execute(text("SELECT COUNT(*) FROM users")).scalar() or 0
        final_sites_count = db.execute(text("SELECT COUNT(*) FROM sites")).scalar() or 0
        final_profiles_count = db.execute(text("SELECT COUNT(*) FROM profiles")).scalar() or 0
        final_cache_count = db.execute(text("SELECT COUNT(*) FROM aicache")).scalar() or 0
        
        logger.info(f"System initialization completed by admin {current_user.username}")
        
        return {
            "status": "initialized",
            "message": "System initialization completed successfully",
            "actions_performed": actions_performed,
            "database_status": {
                "total_users": final_users_count,
                "total_sites": final_sites_count,
                "total_profiles": final_profiles_count,
                "total_cache_entries": final_cache_count
            },
            "admin_action": {
                "performed_by": current_user.username,
                "timestamp": db.execute(text("SELECT datetime('now')")).scalar()
            },
            "warnings": [
                "Default admin password (admin123) should be changed in production",
                "Ensure all environment variables are properly configured"
            ]
        }
        
    except Exception as e:
        logger.error(f"System initialization failed: {str(e)}")
        raise SystemErrorException(
            detail="System initialization failed",
            system_component="database"
        )


@router.post("/reset")
async def system_reset(
    force: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Reset local database by dropping and recreating all tables (admin only).
    
    **WARNING**: This operation is DESTRUCTIVE and will permanently delete ALL data:
    - All users (including the current admin)
    - All sites and profiles
    - All AI cache entries
    - All job runs and processing history
    
    Use with extreme caution! Requires force=true query parameter.
    
    Args:
        force: Must be true to confirm the destructive operation
        db: Database session
        current_user: Current authenticated admin user
    
    Returns:
        Reset status with details of what was reset
    """
    if not force:
        raise SystemErrorException(
            detail="Database reset requires force=true parameter to confirm destructive operation",
            system_component="database"
        )
    
    try:
        logger.warning(f"DESTRUCTIVE OPERATION: Admin {current_user.username} initiated database reset")
        
        # Get counts before reset
        pre_reset_counts = {}
        try:
            pre_reset_counts = {
                "users": db.execute(text("SELECT COUNT(*) FROM users")).scalar() or 0,
                "sites": db.execute(text("SELECT COUNT(*) FROM sites")).scalar() or 0,
                "profiles": db.execute(text("SELECT COUNT(*) FROM profiles")).scalar() or 0,
                "ai_cache": db.execute(text("SELECT COUNT(*) FROM aicache")).scalar() or 0,
                "job_runs": db.execute(text("SELECT COUNT(*) FROM jobruns")).scalar() or 0
            }
        except Exception as e:
            logger.warning(f"Could not get pre-reset counts: {e}")
            pre_reset_counts = {"error": "Could not determine counts before reset"}
        
        # Close current session to avoid conflicts
        db.close()
        
        # Get database engine and perform reset
        db_engine = get_engine()
        db_engine.reset_database()
        
        # Recreate default admin user
        create_default_admin_user()
        
        # Get new session to verify reset
        from ...database.session import get_session
        session_manager = get_session()
        with session_manager.session_scope() as new_db:
            post_reset_counts = {
                "users": new_db.execute(text("SELECT COUNT(*) FROM users")).scalar() or 0,
                "sites": new_db.execute(text("SELECT COUNT(*) FROM sites")).scalar() or 0,
                "profiles": new_db.execute(text("SELECT COUNT(*) FROM profiles")).scalar() or 0,
                "ai_cache": new_db.execute(text("SELECT COUNT(*) FROM aicache")).scalar() or 0,
                "job_runs": new_db.execute(text("SELECT COUNT(*) FROM jobruns")).scalar() or 0
            }
            
            reset_timestamp = new_db.execute(text("SELECT datetime('now')")).scalar()
        
        logger.warning(f"Database reset completed by admin {current_user.username}")
        
        return {
            "status": "reset",
            "message": "Database reset completed successfully - ALL DATA HAS BEEN PERMANENTLY DELETED",
            "reset_details": {
                "pre_reset_counts": pre_reset_counts,
                "post_reset_counts": post_reset_counts,
                "tables_recreated": [
                    "users", "sites", "profiles", "aicache", "jobruns"
                ],
                "default_admin_created": "username: admin, password: admin123"
            },
            "admin_action": {
                "performed_by": current_user.username,
                "timestamp": reset_timestamp,
                "operation": "DESTRUCTIVE_DATABASE_RESET"
            },
            "critical_warnings": [
                "ALL PREVIOUS DATA HAS BEEN PERMANENTLY DELETED",
                "Default admin user recreated: username=admin, password=admin123",
                "You will need to re-authenticate with new admin credentials",
                "All sites, profiles, and processing history have been lost",
                "Change default admin password immediately"
            ]
        }
        
    except Exception as e:
        logger.error(f"Database reset failed: {str(e)}")
        raise SystemErrorException(
            detail="Database reset operation failed",
            system_component="database"
        )


@router.get("/logs")
async def get_logs(
    lines: int = 100,
    level: str = "INFO",
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Get recent system logs (admin only).
    
    Note: This endpoint returns currently available log handlers information 
    since logs are currently configured to output to console only.
    For production use, consider configuring file-based logging.
    
    Args:
        lines: Number of recent log lines to return (currently informational)
        level: Minimum log level to include (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        current_user: Current authenticated admin user
    
    Returns:
        System logging configuration and available log information
    """
    try:
        logger.info(f"Admin {current_user.username} requested system logs")
        
        # Get current logging configuration
        root_logger = logging.getLogger()
        api_logger = logging.getLogger("structura_ai.api")
        
        # Analyze current handlers
        handlers_info = []
        for handler in root_logger.handlers:
            handler_info = {
                "type": type(handler).__name__,
                "level": logging.getLevelName(handler.level),
                "formatter": type(handler.formatter).__name__ if handler.formatter else None,
                "stream": str(getattr(handler, 'stream', 'N/A'))
            }
            handlers_info.append(handler_info)
        
        # Get logger levels
        loggers_info = {
            "root": logging.getLevelName(root_logger.level),
            "api": logging.getLevelName(api_logger.level),
            "sqlalchemy.engine": logging.getLevelName(logging.getLogger("sqlalchemy.engine").level),
            "uvicorn.access": logging.getLevelName(logging.getLogger("uvicorn.access").level)
        }
        
        # Since we're using console logging, we can't retrieve historical logs
        # But we can provide information about the logging configuration
        return {
            "status": "logs_info_available",
            "message": "Console-based logging active - historical logs not available via API",
            "logging_configuration": {
                "handlers": handlers_info,
                "logger_levels": loggers_info,
                "total_handlers": len(handlers_info)
            },
            "request_parameters": {
                "requested_lines": lines,
                "requested_level": level,
                "actual_capability": "Configuration info only"
            },
            "recommendations": [
                "For production deployment, consider configuring file-based logging",
                "Use external log aggregation tools (ELK stack, Fluentd, etc.) for log management",
                "Monitor application logs via container orchestration tools if using Docker/Kubernetes",
                "Recent logs are available in console output where the API server is running"
            ],
            "console_logging_note": {
                "explanation": "Logs are currently sent to stdout/stderr",
                "access_method": "View console where 'uvicorn structura_ai.api.main:app' is running",
                "json_format": "All logs use structured JSON format for easy parsing"
            },
            "admin_action": {
                "performed_by": current_user.username,
                "timestamp": db.execute(text("SELECT datetime('now')")).scalar()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to retrieve logs information: {str(e)}")
        raise SystemErrorException(
            detail="Failed to retrieve system logs information",
            system_component="logging"
        )


@router.post("/cache/clear")
async def clear_cache(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Clear AI service cache (admin only).
    
    Clears both runtime cache statistics and expired database cache entries.
    
    Args:
        db: Database session
        current_user: Current authenticated admin user
    
    Returns:
        Cache clearing status with details of what was cleared
    """
    try:
        # Initialize AI service
        ai_service = AIService()
        
        # Get current statistics before clearing
        current_stats = ai_service.get_service_stats()
        
        # Clean up expired cache entries from database
        expired_removed = await ai_service.cleanup_expired_cache(session=db)
        
        # Reset runtime statistics
        ai_service.reset_stats()
        
        # Get cache statistics after clearing
        cache_stats_after = await ai_service.get_cache_stats(session=db)
        
        logger.info(f"Admin {current_user.username} cleared AI cache - expired entries removed: {expired_removed}")
        
        return {
            "status": "cleared",
            "message": "AI cache cleared successfully",
            "details": {
                "runtime_stats_reset": {
                    "previous_total_requests": current_stats.get("total_requests", 0),
                    "previous_cache_hits": current_stats.get("hits", 0),
                    "previous_cache_misses": current_stats.get("misses", 0),
                    "previous_hit_rate_percent": current_stats.get("cache_hit_rate_percent", 0)
                },
                "database_cleanup": {
                    "expired_entries_removed": expired_removed,
                    "remaining_entries": cache_stats_after.get("total_entries", 0),
                    "remaining_size_mb": round(cache_stats_after.get("total_size_bytes", 0) / (1024 * 1024), 2)
                }
            },
            "admin_action": {
                "performed_by": current_user.username,
                "timestamp": db.execute(text("SELECT datetime('now')")).scalar()
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to clear AI cache: {str(e)}")
        raise SystemErrorException(
            detail="Failed to clear AI service cache",
            system_component="ai_service"
        )