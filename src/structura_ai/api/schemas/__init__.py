"""API schemas package."""

from .auth import (
    LoginRequest,
    TokenResponse,
    RefreshTokenRequest,
    UserResponse,
    UserCreateRequest,
    UserUpdateRequest,
    ChangePasswordRequest
)

from .sites import (
    SiteBase,
    SiteCreate,
    SiteUpdate,
    SiteResponse
)

from .profiles import (
    ProfileBase,
    ProfileCreate,
    ProfileUpdate,
    ProfileResponse
)

from .processing import (
    ProcessingRequest,
    ProcessingResponse,
    ProcessingPreviewResponse,
    ProcessingRunResponse,
    JobResponse
)

from .errors import (
    ErrorResponse,
    ValidationErrorResponse,
    NotFoundErrorResponse,
    ConflictErrorResponse,
    RateLimitErrorResponse,
    ServiceUnavailableErrorResponse,
    ErrorDetail
)

__all__ = [
    # Auth schemas
    "LoginRequest",
    "TokenResponse", 
    "RefreshTokenRequest",
    "UserResponse",
    "UserCreateRequest",
    "UserUpdateRequest",
    "ChangePasswordRequest",
    
    # Sites schemas
    "SiteBase",
    "SiteCreate",
    "SiteUpdate",
    "SiteResponse",
    
    # Profiles schemas
    "ProfileBase",
    "ProfileCreate",
    "ProfileUpdate",
    "ProfileResponse",
    
    # Processing schemas
    "ProcessingRequest",
    "ProcessingResponse",
    "ProcessingPreviewResponse", 
    "ProcessingRunResponse",
    "JobResponse",
    
    # Error schemas
    "ErrorResponse",
    "ValidationErrorResponse", 
    "NotFoundErrorResponse",
    "ConflictErrorResponse",
    "RateLimitErrorResponse",
    "ServiceUnavailableErrorResponse",
    "ErrorDetail",
]