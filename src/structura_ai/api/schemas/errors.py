"""Error response schemas."""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field


class ErrorDetail(BaseModel):
    """Individual error detail."""
    field: Optional[str] = Field(None, description="Field name that caused the error")
    message: str = Field(..., description="Error message")
    code: Optional[str] = Field(None, description="Error code")


class ErrorResponse(BaseModel):
    """Standard error response schema."""
    error: bool = Field(True, description="Always true for error responses")
    message: str = Field(..., description="Main error message")
    error_code: Optional[str] = Field(None, description="Machine-readable error code")
    correlation_id: Optional[str] = Field(None, description="Request correlation ID for tracking")
    timestamp: datetime = Field(default_factory=datetime.utcnow, description="Error timestamp")
    details: Optional[List[ErrorDetail]] = Field(None, description="Detailed error information")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "Validation error",
                "error_code": "VALIDATION_ERROR",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "details": [
                    {
                        "field": "name",
                        "message": "Field is required",
                        "code": "REQUIRED_FIELD"
                    }
                ]
            }
        }


class ValidationErrorResponse(ErrorResponse):
    """Validation error response schema."""
    error_code: str = Field("VALIDATION_ERROR", description="Validation error code")
    field_errors: Optional[Dict[str, str]] = Field(None, description="Field-specific error messages")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "Invalid input data",
                "error_code": "VALIDATION_ERROR",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "field_errors": {
                    "email": "Invalid email format",
                    "password": "Password must be at least 8 characters"
                }
            }
        }


class NotFoundErrorResponse(ErrorResponse):
    """Resource not found error response schema."""
    error_code: str = Field("RESOURCE_NOT_FOUND", description="Resource not found error code")
    resource_type: Optional[str] = Field(None, description="Type of resource that was not found")
    resource_id: Optional[str] = Field(None, description="ID of resource that was not found")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "Site not found with ID: 123",
                "error_code": "RESOURCE_NOT_FOUND",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "resource_type": "Site",
                "resource_id": "123"
            }
        }


class ConflictErrorResponse(ErrorResponse):
    """Resource conflict error response schema."""
    error_code: str = Field("RESOURCE_CONFLICT", description="Resource conflict error code")
    resource_type: Optional[str] = Field(None, description="Type of resource in conflict")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "Site with name 'my-site' already exists",
                "error_code": "RESOURCE_CONFLICT",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "resource_type": "Site"
            }
        }


class RateLimitErrorResponse(ErrorResponse):
    """Rate limit error response schema."""
    error_code: str = Field("RATE_LIMIT_EXCEEDED", description="Rate limit error code")
    retry_after: Optional[int] = Field(None, description="Seconds to wait before retrying")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "Rate limit exceeded. Too many requests.",
                "error_code": "RATE_LIMIT_EXCEEDED",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "retry_after": 60
            }
        }


class ServiceUnavailableErrorResponse(ErrorResponse):
    """Service unavailable error response schema."""
    error_code: str = Field("SERVICE_UNAVAILABLE", description="Service unavailable error code")
    service_name: Optional[str] = Field(None, description="Name of unavailable service")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "AI service temporarily unavailable",
                "error_code": "SERVICE_UNAVAILABLE",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "service_name": "OpenRouter AI"
            }
        }


class SystemErrorResponse(ErrorResponse):
    """System error response schema."""
    error_code: str = Field("SYSTEM_ERROR", description="System error code")
    system_component: Optional[str] = Field(None, description="System component that failed")
    
    class Config:
        json_schema_extra = {
            "example": {
                "error": True,
                "message": "System component failure",
                "error_code": "SYSTEM_ERROR",
                "correlation_id": "req-123456789",
                "timestamp": "2023-12-01T12:00:00.000Z",
                "system_component": "database"
            }
        }