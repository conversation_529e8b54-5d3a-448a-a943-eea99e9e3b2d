"""Processing schemas."""

from typing import Optional, List, Any
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field


class ProcessingRequest(BaseModel):
    """Schema for processing requests."""
    site_id: int = Field(..., description="Site ID to process")
    profile_id: int = Field(..., description="Profile ID to use for processing")
    categories: Optional[List[int]] = Field(None, description="Category IDs to process (if None, all categories)")
    post_ids: Optional[List[int]] = Field(None, description="Specific post IDs to process (if None, process by categories)")
    batch_size: int = Field(10, gt=0, le=100, description="Number of posts to process per batch")
    skip_processed: bool = Field(True, description="Skip already processed posts")


class ProcessingResponse(BaseModel):
    """Schema for processing job creation response."""
    job_id: str = Field(..., description="Unique job identifier")
    status: str = Field(..., description="Job status")
    created_at: datetime = Field(..., description="Job creation timestamp")


class ProcessingPreviewResponse(BaseModel):
    """Schema for processing preview response."""
    site_id: int = Field(..., description="Site ID")
    profile_id: int = Field(..., description="Profile ID")
    estimated_products: int = Field(..., description="Estimated number of products to process")
    preview_sample: List[dict] = Field(..., description="Sample of processing results")
    warnings: List[str] = Field(..., description="Processing warnings")
    processing_time_seconds: float = Field(..., description="Time taken for preview")


class ProcessingRunResponse(BaseModel):
    """Schema for processing run response (synchronous mode)."""
    site_id: int = Field(..., description="Site ID")
    profile_id: int = Field(..., description="Profile ID")
    mode: str = Field(..., description="Processing mode (run)")
    total_processed: int = Field(..., description="Total products processed")
    successful: int = Field(..., description="Successfully processed products")
    errors: int = Field(..., description="Number of errors")
    skipped: int = Field(..., description="Number of skipped products")
    cache_hits: int = Field(..., description="Number of cache hits")
    guard_rail_rejections: int = Field(..., description="Number of guard-rail rejections")
    backup_failures: int = Field(..., description="Number of backup failures")
    success_rate: float = Field(..., description="Success rate (0.0-1.0)")
    processing_time_seconds: float = Field(..., description="Total processing time")
    product_results: Optional[List[dict]] = Field(None, description="Detailed product results")


class JobResponse(BaseModel):
    """Schema for job details response."""
    id: int = Field(..., description="Job ID")
    site_id: int = Field(..., description="Site ID")
    profile_id: int = Field(..., description="Profile ID")
    status: str = Field(..., description="Job status")
    batch_size: int = Field(..., description="Batch size")
    skip_processed: bool = Field(..., description="Skip processed flag")
    total_posts: Optional[int] = Field(None, description="Total posts to process")
    processed_posts: Optional[int] = Field(None, description="Number of processed posts")
    failed_posts: Optional[int] = Field(None, description="Number of failed posts")
    created_at: datetime = Field(..., description="Job creation timestamp")
    started_at: Optional[datetime] = Field(None, description="Job start timestamp")
    completed_at: Optional[datetime] = Field(None, description="Job completion timestamp")
    error_message: Optional[str] = Field(None, description="Error message if job failed")
    
    model_config = ConfigDict(from_attributes=True)