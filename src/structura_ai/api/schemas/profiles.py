"""Profile management schemas."""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field


class ProfileBase(BaseModel):
    """Base profile schema."""
    name: str = Field(..., description="Profile name (unique identifier)")
    display_name: str = Field(..., description="Display name for the profile")
    description: Optional[str] = Field(None, description="Profile description")
    
    # LLM Configuration
    llm_model: str = Field("gpt-3.5-turbo", description="Primary LLM model to use")
    llm_fallback_model: Optional[str] = Field(None, description="Fallback LLM model")
    llm_temperature: float = Field(0.0, ge=0.0, le=2.0, description="LLM temperature setting")
    llm_prompt: str = Field(..., description="LLM prompt template")
    
    # Processing Configuration
    batch_size: int = Field(10, gt=0, le=1000, description="Batch size for processing")
    similarity_threshold: float = Field(0.99, ge=0.0, le=1.0, description="Content similarity threshold")
    length_diff_threshold: int = Field(5, ge=0, description="Length difference threshold (characters)")
    skip_if_processed: bool = Field(True, description="Skip already processed items")
    
    # Retry Configuration
    retry_max_attempts: int = Field(3, ge=1, le=10, description="Maximum retry attempts")
    retry_backoff_base: float = Field(2.0, ge=1.0, le=10.0, description="Retry backoff base multiplier")
    
    # HTML Configuration
    allowed_tags: List[str] = Field(default_factory=list, description="Allowed HTML tags")
    allowed_attributes: Dict[str, List[str]] = Field(default_factory=dict, description="Allowed HTML attributes per tag")
    
    # Meta Configuration  
    processed_meta_key: str = Field("_ai_processed_v1", description="Meta key for processed flag")
    backup_meta_key: str = Field("_ai_original_content_v1", description="Meta key for backup content")
    
    # Profile Status
    is_active: bool = Field(True, description="Whether profile is active")
    version: int = Field(1, ge=1, description="Profile version number")


class ProfileCreate(ProfileBase):
    """Schema for creating a new profile."""
    pass


class ProfileUpdate(BaseModel):
    """Schema for updating an existing profile."""
    name: Optional[str] = Field(None, description="Profile name (unique identifier)")
    display_name: Optional[str] = Field(None, description="Display name for the profile")
    description: Optional[str] = Field(None, description="Profile description")
    
    # LLM Configuration
    llm_model: Optional[str] = Field(None, description="Primary LLM model to use")
    llm_fallback_model: Optional[str] = Field(None, description="Fallback LLM model")
    llm_temperature: Optional[float] = Field(None, ge=0.0, le=2.0, description="LLM temperature setting")
    llm_prompt: Optional[str] = Field(None, description="LLM prompt template")
    
    # Processing Configuration
    batch_size: Optional[int] = Field(None, gt=0, le=1000, description="Batch size for processing")
    similarity_threshold: Optional[float] = Field(None, ge=0.0, le=1.0, description="Content similarity threshold")
    length_diff_threshold: Optional[int] = Field(None, ge=0, description="Length difference threshold (characters)")
    skip_if_processed: Optional[bool] = Field(None, description="Skip already processed items")
    
    # Retry Configuration
    retry_max_attempts: Optional[int] = Field(None, ge=1, le=10, description="Maximum retry attempts")
    retry_backoff_base: Optional[float] = Field(None, ge=1.0, le=10.0, description="Retry backoff base multiplier")
    
    # HTML Configuration
    allowed_tags: Optional[List[str]] = Field(None, description="Allowed HTML tags")
    allowed_attributes: Optional[Dict[str, List[str]]] = Field(None, description="Allowed HTML attributes per tag")
    
    # Meta Configuration  
    processed_meta_key: Optional[str] = Field(None, description="Meta key for processed flag")
    backup_meta_key: Optional[str] = Field(None, description="Meta key for backup content")
    
    # Profile Status
    is_active: Optional[bool] = Field(None, description="Whether profile is active")
    version: Optional[int] = Field(None, ge=1, description="Profile version number")


class ProfileResponse(ProfileBase):
    """Schema for profile response."""
    id: int = Field(..., description="Profile ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    model_config = ConfigDict(from_attributes=True)