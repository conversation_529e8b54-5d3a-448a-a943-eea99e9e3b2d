"""Site management schemas."""

from typing import Optional
from datetime import datetime
from pydantic import BaseModel, ConfigDict, Field


class SiteBase(BaseModel):
    """Base site schema."""
    name: str = Field(..., description="Unique site identifier")
    display_name: str = Field(..., description="Human-readable site name")
    description: Optional[str] = Field(None, description="Site description")
    db_host: str = Field(..., description="Database host")
    db_port: int = Field(3306, description="Database port")
    db_name: str = Field(..., description="Database name")
    db_user: str = Field(..., description="Database username")
    db_password: str = Field(..., description="Database password")
    db_table_prefix: str = Field("wp_", description="Database table prefix")


class SiteCreate(SiteBase):
    """Schema for creating a new site."""
    pass


class SiteUpdate(BaseModel):
    """Schema for updating an existing site."""
    display_name: Optional[str] = Field(None, description="Human-readable site name")
    description: Optional[str] = Field(None, description="Site description")
    db_host: Optional[str] = Field(None, description="Database host")
    db_port: Optional[int] = Field(None, description="Database port")
    db_name: Optional[str] = Field(None, description="Database name")
    db_user: Optional[str] = Field(None, description="Database username")
    db_password: Optional[str] = Field(None, description="Database password")
    db_table_prefix: Optional[str] = Field(None, description="Database table prefix")


class SiteResponse(SiteBase):
    """Schema for site response."""
    id: int = Field(..., description="Site ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    model_config = ConfigDict(from_attributes=True)