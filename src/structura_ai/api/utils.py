"""API utility functions."""

from typing import Dict, Any

from sqlalchemy import text
from sqlalchemy.orm import Session

from ..database.session import get_session


def check_database_health() -> Dict[str, Any]:
    """
    Check database connectivity and return health status.
    
    Returns:
        Dictionary with database health information.
    """
    try:
        session_manager = get_session()
        with session_manager.session_scope() as session:
            # Simple query to test connectivity
            result = session.execute(text("SELECT 1"))
            result.fetchone()
            
            return {
                "database": "healthy",
                "status": "connected",
                "engine": str(session_manager.engine.url)
            }
    except Exception as e:
        return {
            "database": "unhealthy",
            "status": "connection_failed",
            "error": str(e)
        }