"""CLI commands for processing operations (preview and run)."""

import json
import logging
from pathlib import Path
from typing import List, Optional

import click
from sqlalchemy.orm import sessionmaker

from ..database import get_session
from ..models import Site, Profile
from ..services.processing_pipeline import ProcessingPipeline, ProcessingMode
from ..services.ai_service import AIService
from ..services.content_processor import ContentProcessor
from ..config.settings import get_settings

logger = logging.getLogger(__name__)


def parse_id_list(id_string: Optional[str]) -> Optional[List[int]]:
    """Parse comma-separated ID string into list of integers."""
    if not id_string:
        return None
    
    try:
        return [int(id_str.strip()) for id_str in id_string.split(',') if id_str.strip()]
    except ValueError as e:
        raise click.BadParameter(f"Invalid ID format: {e}")


def get_site_by_name(session, site_name: str) -> Site:
    """Get site by name or raise error."""
    site = session.query(Site).filter(Site.name == site_name).first()
    if not site:
        raise click.ClickException(f"Site '{site_name}' not found. Use 'structura site list' to see available sites.")
    return site


def get_profile_by_name(session, profile_name: str) -> Profile:
    """Get profile by name or raise error."""
    profile = session.query(Profile).filter(Profile.name == profile_name).first()
    if not profile:
        raise click.ClickException(f"Profile '{profile_name}' not found. Use 'structura profile list' to see available profiles.")
    return profile


def save_report(report_content: str, output_path: str, report_format: str) -> None:
    """Save report to file."""
    try:
        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        click.echo(f"📄 Report salvato: {output_file}")
        
    except Exception as e:
        logger.error(f"Failed to save report: {e}")
        raise click.ClickException(f"Failed to save report: {e}")


def display_batch_summary(result):
    """Display batch processing summary."""
    click.echo(f"\n📊 Riepilogo elaborazione:")
    click.echo(f"  • Prodotti elaborati: {result.total_processed}")
    click.echo(f"  • Successi: {result.successful}")
    click.echo(f"  • Errori: {result.errors}")
    click.echo(f"  • Saltati: {result.skipped}")
    click.echo(f"  • Cache hit: {result.cache_hits}")
    click.echo(f"  • Respinti (guard-rail): {result.guard_rail_rejections}")
    
    if result.backup_failures > 0:
        click.echo(f"  • Backup falliti: {result.backup_failures}")
    
    click.echo(f"  • Tempo elaborazione: {result.processing_time_seconds:.2f}s")
    click.echo(f"  • Tasso successo: {result.success_rate:.1%}")


def process_products(
    site_name: str,
    profile_name: str, 
    mode: ProcessingMode,
    categories: Optional[str] = None,
    post_ids: Optional[str] = None,
    batch_size: int = 10,
    skip_processed: bool = True,
    report_format: str = "json",
    output_path: Optional[str] = None
):
    """Core processing logic shared between preview and run commands."""
    
    # Parse input parameters
    category_ids = parse_id_list(categories)
    post_id_list = parse_id_list(post_ids)
    
    with get_session() as session:
        # Get site and profile
        site = get_site_by_name(session, site_name)
        profile = get_profile_by_name(session, profile_name)
        
        click.echo(f"🎯 Sito: {site.name}")
        click.echo(f"⚙️ Profilo: {profile.name}")
        
        if category_ids:
            click.echo(f"📂 Categorie: {category_ids}")
        if post_id_list:
            click.echo(f"📋 Post IDs: {post_id_list}")
        
        # Initialize services
        settings = get_settings()
        ai_service = AIService()
        content_processor = ContentProcessor()
        
        # Initialize processing pipeline
        pipeline = ProcessingPipeline(
            site=site,
            profile=profile,
            ai_service=ai_service,
            content_processor=content_processor,
            skip_if_processed=skip_processed
        )
        
        click.echo(f"\n🔄 Avvio elaborazione in modalità {mode.value.upper()}...")
        
        # Process batch
        try:
            result = pipeline.process_batch(
                mode=mode,
                categories=category_ids,
                post_ids=post_id_list,
                batch_size=batch_size,
                offset=0
            )
            
            # Display summary
            display_batch_summary(result)
            
            # Generate and save reports
            if 'json' in report_format.lower():
                json_report = pipeline.generate_batch_report(
                    result, 
                    include_diffs=(mode == ProcessingMode.PREVIEW),
                    report_format="json"
                )
                
                if output_path:
                    json_output = output_path.replace('.csv', '.json') if output_path.endswith('.csv') else f"{output_path}.json"
                    save_report(json_report, json_output, "json")
                else:
                    if mode == ProcessingMode.PREVIEW and result.total_processed > 0:
                        click.echo("\n📄 Report JSON (primi 5 prodotti):")
                        report_data = json.loads(json_report)
                        if len(report_data['product_results']) > 5:
                            report_data['product_results'] = report_data['product_results'][:5]
                            report_data['note'] = "Report troncato - solo primi 5 prodotti mostrati"
                        click.echo(json.dumps(report_data, indent=2, ensure_ascii=False))
            
            if 'csv' in report_format.lower():
                csv_report = pipeline.generate_batch_report(result, include_diffs=False, report_format="csv")
                
                if output_path:
                    csv_output = output_path.replace('.json', '.csv') if output_path.endswith('.json') else f"{output_path}.csv"
                    save_report(csv_report, csv_output, "csv")
                else:
                    click.echo("\n📊 Report CSV:")
                    lines = csv_report.split('\n')[:10]  # Show first 10 lines
                    click.echo('\n'.join(lines))
                    if len(csv_report.split('\n')) > 10:
                        click.echo("... (report troncato)")
            
            # Show detailed diff for preview mode
            if mode == ProcessingMode.PREVIEW and result.total_processed > 0:
                show_diffs = click.confirm("\nMostrare le differenze di contenuto?", default=False)
                if show_diffs:
                    click.echo("\n🔍 Differenze di contenuto:")
                    for i, product_result in enumerate(result.product_results[:3]):  # Show max 3 diffs
                        if product_result.content_changed:
                            click.echo(f"\n--- Prodotto {product_result.product_id}: {product_result.product_title} ---")
                            diff = pipeline.generate_diff_report(product_result, enable_colors=True)
                            click.echo(diff)
                    
                    if len([r for r in result.product_results if r.content_changed]) > 3:
                        click.echo(f"\n... e altri {len([r for r in result.product_results if r.content_changed]) - 3} prodotti modificati")
            
            # Success message
            if result.errors > 0:
                click.echo(f"\n⚠️ Elaborazione completata con {result.errors} errori")
                return 1
            elif result.total_processed == 0:
                click.echo("\n ℹ️ Nessun prodotto trovato con i filtri specificati")
                return 0
            else:
                click.echo(f"\n✅ Elaborazione completata con successo!")
                return 0
                
        except Exception as e:
            logger.error(f"Processing failed: {e}")
            click.echo(f"\n❌ Errore durante l'elaborazione: {e}")
            return 1


@click.command()
@click.option('--site', required=True, help='Nome del sito')
@click.option('--profile', required=True, help='Nome del profilo')
@click.option('--categories', help='ID categorie (comma-separated)')
@click.option('--post-ids', help='ID post specifici (comma-separated)')
@click.option('--batch-size', default=10, help='Dimensione batch (default: 10)')
@click.option('--skip-processed/--no-skip-processed', default=True, 
              help='Salta prodotti già processati (default: True)')
@click.option('--report-format', default='json', 
              type=click.Choice(['json', 'csv', 'json,csv'], case_sensitive=False),
              help='Formato report (default: json)')
@click.option('--output', help='Percorso file di output per il report')
def preview(site: str, profile: str, categories: str, post_ids: str, 
           batch_size: int, skip_processed: bool, report_format: str, output: str) -> None:
    """Anteprima formattazione prodotti (senza modifiche al database)."""
    
    click.echo("🔍 Modalità PREVIEW - Solo anteprima, nessuna modifica al database")
    
    result = process_products(
        site_name=site,
        profile_name=profile,
        mode=ProcessingMode.PREVIEW,
        categories=categories,
        post_ids=post_ids,
        batch_size=batch_size,
        skip_processed=skip_processed,
        report_format=report_format,
        output_path=output
    )
    
    if result != 0:
        raise click.ClickException("Preview failed")


@click.command()
@click.option('--site', required=True, help='Nome del sito')
@click.option('--profile', required=True, help='Nome del profilo')
@click.option('--categories', help='ID categorie (comma-separated)')
@click.option('--post-ids', help='ID post specifici (comma-separated)')
@click.option('--batch-size', default=10, help='Dimensione batch (default: 10)')
@click.option('--skip-processed/--no-skip-processed', default=True,
              help='Salta prodotti già processati (default: True)')
@click.option('--report-format', default='json',
              type=click.Choice(['json', 'csv', 'json,csv'], case_sensitive=False),
              help='Formato report (default: json)')
@click.option('--output', help='Percorso file di output per il report')
@click.option('--confirm', is_flag=True, help='Conferma esecuzione senza prompt')
def run(site: str, profile: str, categories: str, post_ids: str,
        batch_size: int, skip_processed: bool, report_format: str, 
        output: str, confirm: bool) -> None:
    """Esecuzione formattazione prodotti (con modifiche al database)."""
    
    click.echo("🚀 Modalità RUN - ATTENZIONE: Modificherà il database!")
    
    if not confirm:
        click.echo("\n⚠️ Questa operazione:")
        click.echo("  • Modificherà il contenuto dei prodotti nel database")
        click.echo("  • Creerà backup automatici (write-once)")
        click.echo("  • Aggiorcherà i metadati di processing")
        
        if not click.confirm("\nContinuare con l'esecuzione?"):
            click.echo("❌ Operazione annullata dall'utente")
            return
    
    result = process_products(
        site_name=site,
        profile_name=profile,
        mode=ProcessingMode.RUN,
        categories=categories,
        post_ids=post_ids,
        batch_size=batch_size,
        skip_processed=skip_processed,
        report_format=report_format,
        output_path=output
    )
    
    if result != 0:
        raise click.ClickException("Run failed")


# Command group for processing operations
@click.group()
def processing():
    """Operazioni di elaborazione prodotti."""
    pass


processing.add_command(preview)
processing.add_command(run)