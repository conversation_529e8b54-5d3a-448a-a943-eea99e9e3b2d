"""Profile management CLI commands."""

import json
import logging
from pathlib import Path
from typing import Optional

import click
from sqlalchemy.exc import IntegrityError

from ..database import get_session
from ..database.repositories import ProfileRepository
from ..services.profile_service import get_profile_service
from ..models import Profile

logger = logging.getLogger(__name__)


@click.group()
def profile() -> None:
    """Gestione profili di formattazione"""
    pass


@profile.command()
def list() -> None:
    """Elenca tutti i profili configurati"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        profiles = repo.get_all(active_only=False)
        
        if not profiles:
            click.echo("⚙️ Nessun profilo configurato")
            click.echo("💡 Crea un profilo con: structura-ai profile create <nome>")
            return
        
        click.echo("⚙️ Profili configurati:")
        click.echo()
        
        for prof in profiles:
            status_icon = "✅" if prof.is_active else "❌"
            click.echo(f"  {status_icon} {prof.name}")
            click.echo(f"     Nome visualizzato: {prof.display_name}")
            
            if prof.description:
                click.echo(f"     Descrizione: {prof.description}")
            
            # Show key settings
            click.echo(f"     Modello: {prof.llm_model}")
            click.echo(f"     Temperatura: {prof.llm_temperature}")
            click.echo(f"     Soglia similarità: {prof.similarity_threshold}")
            click.echo(f"     Batch size: {prof.batch_size}")
            
            click.echo(f"     Creato: {prof.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
            click.echo()


@profile.command()
@click.argument('name')
@click.option('--display-name', help='Nome visualizzato per il profilo')
@click.option('--description', help='Descrizione del profilo')
@click.option('--llm-model', help='Modello LLM (default: configurazione globale)')
@click.option('--llm-temperature', type=float, help='Temperatura LLM (default: 0.0)')
@click.option('--similarity-threshold', type=float, help='Soglia similarità guard-rail (default: 0.99)')
@click.option('--length-diff-threshold', type=int, help='Soglia differenza lunghezza (default: 5)')
@click.option('--from-json', type=click.Path(exists=True), help='Importa da file JSON')
def create(name: str, display_name: Optional[str], description: Optional[str],
          llm_model: Optional[str], llm_temperature: Optional[float],
          similarity_threshold: Optional[float], length_diff_threshold: Optional[int],
          from_json: Optional[str]) -> None:
    """Crea un nuovo profilo"""
    
    display_name = display_name or name
    profile_service = get_profile_service()
    
    # Handle JSON import
    if from_json:
        try:
            with open(from_json, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            # Validate JSON structure
            required_fields = ['name', 'display_name', 'config']
            for field in required_fields:
                if field not in json_data:
                    raise click.ClickException(f"Campo obbligatorio mancante nel JSON: {field}")
            
            # Override name if provided as argument
            json_data['name'] = name
            if display_name != name:
                json_data['display_name'] = display_name
            if description:
                json_data['description'] = description
            
            # Create profile from JSON
            session_manager = get_session()
            
            try:
                with session_manager.session_scope() as session:
                    repo = ProfileRepository(session)
                    
                    # Extract config data from JSON
                    config_data = json_data['config']
                    
                    profile_data = {
                        'name': json_data['name'],
                        'display_name': json_data['display_name'],
                        'description': json_data.get('description'),
                        'is_active': json_data.get('is_active', True),
                        **config_data  # Merge config into profile data
                    }
                    
                    profile = repo.create(profile_data)
                    click.echo(f"✅ Profilo '{name}' importato da JSON con successo")
                    click.echo(f"   ID: {profile.id}")
                    return
                    
            except IntegrityError:
                raise click.ClickException(f"Profilo con nome '{name}' già esistente")
            except Exception as e:
                logger.error(f"Failed to create profile from JSON: {e}")
                raise click.ClickException(f"Errore durante l'importazione: {e}")
                
        except FileNotFoundError:
            raise click.ClickException(f"File JSON non trovato: {from_json}")
        except json.JSONDecodeError as e:
            raise click.ClickException(f"File JSON non valido: {e}")
        except Exception as e:
            raise click.ClickException(f"Errore lettura file JSON: {e}")
    
    # Build configuration
    config = {}
    
    if llm_model:
        config['llm_model'] = llm_model
    if llm_temperature is not None:
        if not (0.0 <= llm_temperature <= 2.0):
            raise click.BadParameter("Temperatura deve essere tra 0.0 e 2.0")
        config['llm_temperature'] = llm_temperature
    if similarity_threshold is not None:
        if not (0.0 <= similarity_threshold <= 1.0):
            raise click.BadParameter("Soglia similarità deve essere tra 0.0 e 1.0")
        config['similarity_threshold'] = similarity_threshold
    if length_diff_threshold is not None:
        if length_diff_threshold < 0:
            raise click.BadParameter("Soglia differenza lunghezza deve essere >= 0")
        config['length_diff_threshold'] = length_diff_threshold
    
    # Set defaults if no config provided
    if not config:
        config = profile_service._get_default_config()
    
    # Create profile
    try:
        profile = profile_service.create_profile(
            name=name,
            display_name=display_name,
            description=description,
            config=config
        )
        
        click.echo(f"✅ Profilo '{name}' creato con successo")
        click.echo(f"   ID: {profile.id}")
        click.echo(f"   Display name: {profile.display_name}")
        
        if description:
            click.echo(f"   Descrizione: {description}")
        
        # Show configuration
        click.echo("   Configurazione:")
        for key, value in config.items():
            click.echo(f"   • {key}: {value}")
        
    except ValueError as e:
        raise click.ClickException(f"Errore validazione: {e}")
    except IntegrityError:
        raise click.ClickException(f"Profilo con nome '{name}' già esistente")
    except Exception as e:
        logger.error(f"Failed to create profile {name}: {e}")
        raise click.ClickException(f"Errore durante la creazione: {e}")


@profile.command()
@click.argument('name')
@click.confirmation_option(prompt='Sei sicuro di voler eliminare questo profilo?')
def delete(name: str) -> None:
    """Elimina un profilo"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        
        # Find profile
        profile = repo.get_by_name(name)
        if not profile:
            raise click.ClickException(f"Profilo '{name}' non trovato")
        
        # Check if it's the default profile
        if name == 'default':
            if not click.confirm("ATTENZIONE: Stai eliminando il profilo default. Continuare?"):
                click.echo("❌ Operazione annullata")
                return
        
        # Delete profile
        if repo.delete(profile.id):
            click.echo(f"✅ Profilo '{name}' eliminato con successo")
        else:
            click.echo(f"❌ Errore durante l'eliminazione del profilo '{name}'")


@profile.command()
@click.argument('name')
def show(name: str) -> None:
    """Mostra informazioni dettagliate su un profilo"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        
        # Find profile
        profile = repo.get_by_name(name)
        if not profile:
            raise click.ClickException(f"Profilo '{name}' non trovato")
        
        click.echo(f"⚙️ Profilo: {profile.name}")
        click.echo()
        click.echo(f"Nome visualizzato: {profile.display_name}")
        
        if profile.description:
            click.echo(f"Descrizione: {profile.description}")
        
        click.echo()
        click.echo("📝 Configurazione:")
        click.echo(f"   • LLM Model: {profile.llm_model}")
        if profile.llm_fallback_model:
            click.echo(f"   • LLM Fallback: {profile.llm_fallback_model}")
        click.echo(f"   • Temperatura: {profile.llm_temperature}")
        click.echo(f"   • Batch Size: {profile.batch_size}")
        click.echo(f"   • Soglia Similarità: {profile.similarity_threshold}")
        click.echo(f"   • Soglia Lunghezza: {profile.length_diff_threshold}")
        click.echo(f"   • Skip Processati: {profile.skip_if_processed}")
        click.echo(f"   • Retry Max: {profile.retry_max_attempts}")
        click.echo(f"   • Retry Backoff Base: {profile.retry_backoff_base}")
        click.echo(f"   • Processed Meta Key: {profile.processed_meta_key}")
        click.echo(f"   • Backup Meta Key: {profile.backup_meta_key}")
        
        click.echo()
        click.echo(f"Status: {'✅ Attivo' if profile.is_active else '❌ Disattivo'}")
        click.echo(f"Creato: {profile.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        click.echo(f"Aggiornato: {profile.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")


@profile.command()
@click.argument('name')
@click.option('--output', type=click.Path(), help='Percorso file di output (default: <nome>.json)')
def export(name: str, output: Optional[str]) -> None:
    """Esporta un profilo in formato JSON"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        
        # Find profile
        profile = repo.get_by_name(name)
        if not profile:
            raise click.ClickException(f"Profilo '{name}' non trovato")
        
        # Prepare export data
        export_data = {
            'name': profile.name,
            'display_name': profile.display_name,
            'description': profile.description,
            'config': profile.to_dict(),
            'is_active': profile.is_active,
            'created_at': profile.created_at.isoformat(),
            'updated_at': profile.updated_at.isoformat()
        }
        
        # Determine output file
        if not output:
            output = f"{name}.json"
        
        try:
            output_path = Path(output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            click.echo(f"✅ Profilo '{name}' esportato in: {output_path}")
            
        except Exception as e:
            logger.error(f"Failed to export profile {name}: {e}")
            raise click.ClickException(f"Errore durante l'esportazione: {e}")


@profile.command()
@click.argument('name')
@click.option('--display-name', help='Nuovo nome visualizzato')
@click.option('--description', help='Nuova descrizione')
@click.option('--active/--inactive', default=None, help='Stato attivo/inattivo')
@click.option('--llm-model', help='Modello LLM')
@click.option('--llm-temperature', type=float, help='Temperatura LLM (0.0-2.0)')
@click.option('--similarity-threshold', type=float, help='Soglia similarità (0.0-1.0)')
@click.option('--length-diff-threshold', type=int, help='Soglia differenza lunghezza (>=0)')
def update(name: str, display_name: Optional[str], description: Optional[str],
          active: Optional[bool], llm_model: Optional[str], 
          llm_temperature: Optional[float], similarity_threshold: Optional[float],
          length_diff_threshold: Optional[int]) -> None:
    """Aggiorna configurazione profilo"""
    
    # Check if any updates provided
    updates_provided = any(v is not None for v in [
        display_name, description, active, llm_model, 
        llm_temperature, similarity_threshold, length_diff_threshold
    ])
    
    if not updates_provided:
        raise click.ClickException("Nessun parametro da aggiornare specificato")
    
    # Validate parameters
    if llm_temperature is not None and not (0.0 <= llm_temperature <= 2.0):
        raise click.BadParameter("Temperatura deve essere tra 0.0 e 2.0")
    if similarity_threshold is not None and not (0.0 <= similarity_threshold <= 1.0):
        raise click.BadParameter("Soglia similarità deve essere tra 0.0 e 1.0")
    if length_diff_threshold is not None and length_diff_threshold < 0:
        raise click.BadParameter("Soglia differenza lunghezza deve essere >= 0")
    
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        
        # Find profile
        profile = repo.get_by_name(name)
        if not profile:
            raise click.ClickException(f"Profilo '{name}' non trovato")
        
        # Build updates for basic fields
        profile_updates = {}
        if display_name is not None:
            profile_updates['display_name'] = display_name
        if description is not None:
            profile_updates['description'] = description
        if active is not None:
            profile_updates['is_active'] = active
        
        # Handle configuration updates
        config_changed = False
        if llm_model is not None:
            profile_updates['llm_model'] = llm_model
            config_changed = True
        if llm_temperature is not None:
            profile_updates['llm_temperature'] = llm_temperature
            config_changed = True
        if similarity_threshold is not None:
            profile_updates['similarity_threshold'] = similarity_threshold
            config_changed = True
        if length_diff_threshold is not None:
            profile_updates['length_diff_threshold'] = length_diff_threshold
            config_changed = True
        
        # Apply updates
        updated_profile = repo.update(profile.id, profile_updates)
        if updated_profile:
            click.echo(f"✅ Profilo '{name}' aggiornato con successo")
            
            # Show what was updated
            if display_name is not None:
                click.echo(f"   • Nome visualizzato: {display_name}")
            if description is not None:
                click.echo(f"   • Descrizione: {description}")
            if active is not None:
                status = "attivato" if active else "disattivato"
                click.echo(f"   • Status: {status}")
            
            if config_changed:
                click.echo("   • Configurazione aggiornata:")
                if llm_model is not None:
                    click.echo(f"     - Modello LLM: {llm_model}")
                if llm_temperature is not None:
                    click.echo(f"     - Temperatura: {llm_temperature}")
                if similarity_threshold is not None:
                    click.echo(f"     - Soglia similarità: {similarity_threshold}")
                if length_diff_threshold is not None:
                    click.echo(f"     - Soglia lunghezza: {length_diff_threshold}")
        else:
            raise click.ClickException(f"Errore durante l'aggiornamento del profilo '{name}'")


@profile.command()
@click.argument('name')
def copy(name: str) -> None:
    """Duplica un profilo esistente con nuovo nome"""
    new_name = click.prompt("Nome del nuovo profilo")
    
    if new_name == name:
        raise click.ClickException("Il nuovo nome deve essere diverso dall'originale")
    
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = ProfileRepository(session)
        
        # Find original profile
        original = repo.get_by_name(name)
        if not original:
            raise click.ClickException(f"Profilo originale '{name}' non trovato")
        
        # Check if new name already exists
        existing = repo.get_by_name(new_name)
        if existing:
            raise click.ClickException(f"Profilo con nome '{new_name}' già esistente")
        
        # Create copy using all original fields
        original_dict = original.to_dict()
        original_dict['name'] = new_name
        original_dict['display_name'] = f"{original.display_name} (copia)"
        original_dict['is_active'] = True
        
        profile_data = original_dict
        
        try:
            new_profile = repo.create(profile_data)
            click.echo(f"✅ Profilo '{name}' copiato come '{new_name}' con successo")
            click.echo(f"   ID: {new_profile.id}")
            
        except Exception as e:
            logger.error(f"Failed to copy profile {name} to {new_name}: {e}")
            raise click.ClickException(f"Errore durante la copia: {e}")