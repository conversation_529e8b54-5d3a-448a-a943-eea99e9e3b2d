"""Site management CLI commands."""

import logging
from typing import Optional

import click
from sqlalchemy.exc import IntegrityError

from ..database import get_session
from ..database.repositories import SiteRepository
from ..services.woocommerce_repository import WooCommerceRepository
from ..models import Site

logger = logging.getLogger(__name__)


@click.group()
def site() -> None:
    """Gestione siti WooCommerce"""
    pass


@site.command()
def list() -> None:
    """Elenca tutti i siti configurati"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = SiteRepository(session)
        sites = repo.get_all()
        
        if not sites:
            click.echo("📍 Nessun sito configurato")
            return
        
        click.echo("📍 Siti configurati:")
        click.echo()
        
        for site in sites:
            status_icon = "✅" if site.is_active else "❌"
            click.echo(f"  {status_icon} {site.name}")
            click.echo(f"     Nome: {site.display_name}")
            click.echo(f"     Host: {site.db_host}:{site.db_port}")
            click.echo(f"     Database: {site.db_name}")
            click.echo(f"     Prefisso: {site.db_table_prefix}")
            
            if site.description:
                click.echo(f"     Descrizione: {site.description}")
            
            click.echo()


@site.command()
@click.argument('name')
@click.option('--host', required=True, help='Database host')
@click.option('--port', default=3306, help='Database port (default: 3306)')
@click.option('--user', required=True, help='Database user')
@click.option('--password', required=True, help='Database password')
@click.option('--database', required=True, help='Database name')
@click.option('--prefix', default='wp_', help='Table prefix (default: wp_)')
@click.option('--display-name', help='Display name for site')
@click.option('--description', help='Site description')
@click.option('--test-connection', is_flag=True, help='Test connection before saving')
def add(name: str, host: str, port: int, user: str, password: str, 
        database: str, prefix: str, display_name: Optional[str],
        description: Optional[str], test_connection: bool) -> None:
    """Aggiungi un nuovo sito"""
    
    display_name = display_name or name
    
    # Create site object
    site_data = {
        'name': name,
        'display_name': display_name,
        'description': description,
        'db_host': host,
        'db_port': port,
        'db_name': database,
        'db_user': user,
        'db_password': password,
        'db_table_prefix': prefix,
        'is_active': True
    }
    
    # Test connection if requested
    if test_connection:
        click.echo("🔍 Testing connection...")
        
        # Create temporary site for testing
        temp_site = Site(**site_data)
        temp_site.id = 0  # Temporary ID
        
        try:
            repo = WooCommerceRepository(temp_site)
            test_result = repo.test_connection()
            
            if test_result['status'] == 'success':
                click.echo("✅ Connection test successful!")
                click.echo(f"   • Database connectivity: OK")
                click.echo(f"   • WooCommerce tables: {len(test_result['tables_found'])}/4 found")
                click.echo(f"   • Products count: {test_result['products_count']}")
                
                if test_result['missing_tables']:
                    click.echo(f"   ⚠️ Missing tables: {', '.join(test_result['missing_tables'])}")
                    if not click.confirm("Continue despite missing tables?"):
                        click.echo("❌ Site creation cancelled")
                        return
            else:
                click.echo("❌ Connection test failed!")
                click.echo(f"   Error: {test_result['error']}")
                if not click.confirm("Continue despite connection failure?"):
                    click.echo("❌ Site creation cancelled")
                    return
                    
        except Exception as e:
            click.echo(f"❌ Connection test failed: {e}")
            if not click.confirm("Continue despite connection failure?"):
                click.echo("❌ Site creation cancelled")
                return
    
    # Save to database
    session_manager = get_session()
    
    try:
        with session_manager.session_scope() as session:
            repo = SiteRepository(session)
            site = repo.create(site_data)
            
            click.echo(f"✅ Sito '{name}' ({display_name}) aggiunto con successo")
            click.echo(f"   ID: {site.id}")
            
    except IntegrityError:
        click.echo(f"❌ Errore: Sito con nome '{name}' già esistente")
    except Exception as e:
        logger.error(f"Failed to create site {name}: {e}")
        click.echo(f"❌ Errore durante la creazione del sito: {e}")


@site.command()
@click.argument('name')
@click.confirmation_option(prompt='Sei sicuro di voler rimuovere questo sito?')
def remove(name: str) -> None:
    """Rimuovi un sito"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = SiteRepository(session)
        
        # Find site
        site = repo.get_by_name(name)
        if not site:
            click.echo(f"❌ Sito '{name}' non trovato")
            return
        
        # Delete site
        if repo.delete(site.id):
            click.echo(f"✅ Sito '{name}' rimosso con successo")
        else:
            click.echo(f"❌ Errore durante la rimozione del sito '{name}'")


@site.command()
@click.argument('name')
def test(name: str) -> None:
    """Testa la connessione a un sito"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = SiteRepository(session)
        
        # Find site
        site = repo.get_by_name(name)
        if not site:
            click.echo(f"❌ Sito '{name}' non trovato")
            return
        
        click.echo(f"🔍 Testing connection to site '{name}'...")
        
        try:
            woo_repo = WooCommerceRepository(site)
            test_result = woo_repo.test_connection()
            
            if test_result['status'] == 'success':
                click.echo("✅ Connection test successful!")
                click.echo()
                click.echo("📊 Database Info:")
                click.echo(f"   • Host: {site.db_host}:{site.db_port}")
                click.echo(f"   • Database: {site.db_name}")
                click.echo(f"   • Tables found: {len(test_result['tables_found'])}/4")
                click.echo(f"   • Products count: {test_result['products_count']}")
                
                if test_result['missing_tables']:
                    click.echo()
                    click.echo("⚠️ Missing tables:")
                    for table in test_result['missing_tables']:
                        click.echo(f"   • {table}")
                
                click.echo()
                click.echo("📋 Found tables:")
                for table in test_result['tables_found']:
                    click.echo(f"   • {table}")
                    
            else:
                click.echo("❌ Connection test failed!")
                click.echo(f"   Error Type: {test_result['error_type']}")
                click.echo(f"   Error: {test_result['error']}")
                
        except Exception as e:
            logger.error(f"Connection test failed for site {name}: {e}")
            click.echo(f"❌ Connection test failed: {e}")


@site.command()
@click.argument('name')
def info(name: str) -> None:
    """Mostra informazioni dettagliate su un sito"""
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = SiteRepository(session)
        
        # Find site
        site = repo.get_by_name(name)
        if not site:
            click.echo(f"❌ Sito '{name}' non trovato")
            return
        
        click.echo(f"📍 Informazioni sito: {site.name}")
        click.echo()
        click.echo(f"Nome visualizzato: {site.display_name}")
        if site.description:
            click.echo(f"Descrizione: {site.description}")
        
        click.echo()
        click.echo("🗄️ Database:")
        click.echo(f"   Host: {site.db_host}:{site.db_port}")
        click.echo(f"   Database: {site.db_name}")
        click.echo(f"   Username: {site.db_user}")
        click.echo(f"   Prefisso tabelle: {site.db_table_prefix}")
        
        click.echo()
        click.echo(f"Status: {'✅ Attivo' if site.is_active else '❌ Disattivo'}")
        click.echo(f"Creato: {site.created_at.strftime('%Y-%m-%d %H:%M:%S')}")
        click.echo(f"Aggiornato: {site.updated_at.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Try to get categories info
        try:
            woo_repo = WooCommerceRepository(site)
            categories = woo_repo.get_categories()
            
            if categories:
                click.echo()
                click.echo(f"🏷️ Categorie prodotti ({len(categories)}):")
                for cat in categories[:10]:  # Show first 10
                    click.echo(f"   • {cat['name']} (ID: {cat['id']}, {cat['count']} prodotti)")
                
                if len(categories) > 10:
                    click.echo(f"   ... e altre {len(categories) - 10} categorie")
                    
        except Exception as e:
            logger.warning(f"Could not fetch categories for site {name}: {e}")
            click.echo()
            click.echo("⚠️ Could not fetch categories (connection issue)")


@site.command()
@click.argument('name')
@click.option('--active/--inactive', default=None, help='Set site active status')
@click.option('--display-name', help='Update display name')
@click.option('--description', help='Update description')
def update(name: str, active: Optional[bool], display_name: Optional[str], 
          description: Optional[str]) -> None:
    """Aggiorna configurazione sito"""
    
    if all(v is None for v in [active, display_name, description]):
        click.echo("❌ Nessun parametro da aggiornare specificato")
        return
    
    session_manager = get_session()
    
    with session_manager.session_scope() as session:
        repo = SiteRepository(session)
        
        # Find site
        site = repo.get_by_name(name)
        if not site:
            click.echo(f"❌ Sito '{name}' non trovato")
            return
        
        # Build updates
        updates = {}
        if active is not None:
            updates['is_active'] = active
        if display_name is not None:
            updates['display_name'] = display_name
        if description is not None:
            updates['description'] = description
        
        # Apply updates
        updated_site = repo.update(site.id, updates)
        if updated_site:
            click.echo(f"✅ Sito '{name}' aggiornato con successo")
            
            if active is not None:
                status = "attivato" if active else "disattivato"
                click.echo(f"   • Status: {status}")
            if display_name is not None:
                click.echo(f"   • Nome visualizzato: {display_name}")
            if description is not None:
                click.echo(f"   • Descrizione: {description}")
        else:
            click.echo(f"❌ Errore durante l'aggiornamento del sito '{name}'")