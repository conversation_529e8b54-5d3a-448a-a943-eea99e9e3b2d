"""System diagnostic and setup commands."""

import logging
from pathlib import Path

import click

from ..config import get_settings
from ..database import get_engine
from ..services.profile_service import get_profile_service

logger = logging.getLogger(__name__)


@click.group()
def system() -> None:
    """Comandi di sistema e diagnostica"""
    pass


@system.command()
def status() -> None:
    """Mostra lo stato del sistema"""
    settings = get_settings()
    
    click.echo("🔍 Status Sistema Structura AI")
    click.echo("=" * 50)
    
    # Configuration
    click.echo("\n⚙️ Configurazione:")
    click.echo(f"   • OpenRouter Model: {settings.openrouter_default_model}")
    click.echo(f"   • Fallback Model: {settings.openrouter_fallback_model}")
    click.echo(f"   • Temperature: {settings.openrouter_temperature}")
    click.echo(f"   • Batch Size: {settings.default_batch_size}")
    click.echo(f"   • Similarity Threshold: {settings.similarity_threshold}")
    
    # Directories
    click.echo("\n📁 Directory:")
    click.echo(f"   • Data Dir: {settings.app_data_dir}")
    click.echo(f"   • Log File: {settings.log_file_path}")
    click.echo(f"   • SQLite DB: {settings.local_db_path}")
    
    # Check directories
    data_dir = Path(settings.app_data_dir)
    log_dir = Path(settings.log_file_path).parent
    
    click.echo(f"   • Data Dir exists: {'✅' if data_dir.exists() else '❌'}")
    click.echo(f"   • Log Dir exists: {'✅' if log_dir.exists() else '❌'}")
    
    # Database status
    click.echo("\n🗄️ Database Locale:")
    try:
        engine = get_engine()
        db_path = Path(settings.local_db_path)
        click.echo(f"   • Database file: {'✅' if db_path.exists() else '❌'}")
        click.echo(f"   • Engine initialized: ✅")
        
        # Test database connection
        from ..database import get_session
        session_manager = get_session()
        
        with session_manager.session_scope() as session:
            # Count records in each table
            from ..database.repositories import SiteRepository, ProfileRepository
            
            site_repo = SiteRepository(session)
            profile_repo = ProfileRepository(session)
            
            sites = site_repo.get_all(active_only=False)
            profiles = profile_repo.get_all(active_only=False)
            
            click.echo(f"   • Siti configurati: {len(sites)}")
            click.echo(f"   • Profili configurati: {len(profiles)}")
            
    except Exception as e:
        click.echo(f"   • Database error: ❌ {e}")
    
    # API Keys (without showing actual values)
    click.echo("\n🔑 API Keys:")
    has_openrouter = bool(settings.openrouter_api_key and settings.openrouter_api_key != "sk-or-...")
    click.echo(f"   • OpenRouter API Key: {'✅' if has_openrouter else '❌'}")
    
    # Profile service
    click.echo("\n👤 Profili:")
    try:
        profile_service = get_profile_service()
        profile_service.get_or_create_default_profile()
        click.echo(f"   • Profilo default: ✅")
    except Exception as e:
        click.echo(f"   • Profilo default: ❌ {e}")


@system.command()
def init() -> None:
    """Inizializza il sistema con configurazione base"""
    click.echo("🚀 Inizializzazione Sistema Structura AI")
    click.echo("=" * 50)
    
    settings = get_settings()
    
    # Ensure directories
    click.echo("\n📁 Creazione directory...")
    try:
        settings.ensure_directories()
        click.echo("   ✅ Directory create con successo")
    except Exception as e:
        click.echo(f"   ❌ Errore creazione directory: {e}")
        return
    
    # Initialize database
    click.echo("\n🗄️ Inizializzazione database...")
    try:
        engine = get_engine()
        engine.create_tables()
        click.echo("   ✅ Database inizializzato con successo")
    except Exception as e:
        click.echo(f"   ❌ Errore inizializzazione database: {e}")
        return
    
    # Create default profile
    click.echo("\n👤 Creazione profilo default...")
    try:
        profile_service = get_profile_service()
        profile = profile_service.get_or_create_default_profile()
        click.echo(f"   ✅ Profilo default disponibile")
    except Exception as e:
        click.echo(f"   ❌ Errore creazione profilo: {e}")
        return
    
    click.echo("\n🎉 Sistema inizializzato con successo!")
    click.echo("\nProssimi passi:")
    click.echo("1. Configura file .env con le tue credenziali")
    click.echo("2. Aggiungi un sito con: structura-ai site add")
    click.echo("3. Testa la connessione con: structura-ai site test")


@system.command()
@click.confirmation_option(prompt='Sei sicuro di voler resettare il database?')
def reset() -> None:
    """Reset completo del database locale"""
    click.echo("🔄 Reset Database...")
    
    try:
        engine = get_engine()
        engine.reset_database()
        click.echo("✅ Database resettato con successo")
        
        # Recreate default profile
        profile_service = get_profile_service()
        profile_service.create_default_profile()
        click.echo(f"✅ Profilo default ricreato")
        
    except Exception as e:
        click.echo(f"❌ Errore durante il reset: {e}")


@system.command()
def check() -> None:
    """Verifica configurazione e connettività"""
    click.echo("🔍 Verifica Sistema...")
    
    settings = get_settings()
    all_good = True
    
    # Check API key
    if not settings.openrouter_api_key or settings.openrouter_api_key == "sk-or-...":
        click.echo("❌ OpenRouter API key non configurata")
        all_good = False
    else:
        click.echo("✅ OpenRouter API key configurata")
    
    # Check database
    try:
        from ..database import get_session
        session_manager = get_session()
        
        with session_manager.session_scope() as session:
            from ..database.repositories import SiteRepository
            site_repo = SiteRepository(session)
            sites = site_repo.get_all()
            
            if not sites:
                click.echo("⚠️ Nessun sito configurato")
            else:
                click.echo(f"✅ {len(sites)} siti configurati")
                
                # Test each site connection
                for site in sites:
                    from ..services.woocommerce_repository import WooCommerceRepository
                    try:
                        woo_repo = WooCommerceRepository(site)
                        test_result = woo_repo.test_connection()
                        
                        if test_result['status'] == 'success':
                            click.echo(f"   ✅ {site.name}: connessione OK ({test_result['products_count']} prodotti)")
                        else:
                            click.echo(f"   ❌ {site.name}: {test_result['error']}")
                            all_good = False
                    except Exception as e:
                        click.echo(f"   ❌ {site.name}: {e}")
                        all_good = False
    
    except Exception as e:
        click.echo(f"❌ Errore database: {e}")
        all_good = False
    
    if all_good:
        click.echo("\n🎉 Sistema configurato correttamente!")
    else:
        click.echo("\n⚠️ Alcuni problemi rilevati. Controlla la configurazione.")


@system.command()
def info() -> None:
    """Informazioni dettagliate sul sistema"""
    import platform
    import sys
    from datetime import datetime
    
    settings = get_settings()
    
    click.echo("ℹ️ Informazioni Sistema Structura AI")
    click.echo("=" * 50)
    
    # System info
    click.echo(f"\n🖥️ Sistema:")
    click.echo(f"   • OS: {platform.system()} {platform.release()}")
    click.echo(f"   • Python: {sys.version.split()[0]}")
    click.echo(f"   • Architettura: {platform.machine()}")
    click.echo(f"   • Data/Ora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # App info
    from .. import __version__
    click.echo(f"\n🚀 Applicazione:")
    click.echo(f"   • Versione: {__version__}")
    click.echo(f"   • Working Directory: {Path.cwd()}")
    
    # Configuration paths
    click.echo(f"\n📁 Percorsi:")
    click.echo(f"   • Config: .env")
    click.echo(f"   • Database: {settings.local_db_path}")
    click.echo(f"   • Logs: {settings.log_file_path}")
    click.echo(f"   • Data: {settings.app_data_dir}")
    
    # Dependencies
    click.echo(f"\n📦 Dipendenze principali:")
    try:
        import sqlalchemy
        click.echo(f"   • SQLAlchemy: {sqlalchemy.__version__}")
    except ImportError:
        click.echo(f"   • SQLAlchemy: non installato")
    
    try:
        import click as click_pkg
        click.echo(f"   • Click: {click_pkg.__version__}")
    except ImportError:
        click.echo(f"   • Click: non installato")
    
    try:
        import pydantic
        click.echo(f"   • Pydantic: {pydantic.__version__}")
    except ImportError:
        click.echo(f"   • Pydantic: non installato")