"""Configuration settings for Structura AI."""

import os
from pathlib import Path
from typing import List, Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""
    
    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
        extra="ignore"
    )
    
    # OpenRouter API Configuration
    openrouter_api_key: str = Field(..., description="OpenRouter API key")
    openrouter_base_url: str = Field(
        default="https://openrouter.ai/api/v1",
        description="OpenRouter API base URL"
    )
    openrouter_default_model: str = Field(
        default="z-ai/glm-4.5-air:free",
        description="Default OpenRouter model"
    )
    openrouter_fallback_model: str = Field(
        default="mistralai/mistral-7b-instruct",
        description="Fallback OpenRouter model"
    )
    openrouter_temperature: float = Field(
        default=0.0,
        description="Temperature for OpenRouter API calls"
    )
    
    # Database Configuration (WooCommerce)
    dev_site_db_host: str = Field(..., description="Database host")
    dev_site_db_port: int = Field(default=3306, description="Database port")
    dev_site_db_user: str = Field(..., description="Database username")
    dev_site_db_password: str = Field(..., description="Database password")
    dev_site_db_name: str = Field(..., description="Database name")
    wp_table_prefix: str = Field(default="wp_", description="WordPress table prefix")
    
    # Local SQLite Database
    local_db_path: str = Field(
        default="./data/structura_ai.db",
        description="Path to local SQLite database"
    )
    
    # Application Directories
    app_data_dir: str = Field(
        default="./data",
        description="Application data directory"
    )
    log_file_path: str = Field(
        default="./logs/structura-ai.log",
        description="Log file path"
    )
    
    # Logging Configuration
    log_level: str = Field(default="INFO", description="Logging level")
    
    # Processing Configuration
    default_batch_size: int = Field(
        default=10,
        description="Default batch size for processing"
    )
    similarity_threshold: float = Field(
        default=0.99,
        description="Similarity threshold for content validation"
    )
    length_diff_threshold: int = Field(
        default=5,
        description="Maximum allowed length difference in characters"
    )
    retry_max_attempts: int = Field(
        default=3,
        description="Maximum retry attempts"
    )
    retry_backoff_seconds: str = Field(
        default="2,4,8",
        description="Retry backoff intervals in seconds"
    )
    
    # Cache Configuration
    cache_ttl_hours: int = Field(
        default=24,
        description="Cache TTL in hours"
    )
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", description="API host address")
    api_port: int = Field(default=8000, description="API port")
    api_debug: bool = Field(default=False, description="Enable API debug mode")
    api_cors_origins: str = Field(
        default="http://localhost:3000,http://127.0.0.1:3000",
        description="CORS allowed origins (comma-separated)"
    )
    
    # JWT Configuration
    jwt_secret_key: str = Field(..., description="JWT secret key")
    jwt_algorithm: str = Field(default="HS256", description="JWT algorithm")
    jwt_access_token_expire_minutes: int = Field(
        default=60, description="JWT access token expiration in minutes"
    )
    jwt_refresh_token_expire_days: int = Field(
        default=7, description="JWT refresh token expiration in days"
    )
    
    # Redis Configuration (for Celery)
    redis_host: str = Field(default="localhost", description="Redis host")
    redis_port: int = Field(default=6379, description="Redis port")
    redis_db: int = Field(default=0, description="Redis database number")
    
    @field_validator("similarity_threshold")
    @classmethod
    def validate_similarity_threshold(cls, v: float) -> float:
        """Validate similarity threshold is between 0 and 1."""
        if not 0 <= v <= 1:
            raise ValueError("Similarity threshold must be between 0 and 1")
        return v
    
    @field_validator("retry_backoff_seconds")
    @classmethod
    def validate_retry_backoff_seconds(cls, v: str) -> str:
        """Validate retry backoff seconds format."""
        try:
            intervals = [float(x.strip()) for x in v.split(",")]
            if len(intervals) == 0:
                raise ValueError("At least one backoff interval is required")
            return v
        except ValueError as e:
            raise ValueError(f"Invalid retry backoff format: {e}") from e
    
    @property
    def retry_backoff_intervals(self) -> List[float]:
        """Get retry backoff intervals as a list of floats."""
        return [float(x.strip()) for x in self.retry_backoff_seconds.split(",")]
    
    @property
    def data_dir_path(self) -> Path:
        """Get data directory as Path object."""
        return Path(self.app_data_dir)
    
    @property
    def log_dir_path(self) -> Path:
        """Get log directory as Path object."""
        return Path(self.log_file_path).parent
    
    @property
    def cors_origins_list(self) -> List[str]:
        """Get CORS origins as a list of strings."""
        return [origin.strip() for origin in self.api_cors_origins.split(",")]
    
    def ensure_directories(self) -> None:
        """Ensure required directories exist."""
        self.data_dir_path.mkdir(parents=True, exist_ok=True)
        self.log_dir_path.mkdir(parents=True, exist_ok=True)


# Global settings instance
_settings: Optional[Settings] = None


def get_settings() -> Settings:
    """Get global settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
        _settings.ensure_directories()
    return _settings