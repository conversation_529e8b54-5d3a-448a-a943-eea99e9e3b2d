"""Database engine configuration."""

from typing import Optional

from sqlalchemy import create_engine, Engine
from sqlalchemy.pool import StaticPool

from ..config import get_settings
from ..models import Base


class DatabaseEngine:
    """Database engine manager."""
    
    def __init__(self, db_path: Optional[str] = None):
        """Initialize database engine."""
        self.settings = get_settings()
        self.db_path = db_path or self.settings.local_db_path
        self._engine: Optional[Engine] = None
    
    @property
    def engine(self) -> Engine:
        """Get SQLAlchemy engine."""
        if self._engine is None:
            self._engine = create_engine(
                f"sqlite:///{self.db_path}",
                poolclass=StaticPool,
                connect_args={
                    "check_same_thread": False,
                    "timeout": 30
                },
                echo=False  # Set to True for SQL debugging
            )
        return self._engine
    
    def create_tables(self) -> None:
        """Create all database tables."""
        Base.metadata.create_all(bind=self.engine)
    
    def drop_tables(self) -> None:
        """Drop all database tables."""
        Base.metadata.drop_all(bind=self.engine)
    
    def reset_database(self) -> None:
        """Reset database by dropping and recreating tables."""
        self.drop_tables()
        self.create_tables()


# Global engine instance
_engine: Optional[DatabaseEngine] = None


def get_engine() -> DatabaseEngine:
    """Get global database engine."""
    global _engine
    if _engine is None:
        _engine = DatabaseEngine()
        _engine.create_tables()
    return _engine