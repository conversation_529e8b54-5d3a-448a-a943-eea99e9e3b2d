"""Repository classes for database operations."""

import json
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session
from sqlalchemy import select, update, delete

from ..models import Site, Profile, JobRun, AICache


class BaseRepository:
    """Base repository class."""
    
    def __init__(self, session: Session):
        """Initialize repository with database session."""
        self.session = session


class SiteRepository(BaseRepository):
    """Repository for Site operations."""
    
    def create(self, site_data: Dict[str, Any]) -> Site:
        """Create a new site."""
        site = Site(**site_data)
        self.session.add(site)
        self.session.flush()
        return site
    
    def get_by_id(self, site_id: int) -> Optional[Site]:
        """Get site by ID."""
        return self.session.get(Site, site_id)
    
    def get_by_name(self, name: str) -> Optional[Site]:
        """Get site by name."""
        stmt = select(Site).where(Site.name == name)
        return self.session.execute(stmt).scalar_one_or_none()
    
    def get_all(self, active_only: bool = True) -> List[Site]:
        """Get all sites."""
        stmt = select(Site)
        if active_only:
            stmt = stmt.where(Site.is_active == True)
        return self.session.execute(stmt).scalars().all()
    
    def update(self, site_id: int, updates: Dict[str, Any]) -> Optional[Site]:
        """Update site."""
        stmt = update(Site).where(Site.id == site_id).values(**updates)
        result = self.session.execute(stmt)
        if result.rowcount > 0:
            return self.get_by_id(site_id)
        return None
    
    def delete(self, site_id: int) -> bool:
        """Delete site."""
        stmt = delete(Site).where(Site.id == site_id)
        result = self.session.execute(stmt)
        return result.rowcount > 0


class ProfileRepository(BaseRepository):
    """Repository for Profile operations."""
    
    def create(self, profile_data: Dict[str, Any]) -> Profile:
        """Create a new profile."""
        # Convert lists/dicts to JSON strings
        if 'allowed_tags' in profile_data:
            profile_data['allowed_tags_json'] = json.dumps(profile_data.pop('allowed_tags'))
        if 'allowed_attributes' in profile_data:
            profile_data['allowed_attributes_json'] = json.dumps(profile_data.pop('allowed_attributes'))
        
        profile = Profile(**profile_data)
        self.session.add(profile)
        self.session.flush()
        return profile
    
    def get_by_id(self, profile_id: int) -> Optional[Profile]:
        """Get profile by ID."""
        return self.session.get(Profile, profile_id)
    
    def get_by_name(self, name: str) -> Optional[Profile]:
        """Get profile by name."""
        stmt = select(Profile).where(Profile.name == name)
        return self.session.execute(stmt).scalar_one_or_none()
    
    def get_all(self, active_only: bool = True) -> List[Profile]:
        """Get all profiles."""
        stmt = select(Profile)
        if active_only:
            stmt = stmt.where(Profile.is_active == True)
        return self.session.execute(stmt).scalars().all()
    
    def update(self, profile_id: int, updates: Dict[str, Any]) -> Optional[Profile]:
        """Update profile."""
        # Convert lists/dicts to JSON strings
        if 'allowed_tags' in updates:
            updates['allowed_tags_json'] = json.dumps(updates.pop('allowed_tags'))
        if 'allowed_attributes' in updates:
            updates['allowed_attributes_json'] = json.dumps(updates.pop('allowed_attributes'))
        
        stmt = update(Profile).where(Profile.id == profile_id).values(**updates)
        result = self.session.execute(stmt)
        if result.rowcount > 0:
            return self.get_by_id(profile_id)
        return None
    
    def delete(self, profile_id: int) -> bool:
        """Delete profile."""
        stmt = delete(Profile).where(Profile.id == profile_id)
        result = self.session.execute(stmt)
        return result.rowcount > 0


class JobRunRepository(BaseRepository):
    """Repository for JobRun operations."""
    
    def create(self, job_data: Dict[str, Any]) -> JobRun:
        """Create a new job run."""
        # Convert lists to JSON strings
        if 'categories' in job_data:
            job_data['categories_json'] = json.dumps(job_data.pop('categories'))
        if 'post_ids' in job_data:
            job_data['post_ids_json'] = json.dumps(job_data.pop('post_ids'))
        if 'error_details' in job_data:
            job_data['error_details_json'] = json.dumps(job_data.pop('error_details'))
        
        job = JobRun(**job_data)
        self.session.add(job)
        self.session.flush()
        return job
    
    def get_by_id(self, job_id: int) -> Optional[JobRun]:
        """Get job run by ID."""
        return self.session.get(JobRun, job_id)
    
    def get_by_job_id(self, job_id: str) -> Optional[JobRun]:
        """Get job run by job ID (UUID)."""
        stmt = select(JobRun).where(JobRun.job_id == job_id)
        return self.session.execute(stmt).scalar_one_or_none()
    
    def get_by_site(self, site_id: int, limit: int = 50) -> List[JobRun]:
        """Get job runs by site."""
        stmt = select(JobRun).where(JobRun.site_id == site_id).order_by(JobRun.created_at.desc()).limit(limit)
        return self.session.execute(stmt).scalars().all()
    
    def update(self, job_id: int, updates: Dict[str, Any]) -> Optional[JobRun]:
        """Update job run."""
        # Convert lists/dicts to JSON strings
        if 'categories' in updates:
            updates['categories_json'] = json.dumps(updates.pop('categories'))
        if 'post_ids' in updates:
            updates['post_ids_json'] = json.dumps(updates.pop('post_ids'))
        if 'error_details' in updates:
            updates['error_details_json'] = json.dumps(updates.pop('error_details'))
        
        stmt = update(JobRun).where(JobRun.id == job_id).values(**updates)
        result = self.session.execute(stmt)
        if result.rowcount > 0:
            return self.get_by_id(job_id)
        return None


class AICacheRepository(BaseRepository):
    """Repository for AI Cache operations."""
    
    def create(self, cache_data: Dict[str, Any]) -> AICache:
        """Create a new cache entry."""
        cache = AICache(**cache_data)
        self.session.add(cache)
        self.session.flush()
        return cache
    
    def get_by_input_hash(self, input_hash: str) -> Optional[AICache]:
        """Get cache entry by input hash."""
        stmt = select(AICache).where(AICache.input_hash == input_hash)
        return self.session.execute(stmt).scalar_one_or_none()
    
    def update_hit_count(self, cache_id: int) -> None:
        """Update hit count for cache entry."""
        cache = self.session.get(AICache, cache_id)
        if cache:
            cache.update_hit_count()
    
    def cleanup_expired(self) -> int:
        """Remove expired cache entries."""
        from datetime import datetime
        stmt = delete(AICache).where(
            AICache.expires_at.isnot(None),
            AICache.expires_at < datetime.utcnow()
        )
        result = self.session.execute(stmt)
        return result.rowcount
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        from sqlalchemy import func
        
        stats = self.session.execute(
            select(
                func.count(AICache.id).label('total_entries'),
                func.sum(AICache.hit_count).label('total_hits'),
                func.avg(AICache.hit_count).label('avg_hits'),
                func.max(AICache.hit_count).label('max_hits')
            )
        ).first()
        
        return {
            'total_entries': stats.total_entries or 0,
            'total_hits': stats.total_hits or 0,
            'avg_hits': float(stats.avg_hits or 0),
            'max_hits': stats.max_hits or 0
        }