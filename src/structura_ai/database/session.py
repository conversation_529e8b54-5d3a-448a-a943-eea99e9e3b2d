"""Database session management."""

from contextlib import contextmanager
from typing import Generator, Optional

from sqlalchemy.orm import sessionmaker, Session

from .engine import get_engine


class DatabaseSession:
    """Database session manager."""
    
    def __init__(self):
        """Initialize session maker."""
        self.engine = get_engine().engine
        self.SessionLocal = sessionmaker(
            autocommit=False,
            autoflush=False,
            bind=self.engine
        )
    
    def get_session(self) -> Session:
        """Get a new database session."""
        return self.SessionLocal()
    
    @contextmanager
    def session_scope(self) -> Generator[Session, None, None]:
        """Provide a transactional scope around a series of operations."""
        session = self.get_session()
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()


# Global session manager
_session_manager: Optional[DatabaseSession] = None


def get_session() -> DatabaseSession:
    """Get global session manager."""
    global _session_manager
    if _session_manager is None:
        _session_manager = DatabaseSession()
    return _session_manager