#!/usr/bin/env python3
"""
Main entry point for Structura AI CLI
"""

import sys
import logging

# Import Click for CLI
import click

# Import configuration and logging
from .config import get_settings
from .config.logging import setup_logging

# Import database
from .database import get_engine

# Import CLI commands
from .cli.site_commands import site
from .cli.system_commands import system
from .cli.processing_commands import preview, run
from .cli.profile_commands import profile


@click.group()
@click.option('--debug', is_flag=True, help='Enable debug logging')
@click.option('--log-file', help='Override log file path')
@click.pass_context
def cli(ctx: click.Context, debug: bool, log_file: str) -> None:
    """Structura AI - HTML Formatter per prodotti WooCommerce v1.0.0"""
    # Setup logging
    log_level = "DEBUG" if debug else None
    setup_logging(log_level=log_level, log_file=log_file)
    
    # Initialize database
    try:
        engine = get_engine()
        logging.info("Database initialized successfully")
    except Exception as e:
        logging.error(f"Failed to initialize database: {e}")
        sys.exit(1)
    
    # Store context
    ctx.ensure_object(dict)
    ctx.obj['debug'] = debug






# Register command groups
cli.add_command(site)
cli.add_command(system)
cli.add_command(profile)

# Register processing commands
cli.add_command(preview)
cli.add_command(run)


def main() -> int:
    """Main entry point"""
    try:
        cli()
        return 0
    except KeyboardInterrupt:
        click.echo("\n⏹️ Operazione interrotta dall'utente", err=True)
        return 130
    except Exception as e:
        logging.error(f"Errore imprevisto: {e}", exc_info=True)
        click.echo(f"❌ Errore: {e}", err=True)
        return 1


if __name__ == "__main__":
    sys.exit(main())