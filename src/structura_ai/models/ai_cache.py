"""AI cache model for storing LLM responses."""

from datetime import datetime
from typing import Optional

from sqlalchemy import String, Text, DateTime, Integer
from sqlalchemy.orm import Mapped, mapped_column

from .base import Base


class AICache(Base):
    """Model for AI response cache."""
    
    __tablename__ = "ai_cache"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    
    # Hash of normalized input content
    input_hash: Mapped[str] = mapped_column(String(64), unique=True, nullable=False)
    
    # Original input content (for debugging)
    input_content: Mapped[str] = mapped_column(Text, nullable=False)
    
    # LLM response
    output_html: Mapped[str] = mapped_column(Text, nullable=False)
    output_hash: Mapped[str] = mapped_column(String(64), nullable=False)
    
    # Metadata
    model_name: Mapped[str] = mapped_column(String(100), nullable=False)
    temperature: Mapped[float] = mapped_column(nullable=False)
    prompt_hash: Mapped[str] = mapped_column(String(64), nullable=False)
    
    # Quality metrics
    similarity_score: Mapped[float] = mapped_column(nullable=False)
    length_diff: Mapped[int] = mapped_column(nullable=False)
    
    # Usage tracking
    hit_count: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    last_accessed: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        default=lambda: datetime.utcnow()
    )
    
    # TTL
    expires_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True
    )
    
    def __repr__(self) -> str:
        return f"<AICache(id={self.id}, input_hash='{self.input_hash[:8]}...', hits={self.hit_count})>"
    
    @property
    def is_expired(self) -> bool:
        """Check if cache entry is expired."""
        if self.expires_at is None:
            return False
        return datetime.utcnow() > self.expires_at
    
    def update_hit_count(self) -> None:
        """Update hit count and last accessed time."""
        self.hit_count += 1
        self.last_accessed = datetime.utcnow()