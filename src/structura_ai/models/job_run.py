"""Job run model for tracking processing jobs."""

import json
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional

from sqlalchemy import String, Text, Integer, Boolean, DateTime, ForeignKey, Enum as SQLEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class JobStatus(str, Enum):
    """Job execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class JobMode(str, Enum):
    """Job execution mode."""
    PREVIEW = "preview"
    RUN = "run"


class JobRun(Base):
    """Model for job run tracking."""
    
    __tablename__ = "job_runs"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    job_id: Mapped[str] = mapped_column(String(36), unique=True, nullable=False)  # UUID
    
    # References
    site_id: Mapped[int] = mapped_column(Integer, ForeignKey("sites.id"), nullable=False)
    profile_id: Mapped[int] = mapped_column(Integer, <PERSON>K<PERSON>("profiles.id"), nullable=False)
    
    # Job configuration
    mode: Mapped[JobMode] = mapped_column(SQLEnum(JobMode), nullable=False)
    status: Mapped[JobStatus] = mapped_column(SQLEnum(JobStatus), default=JobStatus.PENDING, nullable=False)
    
    # Filters (stored as JSON)
    categories_json: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    post_ids_json: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Execution tracking
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime(timezone=True), nullable=True)
    
    # Results
    total_products: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    processed_products: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    skipped_products: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    failed_products: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    cache_hits: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    
    # Error information
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_details_json: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Report paths
    report_json_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    report_csv_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Relationships
    site: Mapped["Site"] = relationship("Site", back_populates="job_runs")
    profile: Mapped["Profile"] = relationship("Profile", back_populates="job_runs")
    
    def __repr__(self) -> str:
        return f"<JobRun(id={self.id}, job_id='{self.job_id}', status='{self.status.value}')>"
    
    @property
    def categories(self) -> Optional[List[int]]:
        """Get categories filter as list."""
        if self.categories_json:
            return json.loads(self.categories_json)
        return None
    
    @categories.setter
    def categories(self, categories: Optional[List[int]]) -> None:
        """Set categories filter from list."""
        if categories:
            self.categories_json = json.dumps(categories)
        else:
            self.categories_json = None
    
    @property
    def post_ids(self) -> Optional[List[int]]:
        """Get post IDs filter as list."""
        if self.post_ids_json:
            return json.loads(self.post_ids_json)
        return None
    
    @post_ids.setter
    def post_ids(self, post_ids: Optional[List[int]]) -> None:
        """Set post IDs filter from list."""
        if post_ids:
            self.post_ids_json = json.dumps(post_ids)
        else:
            self.post_ids_json = None
    
    @property
    def error_details(self) -> Optional[Dict[str, Any]]:
        """Get error details as dict."""
        if self.error_details_json:
            return json.loads(self.error_details_json)
        return None
    
    @error_details.setter
    def error_details(self, details: Optional[Dict[str, Any]]) -> None:
        """Set error details from dict."""
        if details:
            self.error_details_json = json.dumps(details)
        else:
            self.error_details_json = None
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get job duration in seconds."""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def success_rate(self) -> float:
        """Get success rate as percentage."""
        if self.total_products == 0:
            return 0.0
        return (self.processed_products / self.total_products) * 100