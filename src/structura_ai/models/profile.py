"""Profile model for formatting configurations."""

import json
from typing import Any, Dict, List, Optional

from sqlalchemy import String, Text, Float, Integer, <PERSON>olean
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class Profile(Base):
    """Model for formatting profiles."""
    
    __tablename__ = "profiles"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    display_name: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # LLM Configuration
    llm_model: Mapped[str] = mapped_column(String(100), nullable=False)
    llm_fallback_model: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    llm_temperature: Mapped[float] = mapped_column(Float, default=0.0, nullable=False)
    llm_prompt: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Processing Configuration
    batch_size: Mapped[int] = mapped_column(Integer, default=10, nullable=False)
    similarity_threshold: Mapped[float] = mapped_column(Float, default=0.99, nullable=False)
    length_diff_threshold: Mapped[int] = mapped_column(Integer, default=5, nullable=False)
    skip_if_processed: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Retry Configuration
    retry_max_attempts: Mapped[int] = mapped_column(Integer, default=3, nullable=False)
    retry_backoff_base: Mapped[float] = mapped_column(Float, default=2.0, nullable=False)
    
    # HTML Configuration (stored as JSON)
    allowed_tags_json: Mapped[str] = mapped_column(Text, nullable=False)
    allowed_attributes_json: Mapped[str] = mapped_column(Text, nullable=False)
    
    # Meta Configuration
    processed_meta_key: Mapped[str] = mapped_column(String(100), default="_ai_processed_v1", nullable=False)
    backup_meta_key: Mapped[str] = mapped_column(String(100), default="_ai_original_content_v1", nullable=False)
    
    # Profile status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    version: Mapped[int] = mapped_column(Integer, default=1, nullable=False)
    
    # Relationships
    job_runs: Mapped[List["JobRun"]] = relationship(
        "JobRun", 
        back_populates="profile",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Profile(id={self.id}, name='{self.name}', version={self.version})>"
    
    @property
    def allowed_tags(self) -> List[str]:
        """Get allowed HTML tags as list."""
        return json.loads(self.allowed_tags_json)
    
    @allowed_tags.setter
    def allowed_tags(self, tags: List[str]) -> None:
        """Set allowed HTML tags from list."""
        self.allowed_tags_json = json.dumps(tags)
    
    @property
    def allowed_attributes(self) -> Dict[str, List[str]]:
        """Get allowed HTML attributes as dict."""
        return json.loads(self.allowed_attributes_json)
    
    @allowed_attributes.setter
    def allowed_attributes(self, attributes: Dict[str, List[str]]) -> None:
        """Set allowed HTML attributes from dict."""
        self.allowed_attributes_json = json.dumps(attributes)
    
    @property
    def retry_backoff_intervals(self) -> List[float]:
        """Calculate retry backoff intervals."""
        intervals = []
        for i in range(self.retry_max_attempts):
            intervals.append(self.retry_backoff_base ** i)
        return intervals
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert profile to dictionary for export."""
        return {
            "name": self.name,
            "display_name": self.display_name,
            "description": self.description,
            "llm_model": self.llm_model,
            "llm_fallback_model": self.llm_fallback_model,
            "llm_temperature": self.llm_temperature,
            "llm_prompt": self.llm_prompt,
            "batch_size": self.batch_size,
            "similarity_threshold": self.similarity_threshold,
            "length_diff_threshold": self.length_diff_threshold,
            "skip_if_processed": self.skip_if_processed,
            "retry_max_attempts": self.retry_max_attempts,
            "retry_backoff_base": self.retry_backoff_base,
            "allowed_tags": self.allowed_tags,
            "allowed_attributes": self.allowed_attributes,
            "processed_meta_key": self.processed_meta_key,
            "backup_meta_key": self.backup_meta_key,
            "version": self.version
        }