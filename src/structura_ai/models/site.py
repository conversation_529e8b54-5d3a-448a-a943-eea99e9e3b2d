"""Site model for managing WooCommerce sites."""

from typing import List, Optional

from sqlalchemy import String, <PERSON><PERSON><PERSON>, Boolean, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from .base import Base


class Site(Base):
    """Model for WooCommerce sites."""
    
    __tablename__ = "sites"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True)
    name: Mapped[str] = mapped_column(String(100), unique=True, nullable=False)
    display_name: Mapped[str] = mapped_column(String(200), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Database connection details
    db_host: Mapped[str] = mapped_column(String(255), nullable=False)
    db_port: Mapped[int] = mapped_column(Integer, default=3306, nullable=False)
    db_name: Mapped[str] = mapped_column(String(100), nullable=False)
    db_user: Mapped[str] = mapped_column(String(100), nullable=False)
    db_password: Mapped[str] = mapped_column(String(255), nullable=False)
    db_table_prefix: Mapped[str] = mapped_column(String(20), default="wp_", nullable=False)
    
    # Site status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, nullable=False)
    
    # Relationships
    job_runs: Mapped[List["JobRun"]] = relationship(
        "JobRun", 
        back_populates="site",
        cascade="all, delete-orphan"
    )
    
    def __repr__(self) -> str:
        return f"<Site(id={self.id}, name='{self.name}', display_name='{self.display_name}')>"
    
    @property
    def connection_string(self) -> str:
        """Get database connection string."""
        return f"mysql+pymysql://{self.db_user}:{self.db_password}@{self.db_host}:{self.db_port}/{self.db_name}"