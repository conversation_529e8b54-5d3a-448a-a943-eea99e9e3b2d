"""AI service for content processing with caching and guard-rails."""

import asyncio
import logging
from typing import Dict, <PERSON>, <PERSON>tional, <PERSON><PERSON>

from sqlalchemy.orm import Session

from ..database.session import get_session
from ..database.repositories import AICacheRepository
from .openrouter_client import OpenRouterClient, OpenRouterError
from .content_processor import ContentProcessor, ContentValidationError


logger = logging.getLogger(__name__)


class AIServiceError(Exception):
    """AI service error."""
    
    def __init__(self, message: str, error_type: str, original_error: Optional[Exception] = None):
        self.message = message
        self.error_type = error_type
        self.original_error = original_error
        super().__init__(self.message)


class AIService:
    """
    Main AI service for content processing with intelligent caching and guard-rails.
    
    Integrates OpenRouter client, content processor, and database caching
    to provide robust AI-powered content formatting.
    """
    
    def __init__(self):
        self.openrouter_client = OpenRouterClient()
        self.content_processor = ContentProcessor()
        
        # Cache statistics
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "guard_rail_failures": 0,
            "api_failures": 0
        }
    
    def _get_cache_repository(self, session: Session) -> AICacheRepository:
        """Get AI cache repository instance."""
        return AICacheRepository(session)
    
    def format_product_content(self, content: str, profile) -> Dict[str, Any]:
        """
        Format product content using the profile settings (synchronous wrapper).
        
        Args:
            content: Product content to format
            profile: Profile with LLM settings
            
        Returns:
            Dictionary with processing results
        """
        import asyncio
        
        try:
            # Run async process_content in sync context
            loop = None
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
            
            if loop.is_running():
                # If already in async context, create new loop in thread
                import concurrent.futures
                import threading
                
                def run_in_thread():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(
                            self.process_content(
                                input_content=content,
                                prompt_template=profile.llm_prompt,
                                model_name=profile.llm_model,
                                temperature=profile.llm_temperature
                            )
                        )
                    finally:
                        new_loop.close()
                
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(run_in_thread)
                    processed_content, metadata = future.result()
            else:
                # Normal case
                processed_content, metadata = loop.run_until_complete(
                    self.process_content(
                        input_content=content,
                        prompt_template=profile.llm_prompt,
                        model_name=profile.llm_model,
                        temperature=profile.llm_temperature
                    )
                )
            
            # Convert to expected format
            return {
                'output_html': processed_content,
                'model_name': metadata.get('model_used', profile.llm_model),
                'temperature': metadata.get('temperature_used', profile.llm_temperature),
                'prompt_template': profile.llm_prompt,
                'cache_hit': metadata.get('cache_hit', False),
                'processing_time': metadata.get('processing_time_ms', 0) / 1000.0,
                'guard_rail_passed': metadata.get('guard_rail_passed', True),
                'validation_metrics': metadata.get('validation_metrics', {})
            }
            
        except Exception as e:
            logger.error(f"Error in format_product_content: {e}")
            # Return error format that ProcessingPipeline can handle
            return {
                'output_html': content,  # Return original content
                'model_name': profile.llm_model,
                'temperature': profile.llm_temperature,
                'prompt_template': profile.llm_prompt,
                'cache_hit': False,
                'processing_time': 0.0,
                'error': str(e),
                'guard_rail_passed': False
            }
    
    async def _check_cache(
        self,
        input_content: str,
        prompt_template: str,
        session: Optional[Session] = None
    ) -> Optional[str]:
        """
        Check cache for previous AI response.
        
        Args:
            input_content: Input content to check
            prompt_template: Prompt template used
            session: Database session (optional)
            
        Returns:
            Cached output HTML if found, None otherwise
        """
        input_hash = self.content_processor.generate_content_hash(input_content)
        prompt_hash = self.content_processor.generate_prompt_hash(prompt_template)
        
        # Use provided session or create new one
        if session is None:
            with get_session() as db_session:
                return await self._check_cache_with_session(
                    input_hash, prompt_hash, db_session
                )
        else:
            return await self._check_cache_with_session(
                input_hash, prompt_hash, session
            )
    
    async def _check_cache_with_session(
        self,
        input_hash: str,
        prompt_hash: str,
        session: Session
    ) -> Optional[str]:
        """Check cache with specific session."""
        try:
            cache_repo = self._get_cache_repository(session)
            cache_entry = cache_repo.get_by_input_hash(input_hash)
            
            if cache_entry is None:
                logger.debug(f"Cache miss for input_hash: {input_hash[:12]}...")
                self._cache_stats["misses"] += 1
                return None
            
            # Check if entry is expired
            if cache_entry.is_expired:
                logger.info(f"Cache entry expired for input_hash: {input_hash[:12]}...")
                self._cache_stats["misses"] += 1
                return None
            
            # Check if prompt hash matches (same template)
            if cache_entry.prompt_hash != prompt_hash:
                logger.debug(
                    f"Cache entry found but prompt hash mismatch for input_hash: {input_hash[:12]}... "
                    f"(stored: {cache_entry.prompt_hash[:12]}..., requested: {prompt_hash[:12]}...)"
                )
                self._cache_stats["misses"] += 1
                return None
            
            # Update hit count
            cache_repo.update_hit_count(cache_entry.id)
            session.commit()
            
            logger.info(
                f"Cache hit for input_hash: {input_hash[:12]}... "
                f"(hits: {cache_entry.hit_count + 1}, similarity: {cache_entry.similarity_score:.4f})"
            )
            
            self._cache_stats["hits"] += 1
            return cache_entry.output_html
            
        except Exception as e:
            logger.error(f"Cache check failed: {str(e)}")
            self._cache_stats["misses"] += 1
            return None
    
    async def _store_in_cache(
        self,
        input_content: str,
        output_content: str,
        model_name: str,
        temperature: float,
        prompt_template: str,
        session: Optional[Session] = None
    ) -> bool:
        """
        Store AI response in cache.
        
        Args:
            input_content: Original input content
            output_content: AI-processed output
            model_name: Model used for processing
            temperature: Temperature parameter used
            prompt_template: Prompt template used
            session: Database session (optional)
            
        Returns:
            True if successfully stored, False otherwise
        """
        try:
            # Prepare cache data (includes validation)
            cache_data = self.content_processor.prepare_cache_data(
                input_content,
                output_content,
                model_name,
                temperature,
                prompt_template
            )
            
            # Use provided session or create new one
            if session is None:
                with get_session() as db_session:
                    return await self._store_in_cache_with_session(cache_data, db_session)
            else:
                return await self._store_in_cache_with_session(cache_data, session)
                
        except ContentValidationError as e:
            logger.error(
                f"Guard-rail validation failed, not caching: {e.message} "
                f"(type: {e.validation_type})"
            )
            self._cache_stats["guard_rail_failures"] += 1
            return False
        except Exception as e:
            logger.error(f"Cache storage failed: {str(e)}")
            return False
    
    async def _store_in_cache_with_session(
        self,
        cache_data: Dict[str, Any],
        session: Session
    ) -> bool:
        """Store cache data with specific session."""
        try:
            cache_repo = self._get_cache_repository(session)
            
            # Check if entry already exists (race condition protection)
            existing = cache_repo.get_by_input_hash(cache_data["input_hash"])
            if existing:
                logger.debug(f"Cache entry already exists for input_hash: {cache_data['input_hash'][:12]}...")
                return True
            
            # Create new cache entry
            cache_repo.create(cache_data)
            session.commit()
            
            logger.info(
                f"Stored in cache: input_hash={cache_data['input_hash'][:12]}..., "
                f"similarity={cache_data['similarity_score']:.4f}, "
                f"length_diff={cache_data['length_diff']} chars"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Cache storage with session failed: {str(e)}")
            session.rollback()
            return False
    
    async def process_content(
        self,
        input_content: str,
        prompt_template: str,
        model_name: Optional[str] = None,
        temperature: Optional[float] = None,
        bypass_cache: bool = False,
        session: Optional[Session] = None
    ) -> Tuple[str, Dict[str, Any]]:
        """
        Process content using AI with caching and guard-rails.
        
        Args:
            input_content: Content to process
            prompt_template: Prompt template with {content} placeholder
            model_name: Override default model
            temperature: Override default temperature
            bypass_cache: Skip cache lookup and storage
            session: Database session (optional)
            
        Returns:
            Tuple of (processed_content, metadata)
            
        Raises:
            AIServiceError: If processing fails
        """
        if not input_content or not input_content.strip():
            raise AIServiceError(
                "Input content is empty",
                error_type="empty_input"
            )
        
        if not prompt_template or "{content}" not in prompt_template:
            raise AIServiceError(
                "Invalid prompt template - must contain {content} placeholder",
                error_type="invalid_prompt"
            )
        
        metadata = {
            "cache_hit": False,
            "guard_rail_passed": False,
            "model_used": model_name or self.openrouter_client.default_model,
            "temperature_used": temperature if temperature is not None else self.openrouter_client.temperature,
            "processing_time_ms": 0,
            "validation_metrics": {}
        }
        
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Check cache first (unless bypassed)
            if not bypass_cache:
                cached_result = await self._check_cache(input_content, prompt_template, session)
                if cached_result:
                    metadata["cache_hit"] = True
                    metadata["processing_time_ms"] = int((asyncio.get_event_loop().time() - start_time) * 1000)
                    logger.info(f"Returning cached result (processing time: {metadata['processing_time_ms']}ms)")
                    return cached_result, metadata
            
            # Process with AI
            logger.info(f"Processing content with AI (length: {len(input_content)} chars)")
            
            try:
                processed_content = await self.openrouter_client.format_product_description(
                    input_content,
                    prompt_template,
                    model=model_name,
                    temperature=temperature
                )
                
                # Extract actual model used (could be fallback)
                metadata["model_used"] = model_name or self.openrouter_client.default_model
                
            except OpenRouterError as e:
                self._cache_stats["api_failures"] += 1
                raise AIServiceError(
                    f"OpenRouter API error: {e.message}",
                    error_type=e.error_type,
                    original_error=e
                )
            
            # Validate with guard-rails
            try:
                validation_metrics = self.content_processor.validate_processed_content(
                    input_content, processed_content
                )
                metadata["guard_rail_passed"] = True
                metadata["validation_metrics"] = validation_metrics
                
            except ContentValidationError as e:
                self._cache_stats["guard_rail_failures"] += 1
                raise AIServiceError(
                    f"Content validation failed: {e.message}",
                    error_type=f"guard_rail_{e.validation_type}",
                    original_error=e
                )
            
            # Store in cache (unless bypassed)
            if not bypass_cache:
                cache_stored = await self._store_in_cache(
                    input_content,
                    processed_content,
                    metadata["model_used"],
                    metadata["temperature_used"],
                    prompt_template,
                    session
                )
                
                if cache_stored:
                    logger.debug("Result stored in cache successfully")
                else:
                    logger.warning("Failed to store result in cache")
            
            metadata["processing_time_ms"] = int((asyncio.get_event_loop().time() - start_time) * 1000)
            
            logger.info(
                f"Content processing completed successfully "
                f"(processing time: {metadata['processing_time_ms']}ms, "
                f"similarity: {metadata['validation_metrics'].get('similarity_score', 0):.4f})"
            )
            
            return processed_content, metadata
            
        except AIServiceError:
            # Re-raise AI service errors
            raise
        except Exception as e:
            # Wrap unexpected errors
            logger.error(f"Unexpected error in content processing: {str(e)}")
            raise AIServiceError(
                f"Unexpected processing error: {str(e)}",
                error_type="unexpected_error",
                original_error=e
            )
    
    async def batch_process_content(
        self,
        content_items: list[Dict[str, Any]],
        prompt_template: str,
        model_name: Optional[str] = None,
        temperature: Optional[float] = None,
        bypass_cache: bool = False,
        session: Optional[Session] = None
    ) -> list[Dict[str, Any]]:
        """
        Process multiple content items in batch.
        
        Args:
            content_items: List of dicts with 'content' and optional 'id' keys
            prompt_template: Prompt template with {content} placeholder
            model_name: Override default model
            temperature: Override default temperature
            bypass_cache: Skip cache lookup and storage
            session: Database session (optional)
            
        Returns:
            List of result dicts with original item data plus 'processed_content', 'metadata', and 'error' keys
        """
        results = []
        
        for i, item in enumerate(content_items):
            content = item.get("content", "")
            item_id = item.get("id", f"item_{i}")
            
            result = {
                **item,  # Include all original item data
                "processed_content": None,
                "metadata": {},
                "error": None
            }
            
            try:
                processed_content, metadata = await self.process_content(
                    content,
                    prompt_template,
                    model_name=model_name,
                    temperature=temperature,
                    bypass_cache=bypass_cache,
                    session=session
                )
                
                result["processed_content"] = processed_content
                result["metadata"] = metadata
                
                logger.debug(f"Batch item {item_id} processed successfully")
                
            except AIServiceError as e:
                result["error"] = {
                    "message": e.message,
                    "type": e.error_type
                }
                logger.error(f"Batch item {item_id} failed: {e.message}")
            
            results.append(result)
        
        # Log batch summary
        successful = sum(1 for r in results if r["error"] is None)
        failed = len(results) - successful
        
        logger.info(
            f"Batch processing completed: {successful} successful, {failed} failed "
            f"out of {len(results)} items"
        )
        
        return results
    
    def get_service_stats(self) -> Dict[str, Any]:
        """Get service statistics."""
        total_requests = self._cache_stats["hits"] + self._cache_stats["misses"]
        cache_hit_rate = (self._cache_stats["hits"] / total_requests * 100) if total_requests > 0 else 0
        
        return {
            **self._cache_stats,
            "total_requests": total_requests,
            "cache_hit_rate_percent": round(cache_hit_rate, 2),
            "openrouter_client_info": self.openrouter_client.get_model_info(),
            "guard_rail_config": self.content_processor.get_guard_rail_config()
        }
    
    def reset_stats(self) -> None:
        """Reset service statistics."""
        self._cache_stats = {
            "hits": 0,
            "misses": 0,
            "guard_rail_failures": 0,
            "api_failures": 0
        }
        logger.info("Service statistics reset")
    
    async def cleanup_expired_cache(self, session: Optional[Session] = None) -> int:
        """
        Clean up expired cache entries.
        
        Args:
            session: Database session (optional)
            
        Returns:
            Number of entries removed
        """
        try:
            if session is None:
                with get_session() as db_session:
                    cache_repo = self._get_cache_repository(db_session)
                    removed = cache_repo.cleanup_expired()
                    db_session.commit()
            else:
                cache_repo = self._get_cache_repository(session)
                removed = cache_repo.cleanup_expired()
                session.commit()
            
            if removed > 0:
                logger.info(f"Cleaned up {removed} expired cache entries")
            else:
                logger.debug("No expired cache entries found")
            
            return removed
            
        except Exception as e:
            logger.error(f"Cache cleanup failed: {str(e)}")
            return 0
    
    async def get_cache_stats(self, session: Optional[Session] = None) -> Dict[str, Any]:
        """
        Get detailed cache statistics from database.
        
        Args:
            session: Database session (optional)
            
        Returns:
            Cache statistics dict
        """
        try:
            if session is None:
                with get_session() as db_session:
                    cache_repo = self._get_cache_repository(db_session)
                    stats = cache_repo.get_stats()
            else:
                cache_repo = self._get_cache_repository(session)
                stats = cache_repo.get_stats()
            
            return {
                **stats,
                **self._cache_stats,
                "total_requests": self._cache_stats["hits"] + self._cache_stats["misses"],
                "cache_hit_rate_percent": (
                    self._cache_stats["hits"] / (self._cache_stats["hits"] + self._cache_stats["misses"]) * 100
                    if (self._cache_stats["hits"] + self._cache_stats["misses"]) > 0 else 0
                )
            }
            
        except Exception as e:
            logger.error(f"Failed to get cache stats: {str(e)}")
            return {
                "error": str(e),
                **self._cache_stats
            }