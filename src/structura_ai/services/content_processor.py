"""Content processor with security guard-rails and text normalization."""

import hashlib
import logging
import unicodedata
from difflib import <PERSON><PERSON><PERSON><PERSON><PERSON>
from typing import Dict, Any, Optional, Tuple

from ..config.settings import get_settings


logger = logging.getLogger(__name__)


class ContentValidationError(Exception):
    """Content validation error."""
    
    def __init__(self, message: str, validation_type: str, metrics: Optional[Dict[str, Any]] = None):
        self.message = message
        self.validation_type = validation_type
        self.metrics = metrics or {}
        super().__init__(self.message)


class ContentProcessor:
    """Content processor with security guard-rails and normalization."""
    
    def __init__(self):
        self.settings = get_settings()
        self.similarity_threshold = self.settings.similarity_threshold
        self.length_diff_threshold = self.settings.length_diff_threshold
    
    def normalize_content(self, content: str) -> str:
        """
        Normalize content for consistent processing and hashing.
        
        Args:
            content: Raw content string
            
        Returns:
            Normalized content string
        """
        if not content:
            return ""
        
        # Unicode normalization to NFC (canonical composition)
        # This ensures consistent Unicode representation
        normalized = unicodedata.normalize('NFC', content)
        
        # Convert to lowercase for case-insensitive comparison
        normalized = normalized.lower()
        
        # Strip leading/trailing whitespace and normalize internal whitespace
        normalized = ' '.join(normalized.split())
        
        return normalized
    
    def generate_content_hash(self, content: str) -> str:
        """
        Generate SHA256 hash of normalized content.
        
        Args:
            content: Content to hash
            
        Returns:
            Hexadecimal hash string
        """
        if not content:
            return hashlib.sha256(b"").hexdigest()
        
        normalized = self.normalize_content(content)
        return hashlib.sha256(normalized.encode('utf-8')).hexdigest()
    
    def generate_prompt_hash(self, prompt: str) -> str:
        """
        Generate hash for prompt template.
        
        Args:
            prompt: Prompt template string
            
        Returns:
            Hexadecimal hash string
        """
        if not prompt:
            return hashlib.sha256(b"").hexdigest()
        
        # Don't normalize prompt as we want to preserve original formatting
        return hashlib.sha256(prompt.encode('utf-8')).hexdigest()
    
    def calculate_similarity(self, original: str, processed: str) -> float:
        """
        Calculate similarity score between original and processed content.
        
        Uses difflib.SequenceMatcher for sequence similarity comparison.
        
        Args:
            original: Original content
            processed: Processed content
            
        Returns:
            Similarity score between 0.0 and 1.0
        """
        if not original and not processed:
            return 1.0
        
        if not original or not processed:
            return 0.0
        
        # Normalize both texts for comparison
        norm_original = self.normalize_content(original)
        norm_processed = self.normalize_content(processed)
        
        # Calculate sequence similarity
        matcher = SequenceMatcher(None, norm_original, norm_processed)
        similarity = matcher.ratio()
        
        logger.debug(
            f"Similarity calculation: original={len(norm_original)} chars, "
            f"processed={len(norm_processed)} chars, similarity={similarity:.4f}"
        )
        
        return similarity
    
    def calculate_length_difference(self, original: str, processed: str) -> int:
        """
        Calculate character length difference between original and processed content.
        
        Args:
            original: Original content
            processed: Processed content
            
        Returns:
            Absolute length difference in characters
        """
        original_length = len(original) if original else 0
        processed_length = len(processed) if processed else 0
        
        diff = abs(processed_length - original_length)
        
        logger.debug(
            f"Length difference: original={original_length} chars, "
            f"processed={processed_length} chars, diff={diff} chars"
        )
        
        return diff
    
    def validate_content_similarity(
        self,
        original: str,
        processed: str,
        custom_threshold: Optional[float] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate that processed content maintains sufficient similarity to original.
        
        Args:
            original: Original content
            processed: Processed content
            custom_threshold: Override default similarity threshold
            
        Returns:
            Tuple of (is_valid, metrics_dict)
        """
        threshold = custom_threshold if custom_threshold is not None else self.similarity_threshold
        similarity = self.calculate_similarity(original, processed)
        
        metrics = {
            "similarity_score": similarity,
            "similarity_threshold": threshold,
            "original_length": len(original) if original else 0,
            "processed_length": len(processed) if processed else 0
        }
        
        is_valid = similarity >= threshold
        
        if not is_valid:
            logger.warning(
                f"Content similarity check failed: {similarity:.4f} < {threshold:.4f}"
            )
        else:
            logger.debug(
                f"Content similarity check passed: {similarity:.4f} >= {threshold:.4f}"
            )
        
        return is_valid, metrics
    
    def validate_length_difference(
        self,
        original: str,
        processed: str,
        custom_threshold: Optional[int] = None
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate that processed content length difference is within acceptable range.
        
        Args:
            original: Original content
            processed: Processed content
            custom_threshold: Override default length difference threshold
            
        Returns:
            Tuple of (is_valid, metrics_dict)
        """
        threshold = custom_threshold if custom_threshold is not None else self.length_diff_threshold
        length_diff = self.calculate_length_difference(original, processed)
        
        metrics = {
            "length_difference": length_diff,
            "length_threshold": threshold,
            "original_length": len(original) if original else 0,
            "processed_length": len(processed) if processed else 0
        }
        
        is_valid = length_diff <= threshold
        
        if not is_valid:
            logger.warning(
                f"Content length check failed: {length_diff} > {threshold} chars"
            )
        else:
            logger.debug(
                f"Content length check passed: {length_diff} <= {threshold} chars"
            )
        
        return is_valid, metrics
    
    def validate_processed_content(
        self,
        original: str,
        processed: str,
        similarity_threshold: Optional[float] = None,
        length_threshold: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive validation of processed content against guard-rails.
        
        Args:
            original: Original content
            processed: Processed content
            similarity_threshold: Override default similarity threshold
            length_threshold: Override default length difference threshold
            
        Returns:
            Validation results dict with metrics and status
            
        Raises:
            ContentValidationError: If validation fails
        """
        if not original:
            raise ContentValidationError(
                "Original content is empty",
                validation_type="empty_original"
            )
        
        if not processed:
            raise ContentValidationError(
                "Processed content is empty",
                validation_type="empty_processed"
            )
        
        # Similarity validation
        similarity_valid, similarity_metrics = self.validate_content_similarity(
            original, processed, similarity_threshold
        )
        
        # Length difference validation
        length_valid, length_metrics = self.validate_length_difference(
            original, processed, length_threshold
        )
        
        # Combine metrics
        all_metrics = {
            **similarity_metrics,
            **length_metrics,
            "similarity_passed": similarity_valid,
            "length_passed": length_valid,
            "overall_passed": similarity_valid and length_valid
        }
        
        # Check overall validation
        if not similarity_valid:
            raise ContentValidationError(
                f"Content similarity too low: {similarity_metrics['similarity_score']:.4f} < {similarity_metrics['similarity_threshold']:.4f}",
                validation_type="similarity_too_low",
                metrics=all_metrics
            )
        
        if not length_valid:
            raise ContentValidationError(
                f"Content length difference too high: {length_metrics['length_difference']} > {length_metrics['length_threshold']}",
                validation_type="length_diff_too_high",
                metrics=all_metrics
            )
        
        logger.info(
            f"Content validation passed: similarity={similarity_metrics['similarity_score']:.4f}, "
            f"length_diff={length_metrics['length_difference']} chars"
        )
        
        return all_metrics
    
    def extract_text_from_html(self, html_content: str) -> str:
        """
        Extract plain text from HTML content for comparison.
        
        Simple text extraction that removes HTML tags while preserving content structure.
        
        Args:
            html_content: HTML content string
            
        Returns:
            Plain text content
        """
        if not html_content:
            return ""
        
        import re
        
        # Remove HTML tags but preserve content
        text = re.sub(r'<[^>]+>', ' ', html_content)
        
        # Normalize whitespace
        text = ' '.join(text.split())
        
        return text.strip()
    
    def prepare_cache_data(
        self,
        original_content: str,
        processed_content: str,
        model_name: str,
        temperature: float,
        prompt_template: str
    ) -> Dict[str, Any]:
        """
        Prepare data for AI cache storage.
        
        Args:
            original_content: Original input content
            processed_content: AI-processed content
            model_name: LLM model name used
            temperature: Temperature parameter used
            prompt_template: Prompt template used
            
        Returns:
            Dictionary ready for cache storage
        """
        # Validate content first
        validation_metrics = self.validate_processed_content(original_content, processed_content)
        
        # Generate hashes
        input_hash = self.generate_content_hash(original_content)
        output_hash = self.generate_content_hash(processed_content)
        prompt_hash = self.generate_prompt_hash(prompt_template)
        
        # Calculate TTL
        from datetime import datetime, timedelta
        expires_at = datetime.utcnow() + timedelta(hours=self.settings.cache_ttl_hours)
        
        cache_data = {
            "input_hash": input_hash,
            "input_content": original_content,
            "output_html": processed_content,
            "output_hash": output_hash,
            "model_name": model_name,
            "temperature": temperature,
            "prompt_hash": prompt_hash,
            "similarity_score": validation_metrics["similarity_score"],
            "length_diff": validation_metrics["length_difference"],
            "expires_at": expires_at
        }
        
        logger.debug(f"Prepared cache data with input_hash: {input_hash[:12]}...")
        
        return cache_data
    
    def get_guard_rail_config(self) -> Dict[str, Any]:
        """Get current guard-rail configuration."""
        return {
            "similarity_threshold": self.similarity_threshold,
            "length_diff_threshold": self.length_diff_threshold,
            "cache_ttl_hours": self.settings.cache_ttl_hours
        }

    def process_with_sanitization(
        self,
        original_content: str,
        ai_processed_content: str,
        model_name: str,
        temperature: float,
        prompt_template: str,
        enable_sanitization: bool = True,
        pretty_print: bool = True
    ) -> Dict[str, Any]:
        """
        Complete processing pipeline with HTML sanitization integration.
        
        This method combines AI processing validation with HTML sanitization
        as required by Phase 4 specifications.
        
        Args:
            original_content: Original input content
            ai_processed_content: AI-processed content (potentially unsafe HTML)
            model_name: LLM model name used
            temperature: Temperature parameter used
            prompt_template: Prompt template used
            enable_sanitization: Enable HTML sanitization (default: True)
            pretty_print: Enable pretty-printing of output HTML
            
        Returns:
            Dictionary with complete processing results including sanitization metrics
            
        Raises:
            ContentValidationError: If guard-rail validation fails
        """
        from .html_sanitizer import HTMLSanitizer, HTMLSanitizationError
        
        result = {
            "original_content": original_content,
            "ai_processed_content": ai_processed_content,
            "final_content": ai_processed_content,
            "sanitized": False,
            "guard_rail_validation": {},
            "html_sanitization": {},
            "cache_data": {},
            "processing_errors": []
        }
        
        try:
            # Step 1: Guard-rail validation (existing functionality)
            logger.debug("Performing guard-rail validation...")
            validation_metrics = self.validate_processed_content(
                original_content, 
                ai_processed_content
            )
            result["guard_rail_validation"] = validation_metrics
            
            # Step 2: HTML sanitization (new Phase 4 functionality)
            if enable_sanitization:
                logger.debug("Performing HTML sanitization...")
                sanitizer = HTMLSanitizer()
                
                try:
                    sanitization_result = sanitizer.process_html(
                        ai_processed_content, 
                        pretty_print=pretty_print
                    )
                    
                    result["html_sanitization"] = sanitization_result
                    result["sanitized"] = True
                    
                    # Use sanitized content as final output
                    if sanitization_result["is_valid"] and not sanitization_result["processing_errors"]:
                        result["final_content"] = sanitization_result["formatted_html"]
                        logger.info("HTML sanitization completed successfully")
                    else:
                        # Use sanitized but unformatted content if validation failed
                        result["final_content"] = sanitization_result["sanitized_html"]
                        logger.warning("HTML validation failed, using sanitized content without formatting")
                        result["processing_errors"].extend(sanitization_result["validation_errors"])
                
                except HTMLSanitizationError as e:
                    logger.error(f"HTML sanitization failed: {e}")
                    result["processing_errors"].append(f"HTML sanitization error: {str(e)}")
                    # Fall back to original AI content
                    result["final_content"] = ai_processed_content
                    result["sanitized"] = False
            
            # Step 3: Final validation on sanitized content
            if result["sanitized"]:
                logger.debug("Performing final validation on sanitized content...")
                try:
                    # Re-validate against original content using text comparison
                    original_text = self.extract_text_from_html(original_content)
                    final_text = self.extract_text_from_html(result["final_content"])
                    
                    final_validation = self.validate_processed_content(
                        original_text,
                        final_text
                    )
                    result["final_validation"] = final_validation
                    
                except ContentValidationError as e:
                    logger.warning(f"Final validation failed: {e}")
                    result["processing_errors"].append(f"Final validation error: {str(e)}")
            
            # Step 4: Prepare cache data
            logger.debug("Preparing cache data...")
            try:
                cache_data = self.prepare_cache_data(
                    original_content=original_content,
                    processed_content=result["final_content"],
                    model_name=model_name,
                    temperature=temperature,
                    prompt_template=prompt_template
                )
                result["cache_data"] = cache_data
                
            except ContentValidationError as e:
                logger.warning(f"Cache preparation failed: {e}")
                result["processing_errors"].append(f"Cache preparation error: {str(e)}")
            
            # Step 5: Calculate comprehensive metrics
            result["metrics"] = self._calculate_processing_metrics(
                original_content,
                ai_processed_content,
                result["final_content"],
                result.get("html_sanitization", {}),
                result.get("guard_rail_validation", {})
            )
            
            logger.info(
                f"Content processing completed: {len(result['processing_errors'])} errors, "
                f"sanitized={result['sanitized']}, "
                f"final_length={len(result['final_content'])}"
            )
            
            return result
            
        except ContentValidationError:
            # Re-raise guard-rail validation errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error in content processing: {e}")
            result["processing_errors"].append(f"Unexpected processing error: {str(e)}")
            return result
    
    def _calculate_processing_metrics(
        self,
        original_content: str,
        ai_processed_content: str,
        final_content: str,
        sanitization_result: Dict[str, Any],
        validation_result: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Calculate comprehensive processing metrics.
        
        Args:
            original_content: Original content
            ai_processed_content: AI-processed content
            final_content: Final processed content
            sanitization_result: HTML sanitization results
            validation_result: Guard-rail validation results
            
        Returns:
            Dictionary with comprehensive metrics
        """
        metrics = {
            "content_lengths": {
                "original": len(original_content) if original_content else 0,
                "ai_processed": len(ai_processed_content) if ai_processed_content else 0,
                "final": len(final_content) if final_content else 0
            },
            "similarity_scores": {},
            "html_metrics": {},
            "validation_metrics": {}
        }
        
        # Calculate similarity scores
        if original_content and ai_processed_content:
            metrics["similarity_scores"]["original_to_ai"] = self.calculate_similarity(
                original_content, ai_processed_content
            )
        
        if original_content and final_content:
            metrics["similarity_scores"]["original_to_final"] = self.calculate_similarity(
                original_content, final_content
            )
        
        if ai_processed_content and final_content:
            metrics["similarity_scores"]["ai_to_final"] = self.calculate_similarity(
                ai_processed_content, final_content
            )
        
        # Extract HTML metrics from sanitization
        if sanitization_result and sanitization_result.get("stats"):
            stats = sanitization_result["stats"]
            metrics["html_metrics"] = {
                "tags_removed": stats.get("tags_removed", 0),
                "attributes_removed": stats.get("attributes_removed", 0),
                "validation_errors": len(sanitization_result.get("validation_errors", [])),
                "is_valid_html": sanitization_result.get("is_valid", False)
            }
        
        # Extract validation metrics
        if validation_result:
            metrics["validation_metrics"] = {
                "similarity_passed": validation_result.get("similarity_passed", False),
                "length_passed": validation_result.get("length_passed", False),
                "overall_passed": validation_result.get("overall_passed", False),
                "similarity_score": validation_result.get("similarity_score", 0.0),
                "length_difference": validation_result.get("length_difference", 0)
            }
        
        return metrics
    
    def create_diff_visualization(
        self,
        original_content: str,
        processed_content: str,
        diff_format: str = "unified",
        enable_colors: bool = True
    ) -> str:
        """
        Create diff visualization between original and processed content.
        
        Args:
            original_content: Original content
            processed_content: Processed content
            diff_format: Diff format ("unified", "side_by_side", "json")
            enable_colors: Enable terminal colors
            
        Returns:
            Formatted diff string
        """
        from .diff_visualizer import DiffVisualizer, DiffFormat
        
        # Map string format to enum
        format_mapping = {
            "unified": DiffFormat.UNIFIED,
            "side_by_side": DiffFormat.SIDE_BY_SIDE,
            "json": DiffFormat.JSON
        }
        
        format_enum = format_mapping.get(diff_format.lower(), DiffFormat.UNIFIED)
        
        visualizer = DiffVisualizer(context_lines=3, enable_colors=enable_colors)
        diff_result = visualizer.generate_diff(
            original_content,
            processed_content,
            original_title="Original HTML",
            processed_title="Processed HTML"
        )
        
        return visualizer.format_diff(diff_result, format_enum)
    
    def validate_html_safety(self, html_content: str) -> Dict[str, Any]:
        """
        Validate HTML content for safety issues.
        
        Args:
            html_content: HTML content to validate
            
        Returns:
            Dictionary with validation results
        """
        from .html_sanitizer import HTMLSanitizer
        
        sanitizer = HTMLSanitizer()
        is_valid, errors = sanitizer.validate_html(html_content)
        
        return {
            "is_safe": is_valid,
            "validation_errors": errors,
            "error_count": len(errors),
            "has_forbidden_tags": any("forbidden" in error.lower() for error in errors),
            "has_javascript": any("javascript" in error.lower() for error in errors),
            "has_event_handlers": any("event handler" in error.lower() for error in errors)
        }
