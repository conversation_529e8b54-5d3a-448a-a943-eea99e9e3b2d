"""
Diff visualization service for HTML content changes.

This module provides git-style diff visualization for comparing original and processed
HTML content, with terminal color support and machine-readable output formats.
"""

import difflib
import json
import logging
from dataclasses import dataclass, asdict
from enum import Enum
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class DiffFormat(Enum):
    """Supported diff output formats."""
    UNIFIED = "unified"
    CONTEXT = "context"
    SIDE_BY_SIDE = "side_by_side"
    JSON = "json"


class ChangeType(Enum):
    """Types of changes in diff."""
    ADDED = "added"
    REMOVED = "removed"
    MODIFIED = "modified"
    UNCHANGED = "unchanged"


@dataclass
class DiffLine:
    """Represents a single line in a diff."""
    line_number_original: Optional[int]
    line_number_processed: Optional[int]
    content: str
    change_type: ChangeType
    context: bool = False  # True if this is context line (not a change)


@dataclass
class DiffChunk:
    """Represents a chunk of related changes in a diff."""
    original_start: int
    original_count: int
    processed_start: int
    processed_count: int
    lines: List[DiffLine]


@dataclass
class DiffResult:
    """Complete diff result with metadata."""
    original_title: str
    processed_title: str
    chunks: List[DiffChunk]
    summary: Dict[str, int]
    timestamp: datetime
    
    def __post_init__(self):
        """Calculate summary statistics."""
        self.summary = {
            "total_lines_added": sum(1 for chunk in self.chunks for line in chunk.lines if line.change_type == ChangeType.ADDED),
            "total_lines_removed": sum(1 for chunk in self.chunks for line in chunk.lines if line.change_type == ChangeType.REMOVED),
            "total_lines_modified": sum(1 for chunk in self.chunks for line in chunk.lines if line.change_type == ChangeType.MODIFIED),
            "total_chunks": len(self.chunks),
            "has_changes": len(self.chunks) > 0
        }


class TerminalColors:
    """ANSI color codes for terminal output."""
    
    RESET = "\033[0m"
    BOLD = "\033[1m"
    DIM = "\033[2m"
    
    # Text colors
    RED = "\033[91m"
    GREEN = "\033[92m"
    YELLOW = "\033[93m"
    BLUE = "\033[94m"
    MAGENTA = "\033[95m"
    CYAN = "\033[96m"
    WHITE = "\033[97m"
    GRAY = "\033[90m"
    
    # Background colors
    BG_RED = "\033[101m"
    BG_GREEN = "\033[102m"
    BG_YELLOW = "\033[103m"
    
    @classmethod
    def colorize(cls, text: str, color: str, bold: bool = False) -> str:
        """Apply color formatting to text."""
        prefix = cls.BOLD if bold else ""
        return f"{prefix}{color}{text}{cls.RESET}"
    
    @classmethod
    def is_terminal_color_supported(cls) -> bool:
        """Check if terminal supports color output."""
        import os
        import sys
        return (
            hasattr(sys.stdout, 'isatty') and 
            sys.stdout.isatty() and 
            os.environ.get('TERM') != 'dumb'
        )


class DiffVisualizer:
    """
    HTML content diff visualization with multiple output formats.
    
    Provides git-style unified diffs, side-by-side comparisons, and machine-readable
    JSON output for HTML content changes analysis.
    """
    
    def __init__(self, context_lines: int = 3, enable_colors: Optional[bool] = None):
        """
        Initialize diff visualizer.
        
        Args:
            context_lines: Number of context lines around changes
            enable_colors: Enable terminal colors (auto-detect if None)
        """
        self.context_lines = context_lines
        self.enable_colors = enable_colors if enable_colors is not None else TerminalColors.is_terminal_color_supported()
    
    def generate_diff(
        self,
        original_content: str,
        processed_content: str,
        original_title: str = "Original",
        processed_title: str = "Processed"
    ) -> DiffResult:
        """
        Generate comprehensive diff result.
        
        Args:
            original_content: Original HTML content
            processed_content: Processed HTML content
            original_title: Title for original content
            processed_title: Title for processed content
            
        Returns:
            DiffResult with detailed change information
        """
        # Split content into lines for comparison
        original_lines = original_content.splitlines(keepends=False)
        processed_lines = processed_content.splitlines(keepends=False)
        
        # Generate unified diff using difflib
        differ = difflib.unified_diff(
            original_lines,
            processed_lines,
            fromfile=original_title,
            tofile=processed_title,
            n=self.context_lines,
            lineterm=""
        )
        
        # Parse diff into structured format
        chunks = self._parse_unified_diff(list(differ), original_lines, processed_lines)
        
        result = DiffResult(
            original_title=original_title,
            processed_title=processed_title,
            chunks=chunks,
            summary={},  # Will be calculated in __post_init__
            timestamp=datetime.utcnow()
        )
        
        logger.debug(f"Generated diff with {len(chunks)} chunks: {result.summary}")
        
        return result
    
    def _parse_unified_diff(
        self,
        diff_lines: List[str],
        original_lines: List[str],
        processed_lines: List[str]
    ) -> List[DiffChunk]:
        """Parse unified diff output into structured chunks."""
        chunks = []
        current_chunk = None
        
        i = 0
        while i < len(diff_lines):
            line = diff_lines[i]
            
            # Skip file headers
            if line.startswith('---') or line.startswith('+++'):
                i += 1
                continue
            
            # Parse chunk header: @@ -start,count +start,count @@
            if line.startswith('@@'):
                # Save previous chunk if exists
                if current_chunk and current_chunk.lines:
                    chunks.append(current_chunk)
                
                # Parse chunk header
                header_match = line.split()
                if len(header_match) >= 3:
                    original_range = header_match[1][1:]  # Remove '-'
                    processed_range = header_match[2][1:]  # Remove '+'
                    
                    orig_start, orig_count = self._parse_range(original_range)
                    proc_start, proc_count = self._parse_range(processed_range)
                    
                    current_chunk = DiffChunk(
                        original_start=orig_start,
                        original_count=orig_count,
                        processed_start=proc_start,
                        processed_count=proc_count,
                        lines=[]
                    )
                i += 1
                continue
            
            # Process change lines
            if current_chunk is not None:
                if line.startswith('-'):
                    # Removed line
                    current_chunk.lines.append(DiffLine(
                        line_number_original=self._calculate_original_line_number(current_chunk),
                        line_number_processed=None,
                        content=line[1:],  # Remove prefix
                        change_type=ChangeType.REMOVED
                    ))
                elif line.startswith('+'):
                    # Added line
                    current_chunk.lines.append(DiffLine(
                        line_number_original=None,
                        line_number_processed=self._calculate_processed_line_number(current_chunk),
                        content=line[1:],  # Remove prefix
                        change_type=ChangeType.ADDED
                    ))
                elif line.startswith(' '):
                    # Context line
                    current_chunk.lines.append(DiffLine(
                        line_number_original=self._calculate_original_line_number(current_chunk),
                        line_number_processed=self._calculate_processed_line_number(current_chunk),
                        content=line[1:],  # Remove prefix
                        change_type=ChangeType.UNCHANGED,
                        context=True
                    ))
            
            i += 1
        
        # Add final chunk
        if current_chunk and current_chunk.lines:
            chunks.append(current_chunk)
        
        return chunks
    
    def _parse_range(self, range_str: str) -> Tuple[int, int]:
        """Parse range string like '1,5' or '1' to (start, count)."""
        if ',' in range_str:
            start, count = range_str.split(',')
            return int(start), int(count)
        else:
            return int(range_str), 1
    
    def _calculate_original_line_number(self, chunk: DiffChunk) -> int:
        """Calculate current original line number in chunk."""
        removed_count = sum(1 for line in chunk.lines if line.change_type in (ChangeType.REMOVED, ChangeType.UNCHANGED))
        return chunk.original_start + removed_count
    
    def _calculate_processed_line_number(self, chunk: DiffChunk) -> int:
        """Calculate current processed line number in chunk."""
        added_count = sum(1 for line in chunk.lines if line.change_type in (ChangeType.ADDED, ChangeType.UNCHANGED))
        return chunk.processed_start + added_count
    
    def format_unified_diff(self, diff_result: DiffResult) -> str:
        """
        Format diff result as unified diff output.
        
        Args:
            diff_result: Diff result to format
            
        Returns:
            Formatted unified diff string
        """
        if not diff_result.chunks:
            return "No differences found."
        
        output_lines = []
        
        # File headers
        if self.enable_colors:
            output_lines.append(TerminalColors.colorize(f"--- {diff_result.original_title}", TerminalColors.RED, bold=True))
            output_lines.append(TerminalColors.colorize(f"+++ {diff_result.processed_title}", TerminalColors.GREEN, bold=True))
        else:
            output_lines.append(f"--- {diff_result.original_title}")
            output_lines.append(f"+++ {diff_result.processed_title}")
        
        # Process each chunk
        for chunk in diff_result.chunks:
            # Chunk header
            header = f"@@ -{chunk.original_start},{chunk.original_count} +{chunk.processed_start},{chunk.processed_count} @@"
            if self.enable_colors:
                output_lines.append(TerminalColors.colorize(header, TerminalColors.CYAN, bold=True))
            else:
                output_lines.append(header)
            
            # Chunk lines
            for line in chunk.lines:
                if line.change_type == ChangeType.REMOVED:
                    formatted_line = f"-{line.content}"
                    if self.enable_colors:
                        formatted_line = TerminalColors.colorize(formatted_line, TerminalColors.RED)
                elif line.change_type == ChangeType.ADDED:
                    formatted_line = f"+{line.content}"
                    if self.enable_colors:
                        formatted_line = TerminalColors.colorize(formatted_line, TerminalColors.GREEN)
                else:
                    formatted_line = f" {line.content}"
                    if self.enable_colors and line.context:
                        formatted_line = TerminalColors.colorize(formatted_line, TerminalColors.GRAY)
                
                output_lines.append(formatted_line)
        
        # Add summary
        output_lines.append("")
        summary = f"Summary: +{diff_result.summary['total_lines_added']} -{diff_result.summary['total_lines_removed']} (~{diff_result.summary['total_chunks']} chunks)"
        if self.enable_colors:
            output_lines.append(TerminalColors.colorize(summary, TerminalColors.BLUE, bold=True))
        else:
            output_lines.append(summary)
        
        return "\n".join(output_lines)
    
    def format_side_by_side_diff(self, diff_result: DiffResult, width: int = 120) -> str:
        """
        Format diff result as side-by-side comparison.
        
        Args:
            diff_result: Diff result to format
            width: Total terminal width for formatting
            
        Returns:
            Formatted side-by-side diff string
        """
        if not diff_result.chunks:
            return "No differences found."
        
        half_width = (width - 3) // 2  # Account for separator
        output_lines = []
        
        # Header
        header_line = f"{diff_result.original_title:<{half_width}} | {diff_result.processed_title}"
        separator_line = "=" * width
        
        if self.enable_colors:
            output_lines.append(TerminalColors.colorize(header_line, TerminalColors.BOLD))
            output_lines.append(TerminalColors.colorize(separator_line, TerminalColors.GRAY))
        else:
            output_lines.append(header_line)
            output_lines.append(separator_line)
        
        for chunk in diff_result.chunks:
            # Group lines by pairs for side-by-side display
            original_lines = []
            processed_lines = []
            
            for line in chunk.lines:
                if line.change_type in (ChangeType.REMOVED, ChangeType.UNCHANGED):
                    original_lines.append(line)
                if line.change_type in (ChangeType.ADDED, ChangeType.UNCHANGED):
                    processed_lines.append(line)
            
            # Display side by side
            max_lines = max(len(original_lines), len(processed_lines))
            
            for i in range(max_lines):
                orig_line = original_lines[i] if i < len(original_lines) else None
                proc_line = processed_lines[i] if i < len(processed_lines) else None
                
                # Format original side
                if orig_line:
                    orig_text = orig_line.content[:half_width-2]
                    if orig_line.change_type == ChangeType.REMOVED:
                        prefix = "- "
                        if self.enable_colors:
                            orig_text = TerminalColors.colorize(orig_text, TerminalColors.RED)
                    else:
                        prefix = "  "
                    orig_side = f"{prefix}{orig_text}"
                else:
                    orig_side = ""
                
                # Format processed side
                if proc_line:
                    proc_text = proc_line.content[:half_width-2]
                    if proc_line.change_type == ChangeType.ADDED:
                        prefix = "+ "
                        if self.enable_colors:
                            proc_text = TerminalColors.colorize(proc_text, TerminalColors.GREEN)
                    else:
                        prefix = "  "
                    proc_side = f"{prefix}{proc_text}"
                else:
                    proc_side = ""
                
                # Combine sides
                line_output = f"{orig_side:<{half_width}} | {proc_side}"
                output_lines.append(line_output)
            
            output_lines.append("")  # Blank line between chunks
        
        return "\n".join(output_lines)
    
    def format_json_diff(self, diff_result: DiffResult) -> str:
        """
        Format diff result as JSON for machine processing.
        
        Args:
            diff_result: Diff result to format
            
        Returns:
            JSON string representation
        """
        # Convert to JSON-serializable format
        json_data = asdict(diff_result)
        
        # Convert enums to strings
        def convert_enums(obj):
            if isinstance(obj, dict):
                return {k: convert_enums(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_enums(item) for item in obj]
            elif isinstance(obj, ChangeType):
                return obj.value
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        json_data = convert_enums(json_data)
        
        return json.dumps(json_data, indent=2, ensure_ascii=False)
    
    def format_diff(
        self,
        diff_result: DiffResult,
        format_type: DiffFormat = DiffFormat.UNIFIED,
        **kwargs
    ) -> str:
        """
        Format diff result in specified format.
        
        Args:
            diff_result: Diff result to format
            format_type: Output format type
            **kwargs: Additional formatting options
            
        Returns:
            Formatted diff string
        """
        if format_type == DiffFormat.UNIFIED:
            return self.format_unified_diff(diff_result)
        elif format_type == DiffFormat.SIDE_BY_SIDE:
            width = kwargs.get('width', 120)
            return self.format_side_by_side_diff(diff_result, width)
        elif format_type == DiffFormat.JSON:
            return self.format_json_diff(diff_result)
        else:
            raise ValueError(f"Unsupported format type: {format_type}")
    
    def compare_html_files(
        self,
        original_file: str,
        processed_file: str,
        format_type: DiffFormat = DiffFormat.UNIFIED
    ) -> str:
        """
        Compare two HTML files and return formatted diff.
        
        Args:
            original_file: Path to original HTML file
            processed_file: Path to processed HTML file
            format_type: Output format type
            
        Returns:
            Formatted diff string
        """
        try:
            with open(original_file, 'r', encoding='utf-8') as f:
                original_content = f.read()
            
            with open(processed_file, 'r', encoding='utf-8') as f:
                processed_content = f.read()
            
            diff_result = self.generate_diff(
                original_content,
                processed_content,
                original_file,
                processed_file
            )
            
            return self.format_diff(diff_result, format_type)
        
        except Exception as e:
            logger.error(f"File comparison failed: {e}")
            raise
    
    def get_diff_statistics(self, diff_result: DiffResult) -> Dict[str, Union[int, float]]:
        """
        Calculate detailed statistics from diff result.
        
        Args:
            diff_result: Diff result to analyze
            
        Returns:
            Dictionary with diff statistics
        """
        if not diff_result.chunks:
            return {
                "total_changes": 0,
                "lines_added": 0,
                "lines_removed": 0,
                "lines_modified": 0,
                "chunks_count": 0,
                "change_percentage": 0.0
            }
        
        stats = diff_result.summary.copy()
        
        # Calculate total lines in both versions
        total_original_lines = sum(
            len([line for line in chunk.lines if line.line_number_original is not None])
            for chunk in diff_result.chunks
        )
        
        total_processed_lines = sum(
            len([line for line in chunk.lines if line.line_number_processed is not None])
            for chunk in diff_result.chunks
        )
        
        # Calculate change percentage
        total_changes = stats["total_lines_added"] + stats["total_lines_removed"]
        max_lines = max(total_original_lines, total_processed_lines)
        change_percentage = (total_changes / max_lines * 100) if max_lines > 0 else 0.0
        
        stats.update({
            "total_changes": total_changes,
            "change_percentage": round(change_percentage, 2),
            "original_total_lines": total_original_lines,
            "processed_total_lines": total_processed_lines
        })
        
        return stats