"""File locking service for preventing concurrent executions."""

import logging
from contextlib import contextmanager
from pathlib import Path
from typing import Generator, Optional

import portalocker

from ..config import get_settings

logger = logging.getLogger(__name__)


class FileLockError(Exception):
    """Exception raised for file locking errors."""
    pass


class FileLockService:
    """Service for managing file locks."""
    
    def __init__(self, lock_dir: Optional[str] = None):
        """Initialize file lock service."""
        self.settings = get_settings()
        self.lock_dir = Path(lock_dir) if lock_dir else self.settings.data_dir_path / "locks"
        self.lock_dir.mkdir(parents=True, exist_ok=True)
    
    def get_lock_path(self, site_name: str) -> Path:
        """Get lock file path for a site."""
        return self.lock_dir / f"{site_name}.lock"
    
    @contextmanager
    def acquire_lock(self, site_name: str, timeout: int = 30) -> Generator[None, None, None]:
        """Acquire a file lock for the given site."""
        lock_path = self.get_lock_path(site_name)
        
        logger.info(f"Acquiring lock for site '{site_name}' at {lock_path}")
        
        try:
            with open(lock_path, "w") as lock_file:
                # Try to acquire exclusive lock
                portalocker.lock(lock_file, portalocker.LOCK_EX | portalocker.LOCK_NB)
                
                # Write process info to lock file
                lock_file.write(f"site={site_name}\npid={Path.cwd()}\n")
                lock_file.flush()
                
                logger.info(f"Lock acquired for site '{site_name}'")
                
                try:
                    yield
                finally:
                    # Release lock
                    portalocker.unlock(lock_file)
                    logger.info(f"Lock released for site '{site_name}'")
                    
        except portalocker.LockException as e:
            logger.error(f"Failed to acquire lock for site '{site_name}': {e}")
            raise FileLockError(f"Another process is already running for site '{site_name}'") from e
        except Exception as e:
            logger.error(f"Unexpected error while acquiring lock for site '{site_name}': {e}")
            raise FileLockError(f"Failed to acquire lock for site '{site_name}': {e}") from e
        finally:
            # Clean up lock file if it exists
            try:
                if lock_path.exists():
                    lock_path.unlink()
            except Exception as e:
                logger.warning(f"Failed to clean up lock file {lock_path}: {e}")
    
    def is_locked(self, site_name: str) -> bool:
        """Check if a site is currently locked."""
        lock_path = self.get_lock_path(site_name)
        
        if not lock_path.exists():
            return False
        
        try:
            with open(lock_path, "r") as lock_file:
                # Try to acquire non-blocking lock
                portalocker.lock(lock_file, portalocker.LOCK_EX | portalocker.LOCK_NB)
                portalocker.unlock(lock_file)
                return False
        except portalocker.LockException:
            return True
        except Exception:
            # If we can't determine, assume it's locked for safety
            return True


# Global file lock service
_file_lock_service: Optional[FileLockService] = None


def get_file_lock_service() -> FileLockService:
    """Get global file lock service."""
    global _file_lock_service
    if _file_lock_service is None:
        _file_lock_service = FileLockService()
    return _file_lock_service