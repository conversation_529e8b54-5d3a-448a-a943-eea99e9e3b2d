"""
HTML sanitization service for WooCommerce product content formatting.

This module provides secure HTML sanitization with a whitelist of approved tags and attributes,
HTML validation, and pretty-printing functionality as required by Phase 4 specifications.
"""

import logging
import re
from typing import Dict, List, Optional, Set, Tuple, Any
from urllib.parse import urlparse

import bleach
from bs4 import BeautifulSoup, Tag
from bs4.formatter import HTMLFormatter

logger = logging.getLogger(__name__)


class HTMLSanitizationError(Exception):
    """Raised when HTML sanitization fails."""
    
    def __init__(self, message: str, original_html: Optional[str] = None):
        super().__init__(message)
        self.original_html = original_html


class CompactHTMLFormatter(HTMLFormatter):
    """Custom HTML formatter for pretty-printing with consistent spacing."""
    
    def attributes(self, tag: Tag) -> str:
        """Format tag attributes in a consistent order."""
        if not tag.attrs:
            return ""
        
        # Sort attributes for consistent output
        sorted_attrs = sorted(tag.attrs.items())
        formatted = []
        
        for key, value in sorted_attrs:
            if isinstance(value, list):
                value = " ".join(value)
            formatted.append(f'{key}="{value}"')
        
        return " " + " ".join(formatted) if formatted else ""


class HTMLSanitizer:
    """
    HTML sanitizer with whitelist-based tag and attribute filtering.
    
    Implements Phase 4 requirements:
    - Whitelist of approved HTML tags and attributes
    - Removal of dangerous tags (script, style, iframe)
    - HTML validation and pretty-printing
    - Link security enhancements
    """
    
    # Approved HTML tags as per PRD 4.1
    ALLOWED_TAGS: Set[str] = {
        'section', 'h2', 'h3', 'p', 'ul', 'ol', 'li', 'strong', 'em',
        'a', 'table', 'thead', 'tbody', 'tr', 'th', 'td', 'code', 'br'
    }
    
    # Approved attributes per tag as per PRD 4.1
    ALLOWED_ATTRIBUTES: Dict[str, List[str]] = {
        'a': ['href', 'title', 'target', 'rel'],
        '*': []  # Global attributes (none allowed by default)
    }
    
    # Dangerous tags that must be removed
    FORBIDDEN_TAGS: Set[str] = {
        'script', 'style', 'iframe', 'object', 'embed', 'applet',
        'form', 'input', 'button', 'select', 'textarea',
        'meta', 'link', 'base', 'head', 'html', 'body'
    }
    
    def __init__(self):
        """Initialize HTML sanitizer with security settings."""
        self.formatter = CompactHTMLFormatter()
    
    def _validate_link_href(self, href: str) -> str:
        """
        Validate and sanitize href attributes for links.
        
        Args:
            href: Original href value
            
        Returns:
            Sanitized href value
            
        Raises:
            HTMLSanitizationError: If href is malicious
        """
        if not href or not href.strip():
            return ""
        
        href = href.strip()
        
        # Block javascript: and data: protocols
        if href.lower().startswith(('javascript:', 'data:', 'vbscript:')):
            logger.warning(f"Blocked dangerous href protocol: {href[:50]}...")
            raise HTMLSanitizationError(f"Dangerous href protocol detected: {href[:50]}...")
        
        # Allow relative URLs, mailto:, tel:, and http/https
        allowed_protocols = {'http', 'https', 'mailto', 'tel', 'ftp'}
        
        if '://' in href:
            try:
                parsed = urlparse(href)
                if parsed.scheme.lower() not in allowed_protocols:
                    logger.warning(f"Blocked href with disallowed protocol: {parsed.scheme}")
                    raise HTMLSanitizationError(f"Disallowed protocol: {parsed.scheme}")
            except Exception as e:
                logger.warning(f"Invalid URL format: {href[:50]}... - {e}")
                raise HTMLSanitizationError(f"Invalid URL format: {href[:50]}...")
        
        return href
    
    def _sanitize_attributes(self, tag_name: str, attrs: Dict[str, Any]) -> Dict[str, str]:
        """
        Sanitize attributes for a specific tag.
        
        Args:
            tag_name: HTML tag name
            attrs: Dictionary of attributes
            
        Returns:
            Dictionary of sanitized attributes
        """
        if not attrs:
            return {}
        
        allowed_attrs = self.ALLOWED_ATTRIBUTES.get(tag_name, [])
        global_attrs = self.ALLOWED_ATTRIBUTES.get('*', [])
        all_allowed = set(allowed_attrs + global_attrs)
        
        sanitized = {}
        
        for attr_name, attr_value in attrs.items():
            if attr_name.lower() not in [a.lower() for a in all_allowed]:
                logger.debug(f"Removed disallowed attribute '{attr_name}' from <{tag_name}>")
                continue
            
            # Convert attribute value to string
            if isinstance(attr_value, list):
                attr_value = " ".join(str(v) for v in attr_value)
            else:
                attr_value = str(attr_value)
            
            # Special handling for href attributes
            if attr_name.lower() == 'href' and tag_name.lower() == 'a':
                try:
                    attr_value = self._validate_link_href(attr_value)
                except HTMLSanitizationError:
                    continue  # Skip this attribute if validation fails
            
            # Special handling for target attribute - ensure noopener/noreferrer
            if attr_name.lower() == 'target' and attr_value.lower() == '_blank':
                sanitized['target'] = '_blank'
                # Ensure rel attribute for security
                if 'rel' not in [k.lower() for k in sanitized.keys()]:
                    sanitized['rel'] = 'noopener noreferrer'
                continue
            
            # Add rel attribute security for _blank targets
            if attr_name.lower() == 'rel' and any(k.lower() == 'target' and v == '_blank' for k, v in sanitized.items()):
                # Ensure noopener and noreferrer are present
                rel_parts = attr_value.split()
                if 'noopener' not in [r.lower() for r in rel_parts]:
                    rel_parts.append('noopener')
                if 'noreferrer' not in [r.lower() for r in rel_parts]:
                    rel_parts.append('noreferrer')
                attr_value = ' '.join(rel_parts)
            
            sanitized[attr_name] = attr_value
        
        return sanitized
    
    def sanitize_html(self, html_content: str, strict_mode: bool = False) -> str:
        """
        Sanitize HTML content using whitelist approach.
        
        Args:
            html_content: Raw HTML content
            strict_mode: If True, raise exceptions on dangerous content
            
        Returns:
            Sanitized HTML content
            
        Raises:
            HTMLSanitizationError: If dangerous content found in strict mode
        """
        if not html_content or not html_content.strip():
            return ""
        
        original_content = html_content
        logger.debug(f"Sanitizing HTML content ({len(html_content)} chars)")
        
        try:
            # Check for forbidden tags in original content if in strict mode
            if strict_mode:
                for forbidden_tag in self.FORBIDDEN_TAGS:
                    pattern = rf'<{forbidden_tag}[^>]*>.*?</{forbidden_tag}>'
                    if re.search(pattern, html_content, re.IGNORECASE | re.DOTALL):
                        raise HTMLSanitizationError(
                            f"Forbidden tag detected: <{forbidden_tag}>",
                            original_content
                        )
                    
                    # Check for self-closing forbidden tags
                    pattern = rf'<{forbidden_tag}[^>]*/?>'
                    if re.search(pattern, html_content, re.IGNORECASE):
                        raise HTMLSanitizationError(
                            f"Forbidden tag detected: <{forbidden_tag}>",
                            original_content
                        )
            
            # First pass: Use bleach for initial sanitization with protocol specification
            sanitized = bleach.clean(
                html_content,
                tags=list(self.ALLOWED_TAGS),
                attributes=self.ALLOWED_ATTRIBUTES,
                protocols=['http', 'https', 'mailto', 'tel', 'ftp'],  # Specify allowed protocols
                strip=True,  # Remove disallowed tags entirely
                strip_comments=True
            )
            
            # Second pass: Additional attribute sanitization using BeautifulSoup
            soup = BeautifulSoup(sanitized, 'html.parser')
            
            for tag in soup.find_all():
                if tag.name not in self.ALLOWED_TAGS:
                    logger.warning(f"Removing disallowed tag: <{tag.name}>")
                    tag.unwrap()  # Remove tag but keep content
                    continue
                
                # Sanitize attributes
                original_attrs = dict(tag.attrs) if tag.attrs else {}
                sanitized_attrs = self._sanitize_attributes(tag.name, original_attrs)
                
                # Clear all attributes and set sanitized ones
                tag.attrs.clear()
                tag.attrs.update(sanitized_attrs)
            
            # Convert back to string
            result = str(soup)
            
            # Remove any remaining inline CSS (style attributes should already be gone)
            result = re.sub(r'style\s*=\s*["\'][^"\']*["\']', '', result, flags=re.IGNORECASE)
            
            logger.debug(f"HTML sanitization completed: {len(original_content)} -> {len(result)} chars")
            
            return result.strip()
        
        except HTMLSanitizationError:
            raise
        except Exception as e:
            logger.error(f"HTML sanitization failed: {e}")
            if strict_mode:
                raise HTMLSanitizationError(f"HTML sanitization failed: {e}", original_content)
            return ""
    
    def validate_html(self, html_content: str) -> Tuple[bool, List[str]]:
        """
        Validate HTML structure and well-formedness.
        
        Args:
            html_content: HTML content to validate
            
        Returns:
            Tuple of (is_valid, list_of_errors)
        """
        if not html_content:
            return True, []
        
        errors = []
        
        try:
            # Parse with BeautifulSoup to check well-formedness
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Check for parsing errors (malformed HTML)
            if soup.original_encoding is None and html_content.strip():
                # BeautifulSoup couldn't detect encoding, might indicate issues
                logger.debug("HTML encoding detection issues detected")
            
            # Check for unclosed tags (BeautifulSoup auto-closes them)
            original_tag_count = len(re.findall(r'<(\w+)[^>]*>', html_content))
            parsed_tag_count = len(soup.find_all())
            
            if abs(original_tag_count - parsed_tag_count) > 2:  # Allow some variance
                errors.append(f"Potential unclosed tags detected (original: {original_tag_count}, parsed: {parsed_tag_count})")
            
            # Check for forbidden tags
            for tag in soup.find_all():
                if tag.name in self.FORBIDDEN_TAGS:
                    errors.append(f"Forbidden tag found: <{tag.name}>")
            
            # Check for dangerous attributes
            for tag in soup.find_all():
                if tag.attrs:
                    for attr_name, attr_value in tag.attrs.items():
                        if isinstance(attr_value, str):
                            # Check for javascript in attribute values
                            if 'javascript:' in attr_value.lower():
                                errors.append(f"JavaScript detected in {tag.name}.{attr_name}")
                            
                            # Check for on* event handlers
                            if attr_name.lower().startswith('on'):
                                errors.append(f"Event handler attribute found: {tag.name}.{attr_name}")
            
            is_valid = len(errors) == 0
            
            if is_valid:
                logger.debug("HTML validation passed")
            else:
                logger.warning(f"HTML validation failed with {len(errors)} errors")
            
            return is_valid, errors
        
        except Exception as e:
            logger.error(f"HTML validation error: {e}")
            errors.append(f"HTML parsing error: {str(e)}")
            return False, errors
    
    def pretty_print_html(self, html_content: str, indent: int = 2) -> str:
        """
        Pretty-print HTML content with consistent formatting.
        
        Args:
            html_content: HTML content to format
            indent: Number of spaces per indentation level
            
        Returns:
            Pretty-printed HTML content
        """
        if not html_content or not html_content.strip():
            return ""
        
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # Use prettify with custom indent
            formatted = soup.prettify(formatter=self.formatter)
            
            # Adjust indentation if needed
            if indent != 1:  # BeautifulSoup default is 1 space
                lines = formatted.split('\n')
                adjusted_lines = []
                
                for line in lines:
                    if line.strip():  # Skip empty lines
                        # Count leading spaces
                        leading_spaces = len(line) - len(line.lstrip())
                        # Adjust indentation
                        new_indent = (leading_spaces * indent)
                        adjusted_lines.append(' ' * new_indent + line.lstrip())
                    else:
                        adjusted_lines.append('')
                
                formatted = '\n'.join(adjusted_lines)
            
            # Clean up extra blank lines
            formatted = re.sub(r'\n\s*\n\s*\n', '\n\n', formatted)
            
            return formatted.strip()
        
        except Exception as e:
            logger.error(f"HTML pretty-printing failed: {e}")
            return html_content  # Return original if formatting fails
    
    def process_html(self, html_content: str, pretty_print: bool = True, strict_mode: bool = False) -> Dict[str, Any]:
        """
        Complete HTML processing pipeline: sanitize, validate, and format.
        
        Args:
            html_content: Raw HTML content
            pretty_print: Whether to pretty-print the output
            strict_mode: Whether to use strict mode for sanitization
            
        Returns:
            Dictionary containing processed HTML and metadata
        """
        result = {
            "original_html": html_content,
            "sanitized_html": "",
            "formatted_html": "",
            "is_valid": False,
            "validation_errors": [],
            "processing_errors": [],
            "stats": {
                "original_length": len(html_content) if html_content else 0,
                "sanitized_length": 0,
                "formatted_length": 0,
                "tags_removed": 0,
                "attributes_removed": 0
            }
        }
        
        try:
            # Step 1: Sanitize HTML
            sanitized = self.sanitize_html(html_content, strict_mode=strict_mode)
            result["sanitized_html"] = sanitized
            result["stats"]["sanitized_length"] = len(sanitized)
            
            # Step 2: Validate HTML
            is_valid, validation_errors = self.validate_html(sanitized)
            result["is_valid"] = is_valid
            result["validation_errors"] = validation_errors
            
            # Step 3: Pretty-print if requested
            if pretty_print and sanitized:
                formatted = self.pretty_print_html(sanitized)
                result["formatted_html"] = formatted
                result["stats"]["formatted_length"] = len(formatted)
            else:
                result["formatted_html"] = sanitized
                result["stats"]["formatted_length"] = len(sanitized)
            
            logger.info(f"HTML processing completed successfully: {result['stats']['original_length']} -> {result['stats']['formatted_length']} chars")
            
        except HTMLSanitizationError as e:
            result["processing_errors"].append(str(e))
            logger.error(f"HTML processing failed: {e}")
        except Exception as e:
            result["processing_errors"].append(f"Unexpected error: {str(e)}")
            logger.error(f"Unexpected HTML processing error: {e}")
        
        return result