"""MySQL connection service for WooCommerce database."""

import logging
from contextlib import contextmanager
from typing import Generator, Optional, Dict, Any
from datetime import datetime

from sqlalchemy import create_engine, Engine, text
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.exc import SQLAlchemyError, OperationalError
import pymysql

from ..models import Site
from ..config import get_settings

logger = logging.getLogger(__name__)


class MySQLConnectionError(Exception):
    """Exception raised for MySQL connection errors."""
    pass


class WooCommerceConnection:
    """MySQL connection manager for WooCommerce database."""
    
    def __init__(self, site: Site):
        """Initialize connection for a specific site."""
        self.site = site
        self.settings = get_settings()
        self._engine: Optional[Engine] = None
        self._session_maker: Optional[sessionmaker] = None
    
    @property
    def engine(self) -> Engine:
        """Get SQLAlchemy engine for this site."""
        if self._engine is None:
            connection_string = self._build_connection_string()
            self._engine = create_engine(
                connection_string,
                pool_pre_ping=True,
                pool_recycle=3600,
                pool_size=5,
                max_overflow=10,
                connect_args={
                    "charset": "utf8mb4",
                    "connect_timeout": 30,
                    "read_timeout": 30,
                    "write_timeout": 30
                },
                echo=False  # Set to True for SQL debugging
            )
        return self._engine
    
    @property
    def session_maker(self) -> sessionmaker:
        """Get session maker."""
        if self._session_maker is None:
            self._session_maker = sessionmaker(
                bind=self.engine,
                autocommit=False,
                autoflush=False
            )
        return self._session_maker
    
    def _build_connection_string(self) -> str:
        """Build MySQL connection string."""
        return (
            f"mysql+pymysql://{self.site.db_user}:{self.site.db_password}"
            f"@{self.site.db_host}:{self.site.db_port}/{self.site.db_name}"
            f"?charset=utf8mb4"
        )
    
    def test_connection(self) -> Dict[str, Any]:
        """Test database connection and return status info."""
        try:
            with self.get_session() as session:
                # Test basic connectivity
                result = session.execute(text("SELECT 1 as test")).first()
                
                # Check if WooCommerce tables exist
                tables_query = text("""
                    SELECT table_name 
                    FROM information_schema.tables 
                    WHERE table_schema = :db_name 
                    AND table_name IN (:posts_table, :postmeta_table, :terms_table, :term_relationships_table)
                """)
                
                tables_result = session.execute(tables_query, {
                    "db_name": self.site.db_name,
                    "posts_table": f"{self.site.db_table_prefix}posts",
                    "postmeta_table": f"{self.site.db_table_prefix}postmeta",
                    "terms_table": f"{self.site.db_table_prefix}terms",
                    "term_relationships_table": f"{self.site.db_table_prefix}term_relationships"
                }).fetchall()
                
                found_tables = [row[0] for row in tables_result]
                expected_tables = [
                    f"{self.site.db_table_prefix}posts",
                    f"{self.site.db_table_prefix}postmeta",
                    f"{self.site.db_table_prefix}terms",
                    f"{self.site.db_table_prefix}term_relationships"
                ]
                
                # Count products
                products_query = text(f"""
                    SELECT COUNT(*) as count 
                    FROM {self.site.db_table_prefix}posts 
                    WHERE post_type = 'product' 
                    AND post_status IN ('publish', 'draft')
                """)
                
                products_count = session.execute(products_query).scalar()
                
                return {
                    "status": "success",
                    "connection_test": result[0] == 1,
                    "tables_found": found_tables,
                    "missing_tables": [t for t in expected_tables if t not in found_tables],
                    "products_count": products_count,
                    "tested_at": datetime.utcnow().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Connection test failed for site {self.site.name}: {e}")
            return {
                "status": "error",
                "error": str(e),
                "error_type": type(e).__name__,
                "tested_at": datetime.utcnow().isoformat()
            }
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get a database session with automatic cleanup."""
        session = self.session_maker()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error for site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Database operation failed: {e}") from e
        finally:
            session.close()
    
    @contextmanager
    def get_transaction(self) -> Generator[Session, None, None]:
        """Get a database session with explicit transaction control."""
        session = self.session_maker()
        transaction = session.begin()
        try:
            yield session
            transaction.commit()
        except Exception as e:
            transaction.rollback()
            logger.error(f"Transaction failed for site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Transaction failed: {e}") from e
        finally:
            session.close()
    
    def execute_with_retry(self, query: str, params: Optional[Dict[str, Any]] = None, 
                          max_retries: int = 3) -> Any:
        """Execute query with retry logic."""
        last_exception = None
        
        for attempt in range(max_retries):
            try:
                with self.get_session() as session:
                    if params:
                        result = session.execute(text(query), params)
                    else:
                        result = session.execute(text(query))
                    
                    # Handle different result types
                    if result.returns_rows:
                        return result.fetchall()
                    else:
                        return result.rowcount
                        
            except (OperationalError, pymysql.Error) as e:
                last_exception = e
                logger.warning(
                    f"Query attempt {attempt + 1}/{max_retries} failed for site {self.site.name}: {e}"
                )
                
                if attempt < max_retries - 1:
                    # Wait before retry with exponential backoff
                    import time
                    backoff_time = self.settings.retry_backoff_intervals[attempt]
                    time.sleep(backoff_time)
                    
                    # Recreate engine to handle connection issues
                    self._engine = None
                    self._session_maker = None
                    
            except Exception as e:
                # Non-retryable error
                logger.error(f"Non-retryable error for site {self.site.name}: {e}")
                raise MySQLConnectionError(f"Query execution failed: {e}") from e
        
        # All retries failed
        logger.error(f"All retry attempts failed for site {self.site.name}")
        raise MySQLConnectionError(
            f"Query failed after {max_retries} attempts: {last_exception}"
        ) from last_exception
    
    def close(self) -> None:
        """Close database connection."""
        if self._engine:
            self._engine.dispose()
            self._engine = None
            self._session_maker = None
            logger.info(f"Connection closed for site {self.site.name}")


# Connection pool manager
_connection_pool: Dict[str, WooCommerceConnection] = {}


def get_woocommerce_connection(site: Site) -> WooCommerceConnection:
    """Get or create WooCommerce connection for site."""
    if site.name not in _connection_pool:
        _connection_pool[site.name] = WooCommerceConnection(site)
    return _connection_pool[site.name]


def close_all_connections() -> None:
    """Close all database connections."""
    for connection in _connection_pool.values():
        connection.close()
    _connection_pool.clear()
    logger.info("All database connections closed")