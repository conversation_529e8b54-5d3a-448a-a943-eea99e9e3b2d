"""OpenRouter API client for LLM integration."""

import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import httpx
from pydantic import BaseModel, Field

from ..config.settings import get_settings


logger = logging.getLogger(__name__)


class OpenRouterRequest(BaseModel):
    """OpenRouter API request model."""
    
    model: str
    messages: List[Dict[str, str]]
    temperature: float = Field(default=0.0)
    max_tokens: Optional[int] = Field(default=None)
    top_p: Optional[float] = Field(default=None)
    top_k: Optional[int] = Field(default=None)
    frequency_penalty: Optional[float] = Field(default=None)
    presence_penalty: Optional[float] = Field(default=None)
    repetition_penalty: Optional[float] = Field(default=None)
    stream: bool = Field(default=False)


class OpenRouterResponse(BaseModel):
    """OpenRouter API response model."""
    
    id: str
    object: str
    created: int
    model: str
    choices: List[Dict[str, Any]]
    usage: Dict[str, int]


class OpenRouterError(Exception):
    """OpenRouter API error."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, error_type: Optional[str] = None):
        self.message = message
        self.status_code = status_code
        self.error_type = error_type
        super().__init__(self.message)


class CircuitBreaker:
    """Circuit breaker for service degradation protection."""
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.state = "closed"  # closed, open, half_open
    
    def can_execute(self) -> bool:
        """Check if execution is allowed."""
        if self.state == "closed":
            return True
        
        if self.state == "open":
            if (datetime.utcnow() - self.last_failure_time).seconds >= self.recovery_timeout:
                self.state = "half_open"
                return True
            return False
        
        # half_open state
        return True
    
    def record_success(self) -> None:
        """Record successful execution."""
        self.failure_count = 0
        self.state = "closed"
        self.last_failure_time = None
    
    def record_failure(self) -> None:
        """Record failed execution."""
        self.failure_count += 1
        self.last_failure_time = datetime.utcnow()
        
        if self.failure_count >= self.failure_threshold:
            self.state = "open"
            logger.warning(f"Circuit breaker opened after {self.failure_count} failures")


class OpenRouterClient:
    """HTTP client for OpenRouter API with retry and circuit breaker."""
    
    def __init__(self):
        self.settings = get_settings()
        self.base_url = self.settings.openrouter_base_url.rstrip('/')
        self.api_key = self.settings.openrouter_api_key
        self.default_model = self.settings.openrouter_default_model
        self.fallback_model = self.settings.openrouter_fallback_model
        self.temperature = self.settings.openrouter_temperature
        self.max_attempts = self.settings.retry_max_attempts
        self.backoff_intervals = self.settings.retry_backoff_intervals
        
        self.circuit_breaker = CircuitBreaker()
        
        # HTTP client configuration
        self.timeout = httpx.Timeout(30.0, connect=10.0)
        self.limits = httpx.Limits(max_keepalive_connections=5, max_connections=10)
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers."""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://structura-ai.local",
            "X-Title": "Structura AI - WooCommerce HTML Formatter"
        }
    
    async def _make_request(
        self,
        request_data: OpenRouterRequest,
        use_fallback: bool = False
    ) -> OpenRouterResponse:
        """Make HTTP request to OpenRouter API."""
        if not self.circuit_breaker.can_execute():
            raise OpenRouterError(
                "Circuit breaker is open - service temporarily unavailable",
                status_code=503,
                error_type="circuit_breaker_open"
            )
        
        # Use fallback model if requested
        if use_fallback:
            request_data.model = self.fallback_model
            logger.info(f"Using fallback model: {self.fallback_model}")
        else:
            request_data.model = self.default_model
        
        url = f"{self.base_url}/chat/completions"
        headers = self._get_headers()
        
        try:
            async with httpx.AsyncClient(timeout=self.timeout, limits=self.limits) as client:
                response = await client.post(
                    url,
                    headers=headers,
                    json=request_data.model_dump(exclude_none=True)
                )
                
                if response.status_code == 200:
                    self.circuit_breaker.record_success()
                    response_data = response.json()
                    return OpenRouterResponse(**response_data)
                
                # Handle error responses
                error_data = response.json() if response.headers.get("content-type", "").startswith("application/json") else {}
                error_message = error_data.get("error", {}).get("message", f"HTTP {response.status_code}")
                error_type = error_data.get("error", {}).get("type", "http_error")
                
                self.circuit_breaker.record_failure()
                
                raise OpenRouterError(
                    f"OpenRouter API error: {error_message}",
                    status_code=response.status_code,
                    error_type=error_type
                )
        
        except httpx.TimeoutException as e:
            self.circuit_breaker.record_failure()
            raise OpenRouterError(
                f"Request timeout: {str(e)}",
                status_code=408,
                error_type="timeout"
            )
        except httpx.RequestError as e:
            self.circuit_breaker.record_failure()
            raise OpenRouterError(
                f"Network error: {str(e)}",
                status_code=None,
                error_type="network_error"
            )
    
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        **kwargs
    ) -> str:
        """
        Get chat completion from OpenRouter API with retry and fallback.
        
        Args:
            messages: List of message dicts with 'role' and 'content' keys
            model: Override default model
            temperature: Override default temperature
            max_tokens: Maximum tokens to generate
            **kwargs: Additional parameters for the API request
        
        Returns:
            Generated text content
        
        Raises:
            OpenRouterError: If all retry attempts fail
        """
        request_data = OpenRouterRequest(
            model=model or self.default_model,
            messages=messages,
            temperature=temperature if temperature is not None else self.temperature,
            max_tokens=max_tokens,
            **kwargs
        )
        
        last_error = None
        use_fallback = False
        
        # Try with primary model first, then fallback
        for model_attempt in range(2):
            for attempt in range(self.max_attempts):
                try:
                    logger.info(
                        f"OpenRouter request attempt {attempt + 1}/{self.max_attempts} "
                        f"(model: {request_data.model if not use_fallback else self.fallback_model})"
                    )
                    
                    response = await self._make_request(request_data, use_fallback=use_fallback)
                    
                    if not response.choices:
                        raise OpenRouterError("No choices in response", error_type="empty_response")
                    
                    content = response.choices[0].get("message", {}).get("content", "")
                    if not content.strip():
                        raise OpenRouterError("Empty content in response", error_type="empty_content")
                    
                    logger.info(
                        f"OpenRouter request successful (tokens: input={response.usage.get('prompt_tokens', 0)}, "
                        f"output={response.usage.get('completion_tokens', 0)})"
                    )
                    
                    return content.strip()
                
                except OpenRouterError as e:
                    last_error = e
                    logger.warning(
                        f"OpenRouter attempt {attempt + 1} failed: {e.message} "
                        f"(status: {e.status_code}, type: {e.error_type})"
                    )
                    
                    # Don't retry on certain error types
                    if e.error_type in ["invalid_request", "authentication_error", "authorization_error"]:
                        break
                    
                    # Wait before retry (except on last attempt)
                    if attempt < self.max_attempts - 1:
                        wait_time = self.backoff_intervals[min(attempt, len(self.backoff_intervals) - 1)]
                        logger.info(f"Waiting {wait_time}s before retry...")
                        await asyncio.sleep(wait_time)
            
            # Try fallback model on second iteration
            if model_attempt == 0 and not use_fallback and self.fallback_model != self.default_model:
                use_fallback = True
                logger.info(f"Switching to fallback model: {self.fallback_model}")
                continue
            
            break
        
        # All attempts failed
        error_msg = f"All retry attempts failed. Last error: {last_error.message if last_error else 'Unknown error'}"
        logger.error(error_msg)
        raise OpenRouterError(
            error_msg,
            status_code=last_error.status_code if last_error else None,
            error_type="max_retries_exceeded"
        )
    
    async def format_product_description(
        self,
        content: str,
        prompt_template: str,
        **kwargs
    ) -> str:
        """
        Format product description using OpenRouter API.
        
        Args:
            content: Raw product description content
            prompt_template: Prompt template with {content} placeholder
            **kwargs: Additional parameters for chat completion
        
        Returns:
            Formatted HTML content
        """
        if not content.strip():
            raise OpenRouterError("Empty content provided", error_type="invalid_input")
        
        if "{content}" not in prompt_template:
            raise OpenRouterError("Prompt template must contain {content} placeholder", error_type="invalid_template")
        
        # Build prompt from template
        prompt = prompt_template.format(content=content)
        messages = [
            {"role": "user", "content": prompt}
        ]
        
        logger.debug(f"Sending request with prompt length: {len(prompt)} chars")
        
        return await self.chat_completion(messages, **kwargs)
    
    def get_model_info(self) -> Dict[str, str]:
        """Get current model configuration."""
        return {
            "default_model": self.default_model,
            "fallback_model": self.fallback_model,
            "temperature": str(self.temperature),
            "max_attempts": str(self.max_attempts),
            "circuit_breaker_state": self.circuit_breaker.state
        }