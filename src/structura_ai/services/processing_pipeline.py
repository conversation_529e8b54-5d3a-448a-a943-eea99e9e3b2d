"""Processing pipeline for WooCommerce products with Preview/Run modes."""

import json
import logging
from datetime import datetime, timezone
from enum import Enum
from typing import Dict, Any, List, Optional, Tuple, NamedTuple
from dataclasses import dataclass, asdict

from .content_processor import ContentProcessor, ContentValidationError
from .woocommerce_repository import WooCommerceRepository, WooCommerceProduct
from .ai_service import AIService
from .report_generator import ReportGenerator
from .diff_visualizer import DiffVisualizer, DiffFormat
from ..models import Profile, Site


logger = logging.getLogger(__name__)


class ProcessingMode(Enum):
    """Processing modes."""
    PREVIEW = "preview"
    RUN = "run"


class ProcessingResultStatus(Enum):
    """Processing result status."""
    SUCCESS = "success"
    SKIPPED = "skipped"
    ERROR = "error"
    CACHE_HIT = "cache_hit"
    GUARD_RAIL_REJECTED = "guard_rail_rejected"
    BACKUP_FAILED = "backup_failed"


@dataclass
class ProductProcessingResult:
    """Result of processing a single product."""
    product_id: int
    product_title: str
    status: ProcessingResultStatus
    original_content: str = ""
    processed_content: str = ""
    final_content: str = ""
    processing_metadata: Dict[str, Any] = None
    error_message: str = ""
    cache_hit: bool = False
    skip_reason: str = ""
    backup_created: bool = False
    
    def __post_init__(self):
        if self.processing_metadata is None:
            self.processing_metadata = {}
    
    @property
    def success(self) -> bool:
        """Check if processing was successful."""
        return self.status == ProcessingResultStatus.SUCCESS
    
    @property
    def content_changed(self) -> bool:
        """Check if content was changed."""
        return self.original_content != self.final_content
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        result = asdict(self)
        result['status'] = self.status.value
        return result


@dataclass
class BatchProcessingResult:
    """Result of processing a batch of products."""
    mode: ProcessingMode
    batch_size: int
    total_processed: int
    successful: int
    errors: int
    skipped: int
    cache_hits: int
    guard_rail_rejections: int
    backup_failures: int
    processing_time_seconds: float
    product_results: List[ProductProcessingResult]
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_processed == 0:
            return 0.0
        return self.successful / self.total_processed
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for reporting."""
        result = asdict(self)
        result['mode'] = self.mode.value
        result['product_results'] = [pr.to_dict() for pr in self.product_results]
        return result


class ProcessingPipeline:
    """Main processing pipeline coordinating all components."""
    
    # Metadata keys
    META_PROCESSED = "_ai_processed_v1"
    META_ORIGINAL_BACKUP = "_ai_original_content_v1"
    
    def __init__(self, 
                 site: Site,
                 profile: Profile,
                 ai_service: AIService,
                 content_processor: ContentProcessor,
                 skip_if_processed: bool = True):
        """
        Initialize processing pipeline.
        
        Args:
            site: WooCommerce site configuration
            profile: Processing profile with LLM settings
            ai_service: AI service for content processing
            content_processor: Content processor with guard-rails
            skip_if_processed: Skip products already processed
        """
        self.site = site
        self.profile = profile
        self.ai_service = ai_service
        self.content_processor = content_processor
        self.skip_if_processed = skip_if_processed
        
        # Initialize services
        self.wc_repo = WooCommerceRepository(site)
        self.report_generator = ReportGenerator()
        self.diff_visualizer = DiffVisualizer()
        
        logger.info(f"Initialized processing pipeline for site: {site.name}")
    
    def process_batch(self,
                     mode: ProcessingMode,
                     categories: Optional[List[int]] = None,
                     post_ids: Optional[List[int]] = None,
                     batch_size: int = 10,
                     offset: int = 0) -> BatchProcessingResult:
        """
        Process a batch of products.
        
        Args:
            mode: Processing mode (PREVIEW or RUN)
            categories: Filter by category IDs
            post_ids: Filter by specific post IDs
            batch_size: Number of products per batch
            offset: Starting offset for pagination
            
        Returns:
            Batch processing result with statistics and product results
        """
        start_time = datetime.now()
        
        logger.info(f"Starting {mode.value} batch processing (size={batch_size}, offset={offset})")
        
        # Get products to process
        try:
            products = self.wc_repo.get_products(
                categories=categories,
                post_ids=post_ids,
                batch_size=batch_size,
                offset=offset,
                include_meta=True
            )
        except Exception as e:
            logger.error(f"Failed to fetch products: {e}")
            return BatchProcessingResult(
                mode=mode,
                batch_size=batch_size,
                total_processed=0,
                successful=0,
                errors=1,
                skipped=0,
                cache_hits=0,
                guard_rail_rejections=0,
                backup_failures=0,
                processing_time_seconds=0.0,
                product_results=[]
            )
        
        # Process each product
        product_results = []
        stats = {
            'successful': 0,
            'errors': 0,
            'skipped': 0,
            'cache_hits': 0,
            'guard_rail_rejections': 0,
            'backup_failures': 0
        }
        
        for product in products:
            try:
                result = self._process_single_product(product, mode)
                product_results.append(result)
                
                # Update statistics
                if result.status == ProcessingResultStatus.SUCCESS:
                    stats['successful'] += 1
                elif result.status == ProcessingResultStatus.ERROR:
                    stats['errors'] += 1
                elif result.status == ProcessingResultStatus.SKIPPED:
                    stats['skipped'] += 1
                elif result.status == ProcessingResultStatus.CACHE_HIT:
                    stats['cache_hits'] += 1
                elif result.status == ProcessingResultStatus.GUARD_RAIL_REJECTED:
                    stats['guard_rail_rejections'] += 1
                elif result.status == ProcessingResultStatus.BACKUP_FAILED:
                    stats['backup_failures'] += 1
                
                logger.debug(f"Processed product {product.id}: {result.status.value}")
                
            except Exception as e:
                logger.error(f"Unexpected error processing product {product.id}: {e}")
                error_result = ProductProcessingResult(
                    product_id=product.id,
                    product_title=product.post_title,
                    status=ProcessingResultStatus.ERROR,
                    error_message=str(e),
                    original_content=product.post_content or ""
                )
                product_results.append(error_result)
                stats['errors'] += 1
        
        # Calculate processing time
        end_time = datetime.now()
        processing_time = (end_time - start_time).total_seconds()
        
        # Create batch result
        batch_result = BatchProcessingResult(
            mode=mode,
            batch_size=batch_size,
            total_processed=len(product_results),
            successful=stats['successful'],
            errors=stats['errors'],
            skipped=stats['skipped'],
            cache_hits=stats['cache_hits'],
            guard_rail_rejections=stats['guard_rail_rejections'],
            backup_failures=stats['backup_failures'],
            processing_time_seconds=processing_time,
            product_results=product_results
        )
        
        logger.info(
            f"Batch processing completed: {len(product_results)} products, "
            f"{stats['successful']} successful, {stats['errors']} errors, "
            f"{stats['skipped']} skipped in {processing_time:.2f}s"
        )
        
        return batch_result
    
    def _process_single_product(self, product: WooCommerceProduct, mode: ProcessingMode) -> ProductProcessingResult:
        """
        Process a single product.
        
        Args:
            product: WooCommerce product to process
            mode: Processing mode
            
        Returns:
            Processing result for the product
        """
        logger.debug(f"Processing product {product.id} in {mode.value} mode")
        
        # Check if product has content
        if not product.has_content:
            return ProductProcessingResult(
                product_id=product.id,
                product_title=product.post_title,
                status=ProcessingResultStatus.SKIPPED,
                skip_reason="Product has no content",
                original_content=product.post_content or ""
            )
        
        # Check if already processed (skip logic)
        if self.skip_if_processed:
            skip_result = self._check_should_skip(product)
            if skip_result:
                return skip_result
        
        try:
            # Step 1: Create backup (if running mode)
            backup_created = False
            if mode == ProcessingMode.RUN:
                backup_created = self._create_original_backup(product)
                if not backup_created:
                    return ProductProcessingResult(
                        product_id=product.id,
                        product_title=product.post_title,
                        status=ProcessingResultStatus.BACKUP_FAILED,
                        error_message="Failed to create original content backup",
                        original_content=product.post_content
                    )
            
            # Step 2: Process content with AI
            ai_result = self._process_content_with_ai(product.post_content)
            
            if ai_result['cache_hit']:
                # Cache hit - use cached result
                final_content = ai_result['output_html']
                cache_hit = True
                processing_metadata = {
                    'cache_hit': True,
                    'model_name': ai_result.get('model_name', ''),
                    'cached_at': ai_result.get('cached_at', ''),
                    'similarity_score': ai_result.get('similarity_score', 0.0)
                }
            else:
                # Fresh AI processing
                try:
                    # Apply content processing with sanitization
                    processing_result = self.content_processor.process_with_sanitization(
                        original_content=product.post_content,
                        ai_processed_content=ai_result['output_html'],
                        model_name=ai_result['model_name'],
                        temperature=ai_result['temperature'],
                        prompt_template=ai_result['prompt_template']
                    )
                    
                    if processing_result['processing_errors']:
                        logger.warning(f"Content processing had errors: {processing_result['processing_errors']}")
                    
                    final_content = processing_result['final_content']
                    cache_hit = False
                    processing_metadata = {
                        'cache_hit': False,
                        'model_name': ai_result['model_name'],
                        'temperature': ai_result['temperature'],
                        'guard_rail_validation': processing_result.get('guard_rail_validation', {}),
                        'html_sanitization': processing_result.get('html_sanitization', {}),
                        'processing_errors': processing_result['processing_errors']
                    }
                    
                except ContentValidationError as e:
                    # Guard-rail rejected the content
                    return ProductProcessingResult(
                        product_id=product.id,
                        product_title=product.post_title,
                        status=ProcessingResultStatus.GUARD_RAIL_REJECTED,
                        error_message=str(e),
                        original_content=product.post_content,
                        processed_content=ai_result['output_html'],
                        processing_metadata={'validation_error': str(e)}
                    )
            
            # Step 3: Update database if in RUN mode
            if mode == ProcessingMode.RUN:
                success = self._update_product_content(product, final_content, processing_metadata)
                if not success:
                    return ProductProcessingResult(
                        product_id=product.id,
                        product_title=product.post_title,
                        status=ProcessingResultStatus.ERROR,
                        error_message="Failed to update product content in database",
                        original_content=product.post_content,
                        processed_content=final_content,
                        backup_created=backup_created
                    )
            
            # Success result
            status = ProcessingResultStatus.CACHE_HIT if cache_hit else ProcessingResultStatus.SUCCESS
            
            return ProductProcessingResult(
                product_id=product.id,
                product_title=product.post_title,
                status=status,
                original_content=product.post_content,
                processed_content=ai_result['output_html'],
                final_content=final_content,
                processing_metadata=processing_metadata,
                cache_hit=cache_hit,
                backup_created=backup_created
            )
            
        except Exception as e:
            logger.error(f"Error processing product {product.id}: {e}")
            return ProductProcessingResult(
                product_id=product.id,
                product_title=product.post_title,
                status=ProcessingResultStatus.ERROR,
                error_message=str(e),
                original_content=product.post_content or ""
            )
    
    def _check_should_skip(self, product: WooCommerceProduct) -> Optional[ProductProcessingResult]:
        """
        Check if product should be skipped based on processing metadata.
        
        Args:
            product: Product to check
            
        Returns:
            Skip result if should be skipped, None otherwise
        """
        processed_meta = product.get_meta(self.META_PROCESSED)
        if not processed_meta:
            return None
        
        try:
            processed_data = json.loads(processed_meta)
            
            # Generate current input hash
            current_input_hash = self.content_processor.generate_content_hash(product.post_content)
            stored_input_hash = processed_data.get('input_hash', '')
            
            # Compare hashes - skip if they match
            if current_input_hash == stored_input_hash:
                logger.debug(f"Skipping product {product.id} - already processed with same content")
                return ProductProcessingResult(
                    product_id=product.id,
                    product_title=product.post_title,
                    status=ProcessingResultStatus.SKIPPED,
                    skip_reason="Already processed with same content (input_hash match)",
                    original_content=product.post_content,
                    processing_metadata={
                        'stored_hash': stored_input_hash,
                        'current_hash': current_input_hash,
                        'processed_at': processed_data.get('processed_at', ''),
                        'version': processed_data.get('version', '')
                    }
                )
            
            logger.debug(f"Content changed for product {product.id} - will reprocess")
            return None
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.warning(f"Invalid processing metadata for product {product.id}: {e}")
            return None
    
    def _create_original_backup(self, product: WooCommerceProduct) -> bool:
        """
        Create write-once backup of original content.
        
        Args:
            product: Product to backup
            
        Returns:
            True if backup was created or already exists, False on failure
        """
        # Check if backup already exists
        existing_backup = product.get_meta(self.META_ORIGINAL_BACKUP)
        if existing_backup:
            logger.debug(f"Original backup already exists for product {product.id}")
            return True
        
        # Create backup metadata
        backup_data = {
            'original_content': product.post_content,
            'backed_up_at': datetime.now(timezone.utc).isoformat(),
            'version': 1,
            'content_hash': self.content_processor.generate_content_hash(product.post_content)
        }
        
        backup_json = json.dumps(backup_data, ensure_ascii=False)
        
        try:
            # Use write_once=True to ensure it's only written once
            success = self.wc_repo.set_product_meta(
                product.id, 
                self.META_ORIGINAL_BACKUP, 
                backup_json,
                write_once=True
            )
            
            if success:
                logger.info(f"Created original content backup for product {product.id}")
                return True
            else:
                logger.debug(f"Original backup already exists for product {product.id} (write-once)")
                # For write-once protection, this is still considered success
                return True
            
        except Exception as e:
            logger.error(f"Failed to create backup for product {product.id}: {e}")
            return False
    
    def _process_content_with_ai(self, content: str) -> Dict[str, Any]:
        """
        Process content with AI service.
        
        Args:
            content: Content to process
            
        Returns:
            AI processing result
        """
        return self.ai_service.format_product_content(
            content=content,
            profile=self.profile
        )
    
    def _update_product_content(self, 
                               product: WooCommerceProduct, 
                               new_content: str,
                               processing_metadata: Dict[str, Any]) -> bool:
        """
        Update product content and processing metadata in database.
        
        Args:
            product: Product to update
            new_content: New content to set
            processing_metadata: Processing metadata to store
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Update post content
            success = self.wc_repo.update_product_content(
                product.id, 
                new_content,
                expected_modified=product.post_modified
            )
            
            if not success:
                logger.error(f"Failed to update content for product {product.id}")
                return False
            
            # Update processing metadata
            processed_data = {
                'input_hash': self.content_processor.generate_content_hash(product.post_content),
                'output_hash': self.content_processor.generate_content_hash(new_content),
                'processed_at': datetime.now(timezone.utc).isoformat(),
                'version': 1,
                'model_name': processing_metadata.get('model_name', ''),
                'cache_hit': processing_metadata.get('cache_hit', False),
                'guard_rail_metrics': processing_metadata.get('guard_rail_validation', {}),
                'html_sanitization': processing_metadata.get('html_sanitization', {})
            }
            
            processed_json = json.dumps(processed_data, ensure_ascii=False)
            
            meta_success = self.wc_repo.set_product_meta(
                product.id,
                self.META_PROCESSED,
                processed_json
            )
            
            if not meta_success:
                logger.warning(f"Failed to update processing metadata for product {product.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to update product {product.id}: {e}")
            return False
    
    def generate_diff_report(self, 
                           result: ProductProcessingResult, 
                           format: DiffFormat = DiffFormat.UNIFIED,
                           enable_colors: bool = True) -> str:
        """
        Generate diff report for a product processing result.
        
        Args:
            result: Product processing result
            format: Diff format
            enable_colors: Enable terminal colors
            
        Returns:
            Formatted diff string
        """
        if not result.content_changed:
            return "No content changes detected."
        
        diff_result = self.diff_visualizer.generate_diff(
            result.original_content,
            result.final_content,
            original_title=f"Original (Product {result.product_id})",
            processed_title=f"Processed (Product {result.product_id})"
        )
        
        return self.diff_visualizer.format_diff(diff_result, format, enable_colors)
    
    def generate_batch_report(self, 
                            batch_result: BatchProcessingResult,
                            include_diffs: bool = True,
                            report_format: str = "json") -> str:
        """
        Generate comprehensive batch processing report.
        
        Args:
            batch_result: Batch processing result
            include_diffs: Include diff visualizations
            report_format: Report format (json, csv, text)
            
        Returns:
            Formatted report string
        """
        if report_format.lower() == "json":
            report_data = batch_result.to_dict()
            if include_diffs:
                for i, product_result in enumerate(batch_result.product_results):
                    if product_result.content_changed:
                        diff = self.generate_diff_report(product_result, enable_colors=False)
                        report_data['product_results'][i]['diff'] = diff
            
            return json.dumps(report_data, indent=2, ensure_ascii=False)
        
        elif report_format.lower() == "csv":
            return self.report_generator.generate_csv_report([result.to_dict() for result in batch_result.product_results])
        
        else:  # text format
            return self._generate_text_report(batch_result, include_diffs)
    
    def _generate_text_report(self, batch_result: BatchProcessingResult, include_diffs: bool) -> str:
        """Generate human-readable text report."""
        lines = []
        lines.append(f"Processing Report - {batch_result.mode.value.upper()} Mode")
        lines.append("=" * 50)
        lines.append(f"Total Processed: {batch_result.total_processed}")
        lines.append(f"Successful: {batch_result.successful}")
        lines.append(f"Errors: {batch_result.errors}")
        lines.append(f"Skipped: {batch_result.skipped}")
        lines.append(f"Cache Hits: {batch_result.cache_hits}")
        lines.append(f"Guard-rail Rejections: {batch_result.guard_rail_rejections}")
        lines.append(f"Success Rate: {batch_result.success_rate:.1%}")
        lines.append(f"Processing Time: {batch_result.processing_time_seconds:.2f}s")
        lines.append("")
        
        if include_diffs:
            lines.append("Product Details:")
            lines.append("-" * 30)
            for result in batch_result.product_results:
                lines.append(f"\nProduct {result.product_id}: {result.product_title}")
                lines.append(f"Status: {result.status.value}")
                if result.error_message:
                    lines.append(f"Error: {result.error_message}")
                if result.skip_reason:
                    lines.append(f"Skip Reason: {result.skip_reason}")
                if result.content_changed and include_diffs:
                    lines.append("Content Changes:")
                    lines.append(self.generate_diff_report(result, enable_colors=False))
        
        return "\n".join(lines)