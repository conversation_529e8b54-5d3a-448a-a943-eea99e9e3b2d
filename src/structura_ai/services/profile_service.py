"""Service for managing formatting profiles."""

import json
import logging
from typing import Dict, Any, List, Optional

from ..database import get_session
from ..database.repositories import ProfileRepository
from ..models import Profile
from ..config import get_settings

logger = logging.getLogger(__name__)


class ProfileService:
    """Service for profile management operations."""
    
    def __init__(self):
        """Initialize profile service."""
        self.settings = get_settings()
        self.session_manager = get_session()
    
    def create_default_profile(self) -> Profile:
        """Create a default formatting profile."""
        default_profile_data = {
            'name': 'default',
            'display_name': 'Profilo Default',
            'description': 'Profilo di formattazione predefinito per prodotti WooCommerce',
            'llm_model': self.settings.openrouter_default_model,
            'llm_fallback_model': self.settings.openrouter_fallback_model,
            'llm_temperature': self.settings.openrouter_temperature,
            'llm_prompt': self._get_default_prompt(),
            'batch_size': self.settings.default_batch_size,
            'similarity_threshold': self.settings.similarity_threshold,
            'length_diff_threshold': self.settings.length_diff_threshold,
            'skip_if_processed': True,
            'retry_max_attempts': self.settings.retry_max_attempts,
            'retry_backoff_base': 2.0,
            'allowed_tags': self._get_default_allowed_tags(),
            'allowed_attributes': self._get_default_allowed_attributes(),
            'processed_meta_key': '_ai_processed_v1',
            'backup_meta_key': '_ai_original_content_v1',
            'is_active': True,
            'version': 1
        }
        
        with self.session_manager.session_scope() as session:
            repo = ProfileRepository(session)
            profile = repo.create(default_profile_data)
            logger.info(f"Created default profile with ID {profile.id}")
            return profile
    
    def get_or_create_default_profile(self) -> Profile:
        """Get default profile or create it if it doesn't exist."""
        with self.session_manager.session_scope() as session:
            repo = ProfileRepository(session)
            
            # Try to get existing default profile
            profile = repo.get_by_name('default')
            if profile:
                return profile
            
            # Create default profile if it doesn't exist in the same session
            logger.info("Default profile not found, creating new one")
            
            default_profile_data = {
                'name': 'default',
                'display_name': 'Profilo Default',
                'description': 'Profilo di formattazione predefinito per prodotti WooCommerce',
                'llm_model': self.settings.openrouter_default_model,
                'llm_fallback_model': self.settings.openrouter_fallback_model,
                'llm_temperature': self.settings.openrouter_temperature,
                'llm_prompt': self._get_default_prompt(),
                'batch_size': self.settings.default_batch_size,
                'similarity_threshold': self.settings.similarity_threshold,
                'length_diff_threshold': self.settings.length_diff_threshold,
                'skip_if_processed': True,
                'retry_max_attempts': self.settings.retry_max_attempts,
                'retry_backoff_base': 2.0,
                'allowed_tags': self._get_default_allowed_tags(),
                'allowed_attributes': self._get_default_allowed_attributes(),
                'processed_meta_key': '_ai_processed_v1',
                'backup_meta_key': '_ai_original_content_v1',
                'is_active': True,
                'version': 1
            }
            
            profile = repo.create(default_profile_data)
            logger.info(f"Created default profile with ID {profile.id}")
            return profile
    
    def _get_default_prompt(self) -> str:
        """Get default LLM prompt for HTML formatting."""
        return """You are an HTML formatter for WooCommerce product descriptions. Your task is to add proper HTML markup to improve readability while preserving the original text content exactly.

CRITICAL RULES:
1. DO NOT change, modify, or rephrase any text content
2. DO NOT add or remove any words, sentences, or information
3. ONLY add HTML tags for structure and formatting
4. Preserve all original spacing, line breaks, and formatting intentions

ALLOWED HTML TAGS:
- Structure: <section>, <h2>, <h3>, <p>
- Lists: <ul>, <ol>, <li>
- Text formatting: <strong>, <em>
- Links: <a> (with href and title attributes only)
- Tables: <table>, <thead>, <tbody>, <tr>, <th>, <td>
- Code: <code>
- Line breaks: <br>

FORMATTING GUIDELINES:
- Use <h2> for main sections/headings
- Use <h3> for subsections
- Use <p> for paragraphs
- Use <ul>/<ol> and <li> for lists
- Use <strong> for emphasis/bold text
- Use <em> for italic text
- Use <table> structure for tabular data
- Keep the HTML clean and well-structured

Example:
Input: "Product Features\nDurable construction\nEasy to clean\nAvailable in multiple colors"
Output: "<h2>Product Features</h2><ul><li>Durable construction</li><li>Easy to clean</li><li>Available in multiple colors</li></ul>"

Format the following product description:"""
    
    def _get_default_allowed_tags(self) -> List[str]:
        """Get default allowed HTML tags."""
        return [
            'section', 'h2', 'h3', 'p', 'ul', 'ol', 'li', 
            'strong', 'em', 'a', 'table', 'thead', 'tbody', 
            'tr', 'th', 'td', 'code', 'br'
        ]
    
    def _get_default_allowed_attributes(self) -> Dict[str, List[str]]:
        """Get default allowed HTML attributes."""
        return {
            'a': ['href', 'title', 'target', 'rel'],
            'table': ['class'],
            'th': ['scope'],
            'td': ['colspan', 'rowspan']
        }
    
    def export_profile(self, profile_name: str) -> Dict[str, Any]:
        """Export profile to dictionary for JSON export."""
        with self.session_manager.session_scope() as session:
            repo = ProfileRepository(session)
            profile = repo.get_by_name(profile_name)
            
            if not profile:
                raise ValueError(f"Profile '{profile_name}' not found")
            
            return profile.to_dict()
    
    def import_profile(self, profile_data: Dict[str, Any], overwrite: bool = False) -> Profile:
        """Import profile from dictionary."""
        with self.session_manager.session_scope() as session:
            repo = ProfileRepository(session)
            
            # Check if profile already exists
            existing = repo.get_by_name(profile_data['name'])
            if existing and not overwrite:
                raise ValueError(f"Profile '{profile_data['name']}' already exists")
            
            if existing and overwrite:
                # Update existing profile
                updated_profile = repo.update(existing.id, profile_data)
                logger.info(f"Updated existing profile '{profile_data['name']}'")
                return updated_profile
            else:
                # Create new profile
                new_profile = repo.create(profile_data)
                logger.info(f"Imported new profile '{profile_data['name']}'")
                return new_profile
    
    def validate_profile(self, profile_data: Dict[str, Any]) -> List[str]:
        """Validate profile data and return list of errors."""
        errors = []
        
        # Required fields
        required_fields = [
            'name', 'display_name', 'llm_model', 'llm_prompt',
            'allowed_tags', 'allowed_attributes'
        ]
        
        for field in required_fields:
            if field not in profile_data:
                errors.append(f"Missing required field: {field}")
        
        # Validate numeric fields
        if 'similarity_threshold' in profile_data:
            threshold = profile_data['similarity_threshold']
            if not (0 <= threshold <= 1):
                errors.append("similarity_threshold must be between 0 and 1")
        
        if 'batch_size' in profile_data:
            batch_size = profile_data['batch_size']
            if not (1 <= batch_size <= 100):
                errors.append("batch_size must be between 1 and 100")
        
        if 'retry_max_attempts' in profile_data:
            attempts = profile_data['retry_max_attempts']
            if not (1 <= attempts <= 10):
                errors.append("retry_max_attempts must be between 1 and 10")
        
        # Validate allowed_tags is a list
        if 'allowed_tags' in profile_data:
            if not isinstance(profile_data['allowed_tags'], list):
                errors.append("allowed_tags must be a list")
        
        # Validate allowed_attributes is a dict
        if 'allowed_attributes' in profile_data:
            if not isinstance(profile_data['allowed_attributes'], dict):
                errors.append("allowed_attributes must be a dictionary")
        
        return errors


# Global service instance
_profile_service: Optional[ProfileService] = None


def get_profile_service() -> ProfileService:
    """Get global profile service instance."""
    global _profile_service
    if _profile_service is None:
        _profile_service = ProfileService()
    return _profile_service