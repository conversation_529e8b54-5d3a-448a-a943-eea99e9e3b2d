"""
Report generation service for WooCommerce product processing results.

This module provides structured reporting in JSON and CSV formats as required by Phase 4 
specifications, including job statistics, processing results, and detailed metrics.
"""

import csv
import json
import logging
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)


class ReportFormat(Enum):
    """Supported report output formats."""
    JSON = "json"
    CSV = "csv"
    BOTH = "both"


class ProcessingStatus(Enum):
    """Product processing status values."""
    SUCCESS = "success"
    ERROR = "error"
    SKIPPED = "skipped"
    CACHE_HIT = "cache_hit"
    VALIDATION_FAILED = "validation_failed"


@dataclass
class ProductProcessingResult:
    """Result of processing a single product."""
    
    # Product identification
    product_id: int
    product_title: str
    product_slug: str
    
    # Processing status
    status: ProcessingStatus
    timestamp: datetime
    
    # Content metrics
    original_length: int
    processed_length: int
    similarity_score: Optional[float] = None
    length_difference: int = 0
    
    # Processing details
    processing_time_ms: float = 0.0
    cache_hit: bool = False
    validation_passed: bool = True
    
    # Error information
    error_message: Optional[str] = None
    error_type: Optional[str] = None
    
    # HTML processing details
    html_tags_removed: int = 0
    html_attributes_removed: int = 0
    html_validation_errors: List[str] = None
    
    # AI model information
    model_name: Optional[str] = None
    prompt_hash: Optional[str] = None
    
    def __post_init__(self):
        """Initialize default values after creation."""
        if self.html_validation_errors is None:
            self.html_validation_errors = []
        
        # Calculate derived metrics
        if self.original_length > 0 and self.processed_length > 0:
            self.length_difference = abs(self.processed_length - self.original_length)


@dataclass  
class JobStatistics:
    """Statistics for a complete job run."""
    
    # Job identification
    job_id: Optional[str]
    site_name: str
    profile_name: str
    started_at: datetime
    completed_at: Optional[datetime] = None
    
    # Processing counts
    total_products: int = 0
    successful_count: int = 0
    error_count: int = 0
    skipped_count: int = 0
    cache_hit_count: int = 0
    validation_failed_count: int = 0
    
    # Performance metrics
    total_processing_time_ms: float = 0.0
    average_processing_time_ms: float = 0.0
    
    # Content metrics
    total_original_length: int = 0
    total_processed_length: int = 0
    average_similarity_score: float = 0.0
    
    # AI usage metrics
    llm_calls_made: int = 0
    llm_calls_cached: int = 0
    cache_hit_rate: float = 0.0
    
    # Error summary
    error_types: Dict[str, int] = None
    
    def __post_init__(self):
        """Initialize default values and calculate derived metrics."""
        if self.error_types is None:
            self.error_types = {}
        
        # Calculate completion time if not set
        if self.completed_at is None:
            self.completed_at = datetime.utcnow()
        
        # Calculate derived metrics
        if self.total_products > 0:
            self.average_processing_time_ms = self.total_processing_time_ms / self.total_products
            self.cache_hit_rate = (self.cache_hit_count / self.total_products) * 100
        
        # Calculate success rate
        if self.total_products > 0:
            self.success_rate = (self.successful_count / self.total_products) * 100
        else:
            self.success_rate = 0.0


class ReportGenerator:
    """
    Generate structured reports for product processing results.
    
    Supports JSON and CSV output formats with comprehensive statistics
    and detailed processing results.
    """
    
    def __init__(self, output_dir: Optional[Path] = None):
        """
        Initialize report generator.
        
        Args:
            output_dir: Directory for report output (default: ./reports)
        """
        self.output_dir = Path(output_dir) if output_dir else Path("./reports")
        self.output_dir.mkdir(exist_ok=True)
    
    def generate_job_report(
        self,
        job_stats: JobStatistics,
        product_results: List[ProductProcessingResult],
        report_format: ReportFormat = ReportFormat.JSON,
        output_file: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Generate complete job report with statistics and product details.
        
        Args:
            job_stats: Job-level statistics
            product_results: List of individual product results
            report_format: Output format (JSON, CSV, or BOTH)
            output_file: Custom output filename (without extension)
            
        Returns:
            Dictionary with generated file paths
        """
        # Generate timestamp-based filename if not provided
        if not output_file:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"job_report_{timestamp}"
        
        generated_files = {}
        
        try:
            if report_format in (ReportFormat.JSON, ReportFormat.BOTH):
                json_file = self._generate_json_report(job_stats, product_results, output_file)
                generated_files["json"] = str(json_file)
            
            if report_format in (ReportFormat.CSV, ReportFormat.BOTH):
                csv_file = self._generate_csv_report(job_stats, product_results, output_file)
                generated_files["csv"] = str(csv_file)
            
            logger.info(f"Generated report files: {list(generated_files.values())}")
            return generated_files
        
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise
    
    def _generate_json_report(
        self,
        job_stats: JobStatistics,
        product_results: List[ProductProcessingResult],
        output_file: str
    ) -> Path:
        """Generate JSON format report."""
        json_file = self.output_dir / f"{output_file}.json"
        
        # Prepare data for JSON serialization
        report_data = {
            "report_metadata": {
                "generated_at": datetime.utcnow().isoformat(),
                "report_version": "1.0",
                "total_products": len(product_results)
            },
            "job_statistics": self._serialize_for_json(asdict(job_stats)),
            "product_results": [
                self._serialize_for_json(asdict(result)) 
                for result in product_results
            ],
            "summary": {
                "success_rate": job_stats.success_rate,
                "cache_efficiency": job_stats.cache_hit_rate,
                "average_processing_time": job_stats.average_processing_time_ms,
                "error_breakdown": job_stats.error_types
            }
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        logger.debug(f"JSON report generated: {json_file}")
        return json_file
    
    def _generate_csv_report(
        self,
        job_stats: JobStatistics,
        product_results: List[ProductProcessingResult],
        output_file: str
    ) -> Path:
        """Generate CSV format report."""
        csv_file = self.output_dir / f"{output_file}.csv"
        
        # Define CSV headers
        headers = [
            'product_id', 'product_title', 'product_slug', 'status', 'timestamp',
            'original_length', 'processed_length', 'length_difference',
            'similarity_score', 'processing_time_ms', 'cache_hit',
            'validation_passed', 'error_message', 'error_type',
            'html_tags_removed', 'html_attributes_removed',
            'html_validation_errors', 'model_name'
        ]
        
        with open(csv_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=headers)
            writer.writeheader()
            
            for result in product_results:
                row = {
                    'product_id': result.product_id,
                    'product_title': result.product_title,
                    'product_slug': result.product_slug,
                    'status': result.status.value,
                    'timestamp': result.timestamp.isoformat(),
                    'original_length': result.original_length,
                    'processed_length': result.processed_length,
                    'length_difference': result.length_difference,
                    'similarity_score': result.similarity_score,
                    'processing_time_ms': result.processing_time_ms,
                    'cache_hit': result.cache_hit,
                    'validation_passed': result.validation_passed,
                    'error_message': result.error_message,
                    'error_type': result.error_type,
                    'html_tags_removed': result.html_tags_removed,
                    'html_attributes_removed': result.html_attributes_removed,
                    'html_validation_errors': '; '.join(result.html_validation_errors) if result.html_validation_errors else '',
                    'model_name': result.model_name
                }
                writer.writerow(row)
        
        # Add summary statistics at the end
        with open(csv_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([])  # Empty line
            writer.writerow(['# JOB STATISTICS SUMMARY'])
            writer.writerow(['Job ID', job_stats.job_id])
            writer.writerow(['Site', job_stats.site_name])
            writer.writerow(['Profile', job_stats.profile_name])
            writer.writerow(['Started At', job_stats.started_at.isoformat()])
            writer.writerow(['Completed At', job_stats.completed_at.isoformat() if job_stats.completed_at else 'N/A'])
            writer.writerow(['Total Products', job_stats.total_products])
            writer.writerow(['Successful', job_stats.successful_count])
            writer.writerow(['Errors', job_stats.error_count])
            writer.writerow(['Skipped', job_stats.skipped_count])
            writer.writerow(['Cache Hits', job_stats.cache_hit_count])
            writer.writerow(['Success Rate %', f"{job_stats.success_rate:.2f}"])
            writer.writerow(['Cache Hit Rate %', f"{job_stats.cache_hit_rate:.2f}"])
            writer.writerow(['Avg Processing Time (ms)', f"{job_stats.average_processing_time_ms:.2f}"])
        
        logger.debug(f"CSV report generated: {csv_file}")
        return csv_file
    
    def _serialize_for_json(self, obj: Any) -> Any:
        """Convert objects to JSON-serializable format."""
        if isinstance(obj, dict):
            return {k: self._serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, ProcessingStatus):
            return obj.value
        elif hasattr(obj, '__dict__'):
            return self._serialize_for_json(asdict(obj))
        else:
            return obj
    
    def generate_summary_stats(
        self,
        product_results: List[ProductProcessingResult]
    ) -> JobStatistics:
        """
        Generate job statistics from product processing results.
        
        Args:
            product_results: List of product processing results
            
        Returns:
            JobStatistics object with calculated metrics
        """
        if not product_results:
            return JobStatistics(
                job_id=None,
                site_name="unknown",
                profile_name="unknown",
                started_at=datetime.utcnow()
            )
        
        # Calculate counts
        total_products = len(product_results)
        successful_count = sum(1 for r in product_results if r.status == ProcessingStatus.SUCCESS)
        error_count = sum(1 for r in product_results if r.status == ProcessingStatus.ERROR)
        skipped_count = sum(1 for r in product_results if r.status == ProcessingStatus.SKIPPED)
        cache_hit_count = sum(1 for r in product_results if r.cache_hit)
        validation_failed_count = sum(1 for r in product_results if not r.validation_passed)
        
        # Calculate time metrics
        total_time = sum(r.processing_time_ms for r in product_results)
        
        # Calculate content metrics
        total_original = sum(r.original_length for r in product_results)
        total_processed = sum(r.processed_length for r in product_results)
        
        # Calculate average similarity (only for successful results)
        successful_results = [r for r in product_results if r.status == ProcessingStatus.SUCCESS and r.similarity_score is not None]
        avg_similarity = sum(r.similarity_score for r in successful_results) / len(successful_results) if successful_results else 0.0
        
        # Count error types
        error_types = {}
        for result in product_results:
            if result.error_type:
                error_types[result.error_type] = error_types.get(result.error_type, 0) + 1
        
        # Get job timing (use earliest and latest timestamps)
        timestamps = [r.timestamp for r in product_results]
        started_at = min(timestamps) if timestamps else datetime.utcnow()
        completed_at = max(timestamps) if timestamps else datetime.utcnow()
        
        return JobStatistics(
            job_id=None,  # Will be set by calling code
            site_name="unknown",  # Will be set by calling code
            profile_name="unknown",  # Will be set by calling code
            started_at=started_at,
            completed_at=completed_at,
            total_products=total_products,
            successful_count=successful_count,
            error_count=error_count,
            skipped_count=skipped_count,
            cache_hit_count=cache_hit_count,
            validation_failed_count=validation_failed_count,
            total_processing_time_ms=total_time,
            total_original_length=total_original,
            total_processed_length=total_processed,
            average_similarity_score=avg_similarity,
            llm_calls_made=total_products - cache_hit_count,
            llm_calls_cached=cache_hit_count,
            error_types=error_types
        )
    
    def load_report_from_json(self, json_file: Path) -> Dict[str, Any]:
        """
        Load previously generated JSON report.
        
        Args:
            json_file: Path to JSON report file
            
        Returns:
            Report data dictionary
        """
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.debug(f"Loaded JSON report: {json_file}")
            return data
        
        except Exception as e:
            logger.error(f"Failed to load JSON report {json_file}: {e}")
            raise
    
    def compare_reports(
        self,
        report1_file: Path,
        report2_file: Path
    ) -> Dict[str, Any]:
        """
        Compare two job reports and generate comparison metrics.
        
        Args:
            report1_file: First report file
            report2_file: Second report file
            
        Returns:
            Comparison metrics dictionary
        """
        try:
            report1 = self.load_report_from_json(report1_file)
            report2 = self.load_report_from_json(report2_file)
            
            stats1 = report1["job_statistics"]
            stats2 = report2["job_statistics"]
            
            comparison = {
                "report_files": {
                    "baseline": str(report1_file),
                    "comparison": str(report2_file)
                },
                "metrics_comparison": {
                    "total_products": {
                        "baseline": stats1["total_products"],
                        "comparison": stats2["total_products"],
                        "difference": stats2["total_products"] - stats1["total_products"]
                    },
                    "success_rate": {
                        "baseline": stats1["success_rate"],
                        "comparison": stats2["success_rate"],
                        "difference": stats2["success_rate"] - stats1["success_rate"]
                    },
                    "cache_hit_rate": {
                        "baseline": stats1["cache_hit_rate"],
                        "comparison": stats2["cache_hit_rate"],
                        "difference": stats2["cache_hit_rate"] - stats1["cache_hit_rate"]
                    },
                    "avg_processing_time": {
                        "baseline": stats1["average_processing_time_ms"],
                        "comparison": stats2["average_processing_time_ms"],
                        "difference": stats2["average_processing_time_ms"] - stats1["average_processing_time_ms"]
                    }
                },
                "generated_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Report comparison completed: {report1_file.name} vs {report2_file.name}")
            return comparison
        
        except Exception as e:
            logger.error(f"Report comparison failed: {e}")
            raise