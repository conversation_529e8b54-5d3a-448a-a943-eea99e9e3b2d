"""Repository for WooCommerce products operations."""

import logging
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json

from sqlalchemy import text
from sqlalchemy.orm import Session

from ..models import Site
from .mysql_connection import get_woocommerce_connection, MySQLConnectionError

logger = logging.getLogger(__name__)


class WooCommerceProduct:
    """Data class for WooCommerce product."""
    
    def __init__(self, data: Dict[str, Any]):
        """Initialize product from database row."""
        self.id = data['ID']
        self.post_title = data['post_title']
        self.post_content = data['post_content']
        self.post_status = data['post_status']
        self.post_modified = data['post_modified']
        self.post_modified_gmt = data['post_modified_gmt']
        
        # Metadata
        self.meta = {}
        if 'meta_data' in data:
            for meta in data['meta_data']:
                self.meta[meta['meta_key']] = meta['meta_value']
    
    def __repr__(self) -> str:
        return f"<WooCommerceProduct(id={self.id}, title='{self.post_title[:50]}...')>"
    
    @property
    def content_length(self) -> int:
        """Get content length."""
        return len(self.post_content) if self.post_content else 0
    
    @property
    def has_content(self) -> bool:
        """Check if product has content."""
        return bool(self.post_content and self.post_content.strip())
    
    def get_meta(self, key: str, default: Any = None) -> Any:
        """Get metadata value."""
        return self.meta.get(key, default)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'id': self.id,
            'title': self.post_title,
            'content': self.post_content,
            'status': self.post_status,
            'modified': self.post_modified,
            'modified_gmt': self.post_modified_gmt,
            'content_length': self.content_length,
            'has_content': self.has_content,
            'meta': self.meta
        }


class WooCommerceRepository:
    """Repository for WooCommerce database operations."""
    
    def __init__(self, site: Site):
        """Initialize repository for specific site."""
        self.site = site
        self.connection = get_woocommerce_connection(site)
    
    def get_products(self, 
                    categories: Optional[List[int]] = None,
                    post_ids: Optional[List[int]] = None,
                    batch_size: int = 10,
                    offset: int = 0,
                    include_meta: bool = True) -> List[WooCommerceProduct]:
        """Get products with optional filters."""
        
        # Build base query
        query_parts = [
            f"SELECT p.ID, p.post_title, p.post_content, p.post_status,",
            f"       p.post_modified, p.post_modified_gmt",
            f"FROM {self.site.db_table_prefix}posts p",
            f"WHERE p.post_type = 'product'",
            f"  AND p.post_status IN ('publish', 'draft')"
        ]
        
        params = {}
        
        # Add category filter
        if categories:
            query_parts.extend([
                f"  AND p.ID IN (",
                f"    SELECT tr.object_id",
                f"    FROM {self.site.db_table_prefix}term_relationships tr",
                f"    JOIN {self.site.db_table_prefix}term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id",
                f"    WHERE tt.taxonomy = 'product_cat'",
                f"      AND tt.term_id IN :categories",
                f"  )"
            ])
            params['categories'] = tuple(categories)
        
        # Add post IDs filter
        if post_ids:
            if categories:
                # Intersection: must match both category AND post_ids
                query_parts.append("  AND p.ID IN :post_ids")
            else:
                # Only post IDs filter
                query_parts.append("  AND p.ID IN :post_ids")
            params['post_ids'] = tuple(post_ids)
        
        # Add ordering and pagination
        query_parts.extend([
            f"ORDER BY p.ID ASC",
            f"LIMIT :batch_size OFFSET :offset"
        ])
        params.update({
            'batch_size': batch_size,
            'offset': offset
        })
        
        query = "\n".join(query_parts)
        
        try:
            with self.connection.get_session() as session:
                result = session.execute(text(query), params)
                rows = result.fetchall()
                
                products = []
                for row in rows:
                    product_data = dict(row._mapping)
                    
                    # Load metadata if requested
                    if include_meta:
                        product_data['meta_data'] = self._get_product_meta(session, row.ID)
                    
                    products.append(WooCommerceProduct(product_data))
                
                logger.info(f"Retrieved {len(products)} products from site {self.site.name}")
                return products
                
        except Exception as e:
            logger.error(f"Failed to get products from site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Failed to retrieve products: {e}") from e
    
    def get_product_by_id(self, post_id: int, include_meta: bool = True) -> Optional[WooCommerceProduct]:
        """Get single product by ID."""
        products = self.get_products(post_ids=[post_id], batch_size=1, include_meta=include_meta)
        return products[0] if products else None
    
    def count_products(self, 
                      categories: Optional[List[int]] = None,
                      post_ids: Optional[List[int]] = None) -> int:
        """Count products matching filters."""
        
        query_parts = [
            f"SELECT COUNT(*) as count",
            f"FROM {self.site.db_table_prefix}posts p",
            f"WHERE p.post_type = 'product'",
            f"  AND p.post_status IN ('publish', 'draft')"
        ]
        
        params = {}
        
        # Add category filter
        if categories:
            query_parts.extend([
                f"  AND p.ID IN (",
                f"    SELECT tr.object_id",
                f"    FROM {self.site.db_table_prefix}term_relationships tr",
                f"    JOIN {self.site.db_table_prefix}term_taxonomy tt ON tr.term_taxonomy_id = tt.term_taxonomy_id",
                f"    WHERE tt.taxonomy = 'product_cat'",
                f"      AND tt.term_id IN :categories",
                f"  )"
            ])
            params['categories'] = tuple(categories)
        
        # Add post IDs filter
        if post_ids:
            query_parts.append("  AND p.ID IN :post_ids")
            params['post_ids'] = tuple(post_ids)
        
        query = "\n".join(query_parts)
        
        try:
            result = self.connection.execute_with_retry(query, params)
            count = result[0][0] if result else 0
            logger.info(f"Found {count} products matching filters in site {self.site.name}")
            return count
            
        except Exception as e:
            logger.error(f"Failed to count products from site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Failed to count products: {e}") from e
    
    def update_product_content(self, 
                              post_id: int, 
                              new_content: str,
                              expected_modified: Optional[datetime] = None) -> bool:
        """Update product content with optimistic concurrency control."""
        
        current_time = datetime.utcnow()
        current_time_gmt = current_time  # Assuming UTC for simplicity
        
        # Build update query with optional concurrency check
        if expected_modified:
            query = f"""
                UPDATE {self.site.db_table_prefix}posts 
                SET post_content = :new_content,
                    post_modified = :current_time,
                    post_modified_gmt = :current_time_gmt
                WHERE ID = :post_id 
                  AND post_modified = :expected_modified
            """
            params = {
                'new_content': new_content,
                'current_time': current_time,
                'current_time_gmt': current_time_gmt,
                'post_id': post_id,
                'expected_modified': expected_modified
            }
        else:
            query = f"""
                UPDATE {self.site.db_table_prefix}posts 
                SET post_content = :new_content,
                    post_modified = :current_time,
                    post_modified_gmt = :current_time_gmt
                WHERE ID = :post_id
            """
            params = {
                'new_content': new_content,
                'current_time': current_time,
                'current_time_gmt': current_time_gmt,
                'post_id': post_id
            }
        
        try:
            with self.connection.get_transaction() as session:
                result = session.execute(text(query), params)
                
                if result.rowcount == 0:
                    if expected_modified:
                        logger.warning(f"Concurrency conflict for product {post_id} in site {self.site.name}")
                        return False
                    else:
                        logger.error(f"Product {post_id} not found in site {self.site.name}")
                        return False
                
                logger.info(f"Updated product {post_id} content in site {self.site.name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to update product {post_id} in site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Failed to update product content: {e}") from e
    
    def get_product_meta(self, post_id: int, meta_key: str) -> Optional[str]:
        """Get specific metadata for product."""
        query = f"""
            SELECT meta_value 
            FROM {self.site.db_table_prefix}postmeta 
            WHERE post_id = :post_id AND meta_key = :meta_key
        """
        params = {'post_id': post_id, 'meta_key': meta_key}
        
        try:
            result = self.connection.execute_with_retry(query, params)
            return result[0][0] if result else None
            
        except Exception as e:
            logger.error(f"Failed to get meta {meta_key} for product {post_id}: {e}")
            raise MySQLConnectionError(f"Failed to get product metadata: {e}") from e
    
    def set_product_meta(self, post_id: int, meta_key: str, meta_value: str, 
                        write_once: bool = False) -> bool:
        """Set metadata for product."""
        
        if write_once:
            # Check if meta already exists
            existing = self.get_product_meta(post_id, meta_key)
            if existing is not None:
                logger.info(f"Meta {meta_key} already exists for product {post_id} (write-once)")
                return False
        
        # Use INSERT ... ON DUPLICATE KEY UPDATE for MySQL
        query = f"""
            INSERT INTO {self.site.db_table_prefix}postmeta (post_id, meta_key, meta_value)
            VALUES (:post_id, :meta_key, :meta_value)
            ON DUPLICATE KEY UPDATE meta_value = VALUES(meta_value)
        """
        params = {
            'post_id': post_id,
            'meta_key': meta_key,
            'meta_value': meta_value
        }
        
        try:
            with self.connection.get_transaction() as session:
                session.execute(text(query), params)
                logger.info(f"Set meta {meta_key} for product {post_id} in site {self.site.name}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to set meta {meta_key} for product {post_id}: {e}")
            raise MySQLConnectionError(f"Failed to set product metadata: {e}") from e
    
    def _get_product_meta(self, session: Session, post_id: int) -> List[Dict[str, str]]:
        """Get all metadata for a product."""
        query = f"""
            SELECT meta_key, meta_value 
            FROM {self.site.db_table_prefix}postmeta 
            WHERE post_id = :post_id
        """
        
        result = session.execute(text(query), {'post_id': post_id})
        return [{'meta_key': row.meta_key, 'meta_value': row.meta_value} for row in result]
    
    def get_categories(self) -> List[Dict[str, Any]]:
        """Get all product categories."""
        query = f"""
            SELECT t.term_id, t.name, t.slug, tt.count
            FROM {self.site.db_table_prefix}terms t
            JOIN {self.site.db_table_prefix}term_taxonomy tt ON t.term_id = tt.term_id
            WHERE tt.taxonomy = 'product_cat'
            ORDER BY t.name
        """
        
        try:
            result = self.connection.execute_with_retry(query)
            categories = []
            for row in result:
                categories.append({
                    'id': row[0],
                    'name': row[1],
                    'slug': row[2],
                    'count': row[3]
                })
            
            logger.info(f"Retrieved {len(categories)} categories from site {self.site.name}")
            return categories
            
        except Exception as e:
            logger.error(f"Failed to get categories from site {self.site.name}: {e}")
            raise MySQLConnectionError(f"Failed to retrieve categories: {e}") from e
    
    def test_connection(self) -> Dict[str, Any]:
        """Test connection and return status."""
        return self.connection.test_connection()