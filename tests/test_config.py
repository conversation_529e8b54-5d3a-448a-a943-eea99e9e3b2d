"""Test configuration module."""

import pytest
from pydantic import ValidationError

from structura_ai.config.settings import Settings


def test_settings_with_minimal_config():
    """Test settings with minimal required configuration."""
    # This will fail without required env vars, but that's expected
    with pytest.raises(ValidationError):
        Settings(
            openrouter_api_key="",  # Required but empty
            dev_site_db_host="",    # Required but empty
            dev_site_db_user="",    # Required but empty
            dev_site_db_password="", # Required but empty
            dev_site_db_name=""     # Required but empty
        )


def test_settings_with_valid_config():
    """Test settings with valid configuration."""
    settings = Settings(
        openrouter_api_key="sk-test-key",
        dev_site_db_host="localhost",
        dev_site_db_user="test_user",
        dev_site_db_password="test_pass",
        dev_site_db_name="test_db"
    )
    
    assert settings.openrouter_api_key == "sk-test-key"
    assert settings.dev_site_db_host == "localhost"
    assert settings.similarity_threshold == 0.99  # Default value
    assert settings.default_batch_size == 10      # Default value


def test_settings_validation():
    """Test settings validation."""
    # Test invalid similarity threshold
    with pytest.raises(ValidationError):
        Settings(
            openrouter_api_key="sk-test-key",
            dev_site_db_host="localhost",
            dev_site_db_user="test_user",
            dev_site_db_password="test_pass",
            dev_site_db_name="test_db",
            similarity_threshold=1.5  # Invalid - must be <= 1
        )


def test_retry_backoff_intervals():
    """Test retry backoff intervals parsing."""
    settings = Settings(
        openrouter_api_key="sk-test-key",
        dev_site_db_host="localhost",
        dev_site_db_user="test_user",
        dev_site_db_password="test_pass",
        dev_site_db_name="test_db",
        retry_backoff_seconds="1,2,4,8"
    )
    
    assert settings.retry_backoff_intervals == [1.0, 2.0, 4.0, 8.0]