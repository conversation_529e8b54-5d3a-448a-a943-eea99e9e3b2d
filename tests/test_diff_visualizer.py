"""
Tests for the diff visualization module.

Tests cover unified diff generation, side-by-side comparison,
JSON output, and terminal color formatting.
"""

import json
import pytest
from datetime import datetime
from tempfile import NamedTemporaryFile

from src.structura_ai.services.diff_visualizer import (
    DiffVisualizer,
    DiffFormat,
    DiffResult,
    DiffChunk,
    DiffLine,
    ChangeType,
    TerminalColors
)


class TestChangeTypeEnum:
    """Test ChangeType enum."""
    
    def test_change_type_values(self):
        """Test enum values."""
        assert ChangeType.ADDED.value == "added"
        assert ChangeType.REMOVED.value == "removed"
        assert ChangeType.MODIFIED.value == "modified"
        assert ChangeType.UNCHANGED.value == "unchanged"


class TestDiffLine:
    """Test DiffLine dataclass."""
    
    def test_diff_line_creation(self):
        """Test DiffLine creation with different change types."""
        # Added line
        added_line = DiffLine(
            line_number_original=None,
            line_number_processed=5,
            content="New line content",
            change_type=ChangeType.ADDED
        )
        
        assert added_line.line_number_original is None
        assert added_line.line_number_processed == 5
        assert added_line.change_type == ChangeType.ADDED
        assert not added_line.context
        
        # Removed line
        removed_line = DiffLine(
            line_number_original=3,
            line_number_processed=None,
            content="Removed line content",
            change_type=ChangeType.REMOVED
        )
        
        assert removed_line.line_number_original == 3
        assert removed_line.line_number_processed is None
        assert removed_line.change_type == ChangeType.REMOVED
        
        # Context line
        context_line = DiffLine(
            line_number_original=10,
            line_number_processed=10,
            content="Context line",
            change_type=ChangeType.UNCHANGED,
            context=True
        )
        
        assert context_line.line_number_original == 10
        assert context_line.line_number_processed == 10
        assert context_line.context


class TestDiffChunk:
    """Test DiffChunk dataclass."""
    
    def test_diff_chunk_creation(self):
        """Test DiffChunk creation with lines."""
        lines = [
            DiffLine(1, 1, "Context line", ChangeType.UNCHANGED, True),
            DiffLine(2, None, "Removed line", ChangeType.REMOVED),
            DiffLine(None, 2, "Added line", ChangeType.ADDED),
            DiffLine(3, 3, "Another context", ChangeType.UNCHANGED, True)
        ]
        
        chunk = DiffChunk(
            original_start=1,
            original_count=3,
            processed_start=1,
            processed_count=3,
            lines=lines
        )
        
        assert chunk.original_start == 1
        assert chunk.original_count == 3
        assert len(chunk.lines) == 4


class TestDiffResult:
    """Test DiffResult dataclass."""
    
    def test_diff_result_summary_calculation(self):
        """Test automatic summary calculation in __post_init__."""
        lines = [
            DiffLine(None, 1, "Added line 1", ChangeType.ADDED),
            DiffLine(None, 2, "Added line 2", ChangeType.ADDED),
            DiffLine(1, None, "Removed line", ChangeType.REMOVED),
            DiffLine(2, 3, "Unchanged line", ChangeType.UNCHANGED, True)
        ]
        
        chunk = DiffChunk(1, 2, 1, 3, lines)
        
        result = DiffResult(
            original_title="Original",
            processed_title="Processed",
            chunks=[chunk],
            summary={},  # Will be calculated
            timestamp=datetime.utcnow()
        )
        
        # Check calculated summary
        assert result.summary["total_lines_added"] == 2
        assert result.summary["total_lines_removed"] == 1
        assert result.summary["total_lines_modified"] == 0
        assert result.summary["total_chunks"] == 1
        assert result.summary["has_changes"] is True
    
    def test_diff_result_no_changes(self):
        """Test DiffResult with no changes."""
        result = DiffResult(
            original_title="Original",
            processed_title="Processed",
            chunks=[],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        assert result.summary["total_lines_added"] == 0
        assert result.summary["total_lines_removed"] == 0
        assert result.summary["total_chunks"] == 0
        assert result.summary["has_changes"] is False


class TestTerminalColors:
    """Test terminal color formatting."""
    
    def test_colorize_text(self):
        """Test text colorization."""
        colored = TerminalColors.colorize("test", TerminalColors.RED)
        assert TerminalColors.RED in colored
        assert TerminalColors.RESET in colored
        assert "test" in colored
    
    def test_colorize_with_bold(self):
        """Test bold text colorization."""
        colored = TerminalColors.colorize("test", TerminalColors.GREEN, bold=True)
        assert TerminalColors.BOLD in colored
        assert TerminalColors.GREEN in colored
        assert TerminalColors.RESET in colored
    
    def test_terminal_color_support_detection(self):
        """Test terminal color support detection."""
        # This test may vary based on test environment
        support = TerminalColors.is_terminal_color_supported()
        assert isinstance(support, bool)


class TestDiffVisualizer:
    """Test main diff visualization functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.visualizer = DiffVisualizer(context_lines=2, enable_colors=False)
    
    def test_init(self):
        """Test DiffVisualizer initialization."""
        assert self.visualizer.context_lines == 2
        assert self.visualizer.enable_colors is False
    
    def test_init_with_color_detection(self):
        """Test initialization with automatic color detection."""
        visualizer = DiffVisualizer()
        assert isinstance(visualizer.enable_colors, bool)
    
    def test_generate_diff_identical_content(self):
        """Test diff generation with identical content."""
        original = "Line 1\nLine 2\nLine 3"
        processed = "Line 1\nLine 2\nLine 3"
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert len(result.chunks) == 0
        assert result.summary["has_changes"] is False
        assert result.original_title == "Original"
        assert result.processed_title == "Processed"
    
    def test_generate_diff_simple_addition(self):
        """Test diff generation with line addition."""
        original = "Line 1\nLine 2"
        processed = "Line 1\nNew Line\nLine 2"
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert len(result.chunks) > 0
        assert result.summary["total_lines_added"] == 1
        assert result.summary["total_lines_removed"] == 0
        assert result.summary["has_changes"] is True
    
    def test_generate_diff_simple_removal(self):
        """Test diff generation with line removal."""
        original = "Line 1\nLine to remove\nLine 2"
        processed = "Line 1\nLine 2"
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert len(result.chunks) > 0
        assert result.summary["total_lines_added"] == 0
        assert result.summary["total_lines_removed"] == 1
    
    def test_generate_diff_modification(self):
        """Test diff generation with line modification."""
        original = "Line 1\nOld content\nLine 3"
        processed = "Line 1\nNew content\nLine 3"
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert len(result.chunks) > 0
        assert result.summary["total_lines_added"] == 1
        assert result.summary["total_lines_removed"] == 1
    
    def test_generate_diff_custom_titles(self):
        """Test diff generation with custom titles."""
        original = "Content"
        processed = "Modified content"
        
        result = self.visualizer.generate_diff(
            original, 
            processed,
            original_title="custom_original.html",
            processed_title="custom_processed.html"
        )
        
        assert result.original_title == "custom_original.html"
        assert result.processed_title == "custom_processed.html"
    
    def test_format_unified_diff_no_changes(self):
        """Test unified diff formatting with no changes."""
        result = DiffResult(
            original_title="test.html",
            processed_title="test_processed.html",
            chunks=[],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = self.visualizer.format_unified_diff(result)
        assert "No differences found" in formatted
    
    def test_format_unified_diff_with_changes(self):
        """Test unified diff formatting with changes."""
        lines = [
            DiffLine(1, 1, "Context line", ChangeType.UNCHANGED, True),
            DiffLine(2, None, "Removed line", ChangeType.REMOVED),
            DiffLine(None, 2, "Added line", ChangeType.ADDED),
        ]
        
        chunk = DiffChunk(1, 2, 1, 2, lines)
        
        result = DiffResult(
            original_title="original.html",
            processed_title="processed.html",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = self.visualizer.format_unified_diff(result)
        
        assert "--- original.html" in formatted
        assert "+++ processed.html" in formatted
        assert "@@" in formatted
        assert "-Removed line" in formatted
        assert "+Added line" in formatted
        assert " Context line" in formatted
        assert "Summary:" in formatted
    
    def test_format_unified_diff_with_colors(self):
        """Test unified diff formatting with colors enabled."""
        visualizer = DiffVisualizer(enable_colors=True)
        
        lines = [DiffLine(2, None, "Removed line", ChangeType.REMOVED)]
        chunk = DiffChunk(1, 1, 1, 0, lines)
        
        result = DiffResult(
            original_title="test.html",
            processed_title="test.html",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = visualizer.format_unified_diff(result)
        
        # Should contain ANSI color codes
        assert "\033[" in formatted or not visualizer.enable_colors  # Color codes or colors disabled
    
    def test_format_side_by_side_diff(self):
        """Test side-by-side diff formatting."""
        lines = [
            DiffLine(1, None, "Old line", ChangeType.REMOVED),
            DiffLine(None, 1, "New line", ChangeType.ADDED),
        ]
        
        chunk = DiffChunk(1, 1, 1, 1, lines)
        
        result = DiffResult(
            original_title="Original",
            processed_title="Processed",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = self.visualizer.format_side_by_side_diff(result, width=80)
        
        assert "Original" in formatted
        assert "Processed" in formatted
        assert "|" in formatted  # Column separator
        assert "Old line" in formatted
        assert "New line" in formatted
    
    def test_format_side_by_side_no_changes(self):
        """Test side-by-side formatting with no changes."""
        result = DiffResult(
            original_title="test.html",
            processed_title="test.html",
            chunks=[],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = self.visualizer.format_side_by_side_diff(result)
        assert "No differences found" in formatted
    
    def test_format_json_diff(self):
        """Test JSON diff formatting."""
        lines = [DiffLine(1, None, "Removed", ChangeType.REMOVED)]
        chunk = DiffChunk(1, 1, 1, 0, lines)
        
        result = DiffResult(
            original_title="test.html",
            processed_title="test.html",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        formatted = self.visualizer.format_json_diff(result)
        
        # Should be valid JSON
        data = json.loads(formatted)
        assert data["original_title"] == "test.html"
        assert len(data["chunks"]) == 1
        assert data["chunks"][0]["lines"][0]["change_type"] == "removed"
    
    def test_format_diff_all_formats(self):
        """Test format_diff method with all format types."""
        lines = [DiffLine(1, None, "Test", ChangeType.REMOVED)]
        chunk = DiffChunk(1, 1, 1, 0, lines)
        
        result = DiffResult(
            original_title="test",
            processed_title="test",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        # Test unified format
        unified = self.visualizer.format_diff(result, DiffFormat.UNIFIED)
        assert "---" in unified
        
        # Test side-by-side format
        side_by_side = self.visualizer.format_diff(result, DiffFormat.SIDE_BY_SIDE, width=100)
        assert "|" in side_by_side
        
        # Test JSON format
        json_output = self.visualizer.format_diff(result, DiffFormat.JSON)
        json.loads(json_output)  # Should not raise exception
    
    def test_format_diff_invalid_format(self):
        """Test format_diff with invalid format type."""
        result = DiffResult("a", "b", [], {}, datetime.utcnow())
        
        with pytest.raises(ValueError):
            self.visualizer.format_diff(result, "invalid_format")
    
    def test_compare_html_files(self):
        """Test comparing HTML files."""
        original_content = "<h1>Original Title</h1>\n<p>Content</p>"
        processed_content = "<h1>Modified Title</h1>\n<p>Content</p>"
        
        # Create temporary files
        with NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f1:
            f1.write(original_content)
            original_file = f1.name
        
        with NamedTemporaryFile(mode='w', suffix='.html', delete=False) as f2:
            f2.write(processed_content)
            processed_file = f2.name
        
        try:
            formatted = self.visualizer.compare_html_files(
                original_file,
                processed_file,
                DiffFormat.UNIFIED
            )
            
            assert original_file in formatted
            assert processed_file in formatted
            assert "Original Title" in formatted
            assert "Modified Title" in formatted
        finally:
            import os
            os.unlink(original_file)
            os.unlink(processed_file)
    
    def test_get_diff_statistics(self):
        """Test diff statistics calculation."""
        lines = [
            DiffLine(1, 1, "Unchanged", ChangeType.UNCHANGED),
            DiffLine(2, None, "Removed 1", ChangeType.REMOVED),
            DiffLine(3, None, "Removed 2", ChangeType.REMOVED),
            DiffLine(None, 2, "Added 1", ChangeType.ADDED),
            DiffLine(None, 3, "Added 2", ChangeType.ADDED),
            DiffLine(None, 4, "Added 3", ChangeType.ADDED),
        ]
        
        chunk = DiffChunk(1, 3, 1, 4, lines)
        
        result = DiffResult(
            original_title="test",
            processed_title="test",
            chunks=[chunk],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        stats = self.visualizer.get_diff_statistics(result)
        
        assert stats["total_changes"] == 5  # 2 removed + 3 added
        assert stats["lines_added"] == 3
        assert stats["lines_removed"] == 2
        assert stats["chunks_count"] == 1
        assert stats["change_percentage"] > 0
    
    def test_get_diff_statistics_no_changes(self):
        """Test diff statistics with no changes."""
        result = DiffResult(
            original_title="test",
            processed_title="test",
            chunks=[],
            summary={},
            timestamp=datetime.utcnow()
        )
        
        stats = self.visualizer.get_diff_statistics(result)
        
        assert stats["total_changes"] == 0
        assert stats["lines_added"] == 0
        assert stats["lines_removed"] == 0
        assert stats["change_percentage"] == 0.0


class TestHTMLDiffScenarios:
    """Test diff visualization with HTML-specific scenarios."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.visualizer = DiffVisualizer(context_lines=3, enable_colors=False)
    
    def test_html_tag_addition(self):
        """Test diff with HTML tag addition."""
        original = """<section>
    <h2>Title</h2>
    <p>Content</p>
</section>"""
        
        processed = """<section>
    <h2>Title</h2>
    <p><strong>Content</strong></p>
</section>"""
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        assert result.summary["total_lines_removed"] == 1
        assert result.summary["total_lines_added"] == 1
        
        formatted = self.visualizer.format_unified_diff(result)
        assert "<p>Content</p>" in formatted
        assert "<p><strong>Content</strong></p>" in formatted
    
    def test_html_attribute_changes(self):
        """Test diff with HTML attribute changes."""
        original = '<a href="http://example.com">Link</a>'
        processed = '<a href="https://example.com" target="_blank">Link</a>'
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        
        formatted = self.visualizer.format_unified_diff(result)
        assert 'href="http://example.com"' in formatted
        assert 'href="https://example.com"' in formatted
        assert 'target="_blank"' in formatted
    
    def test_html_structure_reorganization(self):
        """Test diff with HTML structure changes."""
        original = """<div>
    <h1>Title</h1>
    <p>Paragraph 1</p>
    <p>Paragraph 2</p>
</div>"""
        
        processed = """<section>
    <h2>Title</h2>
    <p>Paragraph 1</p>
    <p>Paragraph 2</p>
</section>"""
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        assert result.summary["total_lines_added"] >= 2  # New section and h2
        assert result.summary["total_lines_removed"] >= 2  # Old div and h1
    
    def test_html_content_sanitization_diff(self):
        """Test diff showing HTML sanitization results."""
        original = """<div class="product">
    <h1 style="color: red;">Product Title</h1>
    <p onclick="alert('xss')">Description</p>
    <script>trackEvent();</script>
</div>"""
        
        processed = """<section>
    <h2>Product Title</h2>
    <p>Description</p>
</section>"""
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        
        formatted = self.visualizer.format_unified_diff(result)
        
        # Should show removal of dangerous elements
        assert "script" in formatted  # In removed lines
        assert "onclick" in formatted  # In removed lines
        assert "style=" in formatted  # In removed lines
    
    def test_large_html_diff_performance(self):
        """Test diff performance with large HTML content."""
        # Generate large HTML content
        original_lines = []
        processed_lines = []
        
        for i in range(100):
            original_lines.append(f"    <p>Original paragraph {i}</p>")
            if i == 50:
                # Add a change in the middle
                processed_lines.append(f"    <p><strong>Modified paragraph {i}</strong></p>")
            else:
                processed_lines.append(f"    <p>Original paragraph {i}</p>")
        
        original = "<section>\n" + "\n".join(original_lines) + "\n</section>"
        processed = "<section>\n" + "\n".join(processed_lines) + "\n</section>"
        
        # Should complete without timeout
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        assert len(result.chunks) > 0
        
        # Should be able to format without issues
        formatted = self.visualizer.format_unified_diff(result)
        assert len(formatted) > 0
    
    def test_multiline_html_elements(self):
        """Test diff with multiline HTML elements."""
        original = """<table>
    <tr>
        <td>
            Cell content
            with multiple lines
        </td>
    </tr>
</table>"""
        
        processed = """<table>
    <thead>
        <tr>
            <th>Header</th>
        </tr>
    </thead>
    <tbody>
        <tr>
            <td>
                Cell content
                with multiple lines
            </td>
        </tr>
    </tbody>
</table>"""
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        assert result.summary["total_lines_added"] > 0
        
        formatted = self.visualizer.format_unified_diff(result)
        assert "thead" in formatted
        assert "tbody" in formatted
    
    def test_unicode_html_content_diff(self):
        """Test diff with Unicode HTML content."""
        original = """<section>
    <h2>Título en español</h2>
    <p>Contenido con acentos: áéíóú</p>
</section>"""
        
        processed = """<section>
    <h2>Title in English</h2>
    <p>Content with accents: áéíóú</p>
</section>"""
        
        result = self.visualizer.generate_diff(original, processed)
        
        assert result.summary["has_changes"]
        
        # Test all output formats work with Unicode
        unified = self.visualizer.format_unified_diff(result)
        assert "Título" in unified
        assert "áéíóú" in unified
        
        side_by_side = self.visualizer.format_side_by_side_diff(result)
        assert "español" in side_by_side
        
        json_output = self.visualizer.format_json_diff(result)
        data = json.loads(json_output)
        assert any("español" in str(chunk) for chunk in data.get("chunks", []))


class TestDiffVisualizerEdgeCases:
    """Test edge cases and error conditions."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.visualizer = DiffVisualizer()
    
    def test_empty_content_diff(self):
        """Test diff with empty content."""
        result = self.visualizer.generate_diff("", "")
        assert len(result.chunks) == 0
        assert not result.summary["has_changes"]
    
    def test_one_empty_content_diff(self):
        """Test diff where one content is empty."""
        result = self.visualizer.generate_diff("", "New content")
        assert result.summary["total_lines_added"] == 1
        assert result.summary["total_lines_removed"] == 0
        
        result2 = self.visualizer.generate_diff("Old content", "")
        assert result2.summary["total_lines_added"] == 0
        assert result2.summary["total_lines_removed"] == 1
    
    def test_single_line_diff(self):
        """Test diff with single line content."""
        result = self.visualizer.generate_diff("old", "new")
        assert result.summary["has_changes"]
        assert result.summary["total_lines_added"] == 1
        assert result.summary["total_lines_removed"] == 1
    
    def test_whitespace_only_changes(self):
        """Test diff with whitespace-only changes."""
        original = "line1\nline2\nline3"
        processed = "line1\n  line2  \nline3"  # Added spaces around line2
        
        result = self.visualizer.generate_diff(original, processed)
        assert result.summary["has_changes"]
        
        formatted = self.visualizer.format_unified_diff(result)
        assert "line2" in formatted
        assert "  line2  " in formatted