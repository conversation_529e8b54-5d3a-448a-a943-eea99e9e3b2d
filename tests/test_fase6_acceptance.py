#!/usr/bin/env python3
"""
Test di accettazione FASE 6 - CLI e Gestione Operativa

Questo test verifica tutti i criteri di accettazione specifici della FASE 6
come definiti nel documento fasi-sviluppo-v1.md:

CRITERI DI ACCETTAZIONE FASE 6:
✓ CLI completa e funzionale per tutti i casi d'uso  
✓ Gestione siti e profili completamente operativa
✓ Tutti i criteri DoD del PRD soddisfatti
✓ Test end-to-end passano al 100%
✓ Documentazione utente completa
"""

import pytest
from click.testing import CliRunner

from src.structura_ai.main import cli
from src.structura_ai.config import get_settings


class TestFase6AcceptanceCriteria:
    """Test di accettazione per FASE 6."""
    
    def setup_method(self):
        """Setup test environment."""
        self.runner = CliRunner()
        
    def test_criterio_1_cli_completa_e_funzionale(self):
        """
        CRITERIO 1: CLI completa e funzionale per tutti i casi d'uso
        
        Verifica che tutti i comandi CLI principali siano disponibili e funzionali.
        """
        # Test comando principale
        result = self.runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        
        # Verifica presenza tutti i comandi richiesti dal PRD
        expected_commands = ['site', 'profile', 'preview', 'run', 'system']
        for cmd in expected_commands:
            assert cmd in result.output, f"Comando '{cmd}' mancante dalla CLI"
        
        # Test che ogni comando sia effettivamente accessibile
        for cmd in expected_commands:
            result = self.runner.invoke(cli, [cmd, '--help'])
            assert result.exit_code == 0, f"Comando '{cmd}' non funziona correttamente"
    
    def test_criterio_2_gestione_siti_completa(self):
        """
        CRITERIO 2: Gestione siti WooCommerce completamente operativa
        
        Verifica tutti i comandi di gestione siti secondo PRD sezione 10.
        """
        # Test struttura comandi siti
        result = self.runner.invoke(cli, ['site', '--help'])
        assert result.exit_code == 0
        
        # Verifica presenza sottomandi richiesti
        expected_site_commands = ['add', 'list', 'remove', 'test', 'info', 'update']
        for subcmd in expected_site_commands:
            assert subcmd in result.output, f"Sottocommando site '{subcmd}' mancante"
        
        # Test comando site add con parametri PRD
        result = self.runner.invoke(cli, ['site', 'add', '--help'])
        assert result.exit_code == 0
        
        # Verifica parametri richiesti dal PRD
        required_params = ['--host', '--user', '--password', '--database']
        for param in required_params:
            assert param in result.output, f"Parametro '{param}' mancante in site add"
        
        # Verifica parametri opzionali
        optional_params = ['--prefix', '--display-name', '--description', '--test-connection']
        for param in optional_params:
            assert param in result.output, f"Parametro opzionale '{param}' mancante"
    
    def test_criterio_3_gestione_profili_completa(self):
        """
        CRITERIO 3: Gestione profili completamente operativa
        
        Verifica tutti i comandi di gestione profili secondo PRD sezione 10.
        """
        # Test struttura comandi profili
        result = self.runner.invoke(cli, ['profile', '--help'])
        assert result.exit_code == 0
        
        # Verifica presenza sottocomandi richiesti
        expected_profile_commands = ['create', 'list', 'delete', 'show', 'export', 'update', 'copy']
        for subcmd in expected_profile_commands:
            assert subcmd in result.output, f"Sottocommando profile '{subcmd}' mancante"
        
        # Test comando create con parametri configurazione
        result = self.runner.invoke(cli, ['profile', 'create', '--help'])
        assert result.exit_code == 0
        
        # Verifica parametri configurazione dal PRD sezione 15
        config_params = ['--similarity-threshold', '--llm-temperature', '--from-json']
        for param in config_params:
            assert param in result.output, f"Parametro configurazione '{param}' mancante"
        
        # Test import/export profili JSON (PRD 6.5)
        result = self.runner.invoke(cli, ['profile', 'export', '--help'])
        assert result.exit_code == 0
        assert '--output' in result.output
    
    def test_criterio_4_comandi_operativi_completi(self):
        """
        CRITERIO 4: Comandi operativi preview/run completi
        
        Verifica comandi preview e run secondo PRD sezioni 8.1 e 8.2.
        """
        # Test comando preview (PRD 8.1)
        result = self.runner.invoke(cli, ['preview', '--help'])
        assert result.exit_code == 0
        
        # Verifica parametri richiesti
        required_preview_params = ['--site', '--profile']
        for param in required_preview_params:
            assert param in result.output, f"Parametro preview '{param}' mancante"
        
        # Verifica parametri opzionali PRD
        optional_preview_params = ['--categories', '--post-ids', '--batch-size', '--report-format', '--output']
        for param in optional_preview_params:
            assert param in result.output, f"Parametro preview opzionale '{param}' mancante"
        
        # Test comando run (PRD 8.2)
        result = self.runner.invoke(cli, ['run', '--help'])
        assert result.exit_code == 0
        
        # Verifica parametri richiesti
        required_run_params = ['--site', '--profile', '--confirm']
        for param in required_run_params:
            assert param in result.output, f"Parametro run '{param}' mancante"
    
    def test_criterio_5_parametri_configurazione_prd(self):
        """
        CRITERIO 5: Parametri configurazione conformi PRD sezione 15
        
        Verifica che tutti i parametri di default siano conformi al PRD.
        """
        settings = get_settings()
        
        # Test parametri configurazione PRD sezione 15
        assert settings.default_batch_size == 10, "Batch size default non conforme (deve essere 10)"
        assert settings.similarity_threshold == 0.99, "Soglia similarità default non conforme (deve essere 0.99)"
        assert settings.length_diff_threshold == 5, "Soglia differenza lunghezza non conforme (deve essere 5)"
        assert settings.openrouter_temperature == 0.0, "Temperatura default non conforme (deve essere 0.0)"
        assert settings.retry_backoff_seconds == "2,4,8", "Retry policy non conforme (deve essere 2,4,8s)"
        assert settings.openrouter_default_model == "z-ai/glm-4.5-air:free", "Modello LLM default non conforme"
    
    def test_criterio_6_validazione_parametri(self):
        """
        CRITERIO 6: Validazione parametri input
        
        Verifica che la validazione parametri funzioni correttamente.
        """
        # Test validazione batch-size
        result = self.runner.invoke(cli, ['preview', '--help'])
        assert result.exit_code == 0
        assert 'default: 10' in result.output, "Default batch size non mostrato in help"
        
        # Test validazione report-format
        assert 'json' in result.output, "Formato JSON non disponibile"
        assert 'csv' in result.output, "Formato CSV non disponibile"
        
        # Test skip-processed flag (PRD 5.5)
        assert 'skip-processed' in result.output, "Flag skip-processed mancante"
        assert 'default: True' in result.output, "Default skip-processed non corretto"
    
    def test_criterio_7_comandi_sistema_diagnostica(self):
        """
        CRITERIO 7: Comandi sistema e diagnostica
        
        Verifica disponibilità comandi diagnostici per troubleshooting.
        """
        # Test comandi sistema
        result = self.runner.invoke(cli, ['system', '--help'])
        assert result.exit_code == 0
        
        # Verifica sottocomandi diagnostici
        expected_system_commands = ['status', 'init', 'check', 'info', 'reset']
        for subcmd in expected_system_commands:
            assert subcmd in result.output, f"Comando sistema '{subcmd}' mancante"
        
        # Test funzionalità comando status
        result = self.runner.invoke(cli, ['system', 'status'])
        assert result.exit_code == 0
        assert "Status Sistema Structura AI" in result.output
        
        # Verifica informazioni essenziali in status
        status_info = ['Configurazione', 'Database', 'API Keys', 'Profili']
        for info in status_info:
            assert info in result.output, f"Informazione '{info}' mancante in system status"
    
    def test_criterio_8_opzioni_globali(self):
        """
        CRITERIO 8: Opzioni globali CLI
        
        Verifica disponibilità opzioni globali per logging e debug.
        """
        # Test opzioni globali
        result = self.runner.invoke(cli, ['--help'])
        assert result.exit_code == 0
        
        # Verifica opzioni globali richieste
        global_options = ['--debug', '--log-file', '--help']
        for option in global_options:
            assert option in result.output, f"Opzione globale '{option}' mancante"
        
        # Test che opzioni globali funzionino
        result = self.runner.invoke(cli, ['--debug', '--help'])
        assert result.exit_code == 0
    
    def test_criterio_9_gestione_errori(self):
        """
        CRITERIO 9: Gestione errori CLI
        
        Verifica che gli errori siano gestiti correttamente.
        """
        # Test comando inesistente
        result = self.runner.invoke(cli, ['comando-inesistente'])
        assert result.exit_code != 0
        
        # Test parametri mancanti
        result = self.runner.invoke(cli, ['preview'])  # senza --site e --profile
        assert result.exit_code != 0
        
        # Test flag non validi
        result = self.runner.invoke(cli, ['--flag-inesistente'])
        assert result.exit_code != 0
    
    def test_criterio_10_help_system_completo(self):
        """
        CRITERIO 10: Sistema help completo
        
        Verifica che ogni comando abbia help contestuale e informativo.
        """
        # Lista tutti i comandi da testare
        commands_to_test = [
            ['site'],
            ['site', 'add'],
            ['site', 'list'],
            ['profile'],
            ['profile', 'create'],
            ['profile', 'list'],
            ['system'],
            ['system', 'status'],
            ['preview'],
            ['run']
        ]
        
        # Test help per ogni comando
        for cmd_path in commands_to_test:
            result = self.runner.invoke(cli, cmd_path + ['--help'])
            assert result.exit_code == 0, f"Help fallito per comando: {' '.join(cmd_path)}"
            assert len(result.output) > 50, f"Help troppo breve per comando: {' '.join(cmd_path)}"
    
    def test_criterio_11_meta_keys_configurabili(self):
        """
        CRITERIO 11: Meta keys configurabili (PRD 15)
        
        Verifica che le meta keys siano configurabili come richiesto dal PRD.
        """
        # Test che i meta keys siano definiti nei settings o profile
        result = self.runner.invoke(cli, ['profile', 'show', 'default'])
        assert result.exit_code == 0
        
        # Verifica presenza meta keys nella configurazione profilo
        assert 'Processed Meta Key:' in result.output, "Processed meta key non configurabile"
        assert 'Backup Meta Key:' in result.output, "Backup meta key non configurabile"
        
        # Verifica valori default conformi PRD
        assert '_ai_processed_v1' in result.output, "Processed meta key default non conforme"
        assert '_ai_original_content_v1' in result.output, "Backup meta key default non conforme"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])