"""
Comprehensive tests for HTML sanitization module.

Tests cover all Phase 4 requirements including whitelist enforcement,
dangerous tag removal, attribute sanitization, and validation.
"""

import pytest
from bs4 import BeautifulSoup

from src.structura_ai.services.html_sanitizer import (
    HTMLSanitizer,
    HTMLSanitizationError,
    CompactHTMLFormatter
)


class TestHTMLSanitizer:
    """Test suite for HTML sanitization functionality."""
    
    def setup_method(self):
        """Set up test fixture."""
        self.sanitizer = HTMLSanitizer()
    
    def test_init(self):
        """Test sanitizer initialization."""
        assert isinstance(self.sanitizer, HTMLSanitizer)
        assert self.sanitizer.ALLOWED_TAGS
        assert self.sanitizer.ALLOWED_ATTRIBUTES
        assert self.sanitizer.FORBIDDEN_TAGS
    
    def test_allowed_tags_whitelist(self):
        """Test that only whitelisted tags are preserved."""
        html = """
        <section>
            <h2>Title</h2>
            <h3>Subtitle</h3>
            <p>Paragraph with <strong>bold</strong> and <em>italic</em></p>
            <ul>
                <li>List item</li>
            </ul>
            <ol>
                <li>Numbered item</li>
            </ol>
            <table>
                <thead>
                    <tr><th>Header</th></tr>
                </thead>
                <tbody>
                    <tr><td>Data</td></tr>
                </tbody>
            </table>
            <a href="https://example.com" title="Link">Link</a>
            <code>code snippet</code>
            <br>
        </section>
        """
        
        result = self.sanitizer.sanitize_html(html)
        
        # All allowed tags should be present
        soup = BeautifulSoup(result, 'html.parser')
        assert soup.find('section')
        assert soup.find('h2')
        assert soup.find('h3')
        assert soup.find('p')
        assert soup.find('strong')
        assert soup.find('em')
        assert soup.find('ul')
        assert soup.find('ol')
        assert soup.find('li')
        assert soup.find('table')
        assert soup.find('thead')
        assert soup.find('tbody')
        assert soup.find('tr')
        assert soup.find('th')
        assert soup.find('td')
        assert soup.find('a')
        assert soup.find('code')
        assert soup.find('br')
    
    def test_forbidden_tags_removal(self):
        """Test that forbidden tags are removed."""
        html = """
        <p>Safe content</p>
        <script>alert('XSS');</script>
        <style>body { color: red; }</style>
        <iframe src="malicious.html"></iframe>
        <object data="malicious.swf"></object>
        <embed src="malicious.swf">
        <form><input type="text"></form>
        <meta charset="utf-8">
        <link rel="stylesheet" href="malicious.css">
        """
        
        result = self.sanitizer.sanitize_html(html, strict_mode=False)
        soup = BeautifulSoup(result, 'html.parser')
        
        # Forbidden tags should be removed
        assert not soup.find('script')
        assert not soup.find('style')
        assert not soup.find('iframe')
        assert not soup.find('object')
        assert not soup.find('embed')
        assert not soup.find('form')
        assert not soup.find('input')
        assert not soup.find('meta')
        assert not soup.find('link')
        
        # Safe content should remain
        assert soup.find('p')
        assert 'Safe content' in result
    
    def test_strict_mode_forbidden_tags(self):
        """Test that strict mode raises exceptions for forbidden tags."""
        html = '<p>Safe</p><script>alert("XSS");</script>'
        
        with pytest.raises(HTMLSanitizationError) as exc_info:
            self.sanitizer.sanitize_html(html, strict_mode=True)
        
        assert "Forbidden tag detected" in str(exc_info.value)
        assert "script" in str(exc_info.value)
    
    def test_link_href_validation(self):
        """Test href attribute validation for links."""
        # Valid URLs
        valid_html = """
        <a href="https://example.com">HTTPS</a>
        <a href="http://example.com">HTTP</a>
        <a href="mailto:<EMAIL>">Email</a>
        <a href="tel:+1234567890">Phone</a>
        <a href="/relative/path">Relative</a>
        <a href="ftp://files.example.com">FTP</a>
        """
        
        result = self.sanitizer.sanitize_html(valid_html)
        soup = BeautifulSoup(result, 'html.parser')
        links = soup.find_all('a')
        
        assert len(links) == 6
        assert all(link.get('href') for link in links)
    
    def test_dangerous_href_removal(self):
        """Test that dangerous href values are removed."""
        dangerous_html = """
        <a href="javascript:alert('XSS')">JS Link</a>
        <a href="data:text/html,<script>alert('XSS')</script>">Data URL</a>
        <a href="vbscript:msgbox('XSS')">VBScript</a>
        """
        
        result = self.sanitizer.sanitize_html(dangerous_html)
        soup = BeautifulSoup(result, 'html.parser')
        links = soup.find_all('a')
        
        # Links should remain but without href attributes
        assert len(links) == 3
        for link in links:
            assert not link.get('href')
    
    def test_target_blank_security(self):
        """Test that target='_blank' gets proper rel attributes."""
        html = '<a href="https://example.com" target="_blank">External</a>'
        
        result = self.sanitizer.sanitize_html(html)
        soup = BeautifulSoup(result, 'html.parser')
        link = soup.find('a')
        
        assert link.get('target') == '_blank'
        rel = link.get('rel')
        assert 'noopener' in rel
        assert 'noreferrer' in rel
    
    def test_attribute_sanitization(self):
        """Test that only allowed attributes are preserved."""
        html = """
        <a href="https://example.com" title="Link" onclick="alert('XSS')" class="link">Link</a>
        <p style="color: red;" data-custom="value">Paragraph</p>
        <h2 id="header" class="title">Header</h2>
        """
        
        result = self.sanitizer.sanitize_html(html)
        soup = BeautifulSoup(result, 'html.parser')
        
        # Link should keep only allowed attributes
        link = soup.find('a')
        assert link.get('href')
        assert link.get('title')
        assert not link.get('onclick')
        assert not link.get('class')
        
        # Other tags should not have disallowed attributes
        p = soup.find('p')
        assert not p.get('style')
        assert not p.get('data-custom')
        
        h2 = soup.find('h2')
        assert not h2.get('id')
        assert not h2.get('class')
    
    def test_html_validation_well_formed(self):
        """Test HTML validation for well-formed content."""
        valid_html = '<section><h2>Title</h2><p>Content</p></section>'
        
        is_valid, errors = self.sanitizer.validate_html(valid_html)
        
        assert is_valid
        assert len(errors) == 0
    
    def test_html_validation_forbidden_tags(self):
        """Test HTML validation detects forbidden tags."""
        invalid_html = '<p>Content</p><script>alert("XSS");</script>'
        
        is_valid, errors = self.sanitizer.validate_html(invalid_html)
        
        assert not is_valid
        assert len(errors) > 0
        assert any('script' in error.lower() for error in errors)
    
    def test_html_validation_javascript_attributes(self):
        """Test HTML validation detects JavaScript in attributes."""
        invalid_html = '<a href="javascript:alert(\'XSS\')">Link</a>'
        
        is_valid, errors = self.sanitizer.validate_html(invalid_html)
        
        assert not is_valid
        assert len(errors) > 0
        assert any('javascript' in error.lower() for error in errors)
    
    def test_html_validation_event_handlers(self):
        """Test HTML validation detects event handler attributes."""
        invalid_html = '<p onclick="alert(\'XSS\')">Click me</p>'
        
        is_valid, errors = self.sanitizer.validate_html(invalid_html)
        
        assert not is_valid
        assert len(errors) > 0
        assert any('event handler' in error.lower() for error in errors)
    
    def test_pretty_print_html(self):
        """Test HTML pretty-printing functionality."""
        html = '<section><h2>Title</h2><p>Content with <strong>bold</strong> text.</p></section>'
        
        result = self.sanitizer.pretty_print_html(html)
        
        # Should be formatted with proper indentation
        lines = result.split('\n')
        assert len(lines) > 1
        
        # Check basic structure is preserved
        assert 'section' in result.lower()
        assert 'h2' in result.lower()
        assert 'strong' in result.lower()
    
    def test_pretty_print_custom_indent(self):
        """Test pretty-printing with custom indentation."""
        html = '<section><h2>Title</h2></section>'
        
        result = self.sanitizer.pretty_print_html(html, indent=4)
        
        # Should contain proper indentation
        lines = result.split('\n')
        indented_lines = [line for line in lines if line.startswith('    ')]
        assert len(indented_lines) > 0
    
    def test_process_html_complete_pipeline(self):
        """Test complete HTML processing pipeline."""
        html = """
        <section>
            <h2>Product Title</h2>
            <p>Description with <strong>important</strong> info</p>
            <script>alert('XSS');</script>
            <a href="https://example.com" target="_blank">Link</a>
        </section>
        """
        
        result = self.sanitizer.process_html(html, pretty_print=True)
        
        # Check result structure
        assert 'original_html' in result
        assert 'sanitized_html' in result
        assert 'formatted_html' in result
        assert 'is_valid' in result
        assert 'validation_errors' in result
        assert 'processing_errors' in result
        assert 'stats' in result
        
        # Check sanitization worked
        assert 'script' not in result['sanitized_html']
        assert 'Product Title' in result['sanitized_html']
        
        # Check validation
        assert result['is_valid']
        assert len(result['processing_errors']) == 0
        
        # Check stats
        stats = result['stats']
        assert stats['original_length'] > 0
        assert stats['sanitized_length'] > 0
        assert stats['formatted_length'] > 0
    
    def test_process_html_with_errors(self):
        """Test HTML processing with validation errors."""
        html = '<script>alert("XSS");</script><p>Content</p>'
        
        # Should not raise exception in non-strict mode
        result = self.sanitizer.process_html(html, pretty_print=True)
        
        # Script should be removed
        assert 'script' not in result['sanitized_html']
        assert 'Content' in result['sanitized_html']
    
    def test_empty_content_handling(self):
        """Test handling of empty or None content."""
        # Empty string
        result = self.sanitizer.sanitize_html("")
        assert result == ""
        
        # None content  
        result = self.sanitizer.sanitize_html(None)
        assert result == ""
        
        # Whitespace only
        result = self.sanitizer.sanitize_html("   \n\t   ")
        assert result == ""
    
    def test_inline_css_removal(self):
        """Test that inline CSS is removed."""
        html = '<p style="color: red; background: blue;">Styled content</p>'
        
        result = self.sanitizer.sanitize_html(html)
        
        # Style attribute should be removed
        assert 'style=' not in result
        assert 'Styled content' in result
    
    def test_malformed_html_handling(self):
        """Test handling of malformed HTML."""
        malformed_html = '<p>Unclosed paragraph<div>Nested incorrectly<span>More nesting'
        
        # Should not raise exception
        result = self.sanitizer.sanitize_html(malformed_html)
        
        # Content should be preserved even if structure is fixed
        assert 'Unclosed paragraph' in result
        assert 'Nested incorrectly' in result
        assert 'More nesting' in result
    
    def test_special_characters_preservation(self):
        """Test that special characters are preserved."""
        html = '<p>Special chars: &amp; &lt; &gt; &quot; &#39; €</p>'
        
        result = self.sanitizer.sanitize_html(html)
        
        # Special characters should be preserved
        assert '&amp;' in result or '&' in result
        assert '&lt;' in result or '<' in result
        assert '&gt;' in result or '>' in result
        assert 'Special chars:' in result
    
    def test_unicode_content_handling(self):
        """Test handling of Unicode content."""
        html = '<p>Unicode: áéíóú ñ 中文 العربية русский</p>'
        
        result = self.sanitizer.sanitize_html(html)
        
        # Unicode content should be preserved
        assert 'áéíóú' in result
        assert '中文' in result
        assert 'العربية' in result
        assert 'русский' in result
    
    def test_multiple_href_protocols(self):
        """Test various href protocols."""
        html = """
        <a href="https://secure.example.com">HTTPS</a>
        <a href="http://example.com">HTTP</a>
        <a href="mailto:<EMAIL>">Email</a>
        <a href="tel:******-567-8900">Phone</a>
        <a href="ftp://files.example.com/file.zip">FTP</a>
        <a href="/path/to/page">Relative</a>
        <a href="../parent/page">Parent Relative</a>
        <a href="#anchor">Anchor</a>
        """
        
        result = self.sanitizer.sanitize_html(html)
        soup = BeautifulSoup(result, 'html.parser')
        links = soup.find_all('a')
        
        # All valid protocols should be preserved
        hrefs = [link.get('href') for link in links if link.get('href')]
        assert len(hrefs) == 8
        
        # Check specific protocols
        protocols = [href.split(':')[0] if ':' in href else 'relative' for href in hrefs]
        assert 'https' in protocols
        assert 'http' in protocols
        assert 'mailto' in protocols
        assert 'tel' in protocols
        assert 'ftp' in protocols
    
    def test_nested_tag_structure(self):
        """Test complex nested tag structures."""
        html = """
        <section>
            <h2>Main Title</h2>
            <table>
                <thead>
                    <tr>
                        <th><strong>Column 1</strong></th>
                        <th><em>Column 2</em></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <ul>
                                <li>Item <code>code</code></li>
                                <li>Item with <a href="/link">link</a></li>
                            </ul>
                        </td>
                        <td>
                            <p>Paragraph with <strong><em>bold italic</em></strong></p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </section>
        """
        
        result = self.sanitizer.sanitize_html(html)
        soup = BeautifulSoup(result, 'html.parser')
        
        # Check nested structure is preserved
        assert soup.find('section').find('table')
        assert soup.find('table').find('thead').find('th').find('strong')
        assert soup.find('tbody').find('td').find('ul').find('li').find('code')
        assert soup.find('td').find('p').find('strong').find('em')
        
        # Check content is preserved
        assert 'Main Title' in result
        assert 'Column 1' in result
        assert 'bold italic' in result


class TestCompactHTMLFormatter:
    """Test suite for HTML formatter."""
    
    def setup_method(self):
        """Set up test fixture."""
        self.formatter = CompactHTMLFormatter()
    
    def test_attributes_formatting(self):
        """Test attribute formatting with consistent ordering."""
        html = '<a title="Link Title" href="https://example.com" target="_blank">Link</a>'
        soup = BeautifulSoup(html, 'html.parser')
        tag = soup.find('a')
        
        attributes = self.formatter.attributes(tag)
        
        # Should contain all attributes
        assert 'href=' in attributes
        assert 'title=' in attributes
        assert 'target=' in attributes
    
    def test_empty_attributes(self):
        """Test formatting tag with no attributes."""
        html = '<p>Content</p>'
        soup = BeautifulSoup(html, 'html.parser')
        tag = soup.find('p')
        
        attributes = self.formatter.attributes(tag)
        
        assert attributes == ""
    
    def test_list_attribute_values(self):
        """Test formatting attributes with list values."""
        html = '<div class="class1 class2">Content</div>'
        soup = BeautifulSoup(html, 'html.parser')
        tag = soup.find('div')
        
        # BeautifulSoup parses class as list
        if isinstance(tag.attrs.get('class'), list):
            attributes = self.formatter.attributes(tag)
            assert 'class="class1 class2"' in attributes


# Fixture for integration tests
@pytest.fixture
def sample_woocommerce_content():
    """Sample WooCommerce product content for testing."""
    return """
    <div class="product-description">
        <h2>Product Features</h2>
        <ul>
            <li><strong>Feature 1:</strong> High quality materials</li>
            <li><strong>Feature 2:</strong> Advanced technology</li>
        </ul>
        
        <h3>Technical Specifications</h3>
        <table>
            <thead>
                <tr>
                    <th>Specification</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Dimensions</td>
                    <td>10 x 20 x 30 cm</td>
                </tr>
                <tr>
                    <td>Weight</td>
                    <td>1.5 kg</td>
                </tr>
            </tbody>
        </table>
        
        <p>For more information, visit <a href="https://example.com/info">our website</a>.</p>
        
        <script>
            // This should be removed
            analytics.track('product_view');
        </script>
        
        <style>
            .product-description { color: blue; }
        </style>
    </div>
    """


class TestWooCommerceIntegration:
    """Integration tests with WooCommerce-style content."""
    
    def test_woocommerce_content_sanitization(self, sample_woocommerce_content):
        """Test sanitization of typical WooCommerce product content."""
        sanitizer = HTMLSanitizer()
        
        result = sanitizer.process_html(sample_woocommerce_content)
        
        # Should remove dangerous elements
        sanitized = result['sanitized_html']
        assert 'script' not in sanitized.lower()
        assert 'style' not in sanitized.lower()
        assert 'div' not in sanitized.lower()  # Not in allowed tags
        
        # Should preserve safe content
        assert 'Product Features' in sanitized
        assert 'Technical Specifications' in sanitized
        assert 'High quality materials' in sanitized
        
        # Should preserve allowed structure
        soup = BeautifulSoup(sanitized, 'html.parser')
        assert soup.find('h2')
        assert soup.find('h3')
        assert soup.find('ul')
        assert soup.find('li')
        assert soup.find('strong')
        assert soup.find('table')
        assert soup.find('thead')
        assert soup.find('tbody')
        assert soup.find('tr')
        assert soup.find('th')
        assert soup.find('td')
        assert soup.find('p')
        assert soup.find('a')
        
        # Should validate successfully
        assert result['is_valid']
        assert len(result['processing_errors']) == 0
    
    def test_performance_with_large_content(self):
        """Test performance with large HTML content."""
        # Generate large content
        large_html = '<section>'
        for i in range(100):
            large_html += f"""
            <h2>Section {i}</h2>
            <p>Content for section {i} with <strong>bold</strong> and <em>italic</em> text.</p>
            <ul>
                <li>Item 1 for section {i}</li>
                <li>Item 2 for section {i}</li>
            </ul>
            """
        large_html += '</section>'
        
        sanitizer = HTMLSanitizer()
        
        # Should complete without timeout
        result = sanitizer.process_html(large_html)
        
        assert result['is_valid']
        assert len(result['processing_errors']) == 0
        assert result['stats']['formatted_length'] > 0