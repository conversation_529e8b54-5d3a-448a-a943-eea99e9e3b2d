"""Test MySQL connection functionality."""

import pytest
from unittest.mock import Mock, patch, MagicMock
from sqlalchemy.exc import OperationalError

from structura_ai.models import Site
from structura_ai.services.mysql_connection import WooCommerceConnection, MySQLConnectionError
from structura_ai.services.woocommerce_repository import WooCommerceRepository, WooCommerceProduct


@pytest.fixture
def mock_site():
    """Create a mock site for testing."""
    site = Site(
        name="test_site",
        display_name="Test Site",
        db_host="localhost",
        db_port=3306,
        db_name="test_woo",
        db_user="test_user",
        db_password="test_pass",
        db_table_prefix="wp_"
    )
    site.id = 1
    return site


@pytest.fixture
def mock_connection(mock_site):
    """Create a mock WooCommerce connection."""
    return WooCommerceConnection(mock_site)


def test_connection_string_building(mock_connection):
    """Test connection string building."""
    expected = "mysql+pymysql://test_user:test_pass@localhost:3306/test_woo?charset=utf8mb4"
    assert mock_connection._build_connection_string() == expected


@patch('structura_ai.services.mysql_connection.create_engine')
def test_engine_creation(mock_create_engine, mock_connection):
    """Test SQLAlchemy engine creation."""
    mock_engine = Mock()
    mock_create_engine.return_value = mock_engine
    
    engine = mock_connection.engine
    
    assert engine == mock_engine
    mock_create_engine.assert_called_once()
    
    # Test engine caching
    engine2 = mock_connection.engine
    assert engine2 == mock_engine
    assert mock_create_engine.call_count == 1


def test_woocommerce_product_creation():
    """Test WooCommerceProduct data class."""
    data = {
        'ID': 123,
        'post_title': 'Test Product',
        'post_content': '<p>Test content</p>',
        'post_status': 'publish',
        'post_modified': '2023-08-05 10:00:00',
        'post_modified_gmt': '2023-08-05 10:00:00',
        'meta_data': [
            {'meta_key': '_price', 'meta_value': '29.99'},
            {'meta_key': '_stock', 'meta_value': '10'}
        ]
    }
    
    product = WooCommerceProduct(data)
    
    assert product.id == 123
    assert product.post_title == 'Test Product'
    assert product.post_content == '<p>Test content</p>'
    assert product.content_length == 20
    assert product.has_content is True
    assert product.get_meta('_price') == '29.99'
    assert product.get_meta('_stock') == '10'
    assert product.get_meta('_nonexistent') is None
    assert product.get_meta('_nonexistent', 'default') == 'default'


def test_woocommerce_product_empty_content():
    """Test WooCommerceProduct with empty content."""
    data = {
        'ID': 124,
        'post_title': 'Empty Product',
        'post_content': '',
        'post_status': 'draft',
        'post_modified': '2023-08-05 10:00:00',
        'post_modified_gmt': '2023-08-05 10:00:00'
    }
    
    product = WooCommerceProduct(data)
    
    assert product.content_length == 0
    assert product.has_content is False


@patch('structura_ai.services.mysql_connection.sessionmaker')
@patch('structura_ai.services.mysql_connection.create_engine')
def test_connection_test_success(mock_create_engine, mock_sessionmaker, mock_connection):
    """Test successful connection test."""
    # Mock engine and session
    mock_engine = Mock()
    mock_create_engine.return_value = mock_engine
    
    mock_session = Mock()
    mock_sessionmaker.return_value = mock_session
    
    # Mock session context manager
    mock_session_instance = Mock()
    mock_session.__enter__ = Mock(return_value=mock_session_instance)
    mock_session.__exit__ = Mock(return_value=None)
    
    # Mock query results
    mock_session_instance.execute.side_effect = [
        # First query - connection test
        Mock(first=Mock(return_value=[1])),
        # Second query - tables check
        Mock(fetchall=Mock(return_value=[
            ['wp_posts'], ['wp_postmeta'], ['wp_terms'], ['wp_term_relationships']
        ])),
        # Third query - products count
        Mock(scalar=Mock(return_value=150))
    ]
    
    with patch.object(mock_connection, 'get_session', return_value=mock_session):
        result = mock_connection.test_connection()
    
    assert result['status'] == 'success'
    assert result['connection_test'] is True
    assert len(result['tables_found']) == 4
    assert len(result['missing_tables']) == 0
    assert result['products_count'] == 150


@patch('structura_ai.services.mysql_connection.sessionmaker')
@patch('structura_ai.services.mysql_connection.create_engine')
def test_connection_test_failure(mock_create_engine, mock_sessionmaker, mock_connection):
    """Test connection test failure."""
    # Mock engine and session
    mock_engine = Mock()
    mock_create_engine.return_value = mock_engine
    
    mock_session = Mock()
    mock_sessionmaker.return_value = mock_session
    
    # Mock session to raise exception
    mock_session.__enter__ = Mock(side_effect=OperationalError("Connection failed", None, None))
    mock_session.__exit__ = Mock(return_value=None)
    
    with patch.object(mock_connection, 'get_session', return_value=mock_session):
        result = mock_connection.test_connection()
    
    assert result['status'] == 'error'
    assert 'error' in result
    assert result['error_type'] == 'OperationalError'


def test_woocommerce_repository_initialization(mock_site):
    """Test WooCommerceRepository initialization."""
    with patch('structura_ai.services.woocommerce_repository.get_woocommerce_connection') as mock_get_conn:
        mock_connection = Mock()
        mock_get_conn.return_value = mock_connection
        
        repo = WooCommerceRepository(mock_site)
        
        assert repo.site == mock_site
        assert repo.connection == mock_connection
        mock_get_conn.assert_called_once_with(mock_site)


def test_product_count_query_building(mock_site):
    """Test product count query building logic."""
    with patch('structura_ai.services.woocommerce_repository.get_woocommerce_connection'):
        repo = WooCommerceRepository(mock_site)
        
        # Mock connection execute_with_retry
        repo.connection.execute_with_retry = Mock(return_value=[[42]])
        
        # Test count without filters
        count = repo.count_products()
        assert count == 42
        
        # Verify query was called
        args, kwargs = repo.connection.execute_with_retry.call_args
        query = args[0]
        assert "COUNT(*)" in query
        assert "post_type = 'product'" in query
        assert "post_status IN ('publish', 'draft')" in query


def test_product_count_with_filters(mock_site):
    """Test product count with category and post ID filters."""
    with patch('structura_ai.services.woocommerce_repository.get_woocommerce_connection'):
        repo = WooCommerceRepository(mock_site)
        
        # Mock connection execute_with_retry
        repo.connection.execute_with_retry = Mock(return_value=[[15]])
        
        # Test count with category filter
        count = repo.count_products(categories=[1, 2, 3])
        assert count == 15
        
        # Verify query includes category join
        args, kwargs = repo.connection.execute_with_retry.call_args
        query = args[0]
        params = args[1]
        
        assert "term_relationships" in query
        assert "term_taxonomy" in query
        assert "taxonomy = 'product_cat'" in query
        assert "term_id IN :categories" in query
        assert params['categories'] == (1, 2, 3)


def test_product_update_concurrency_control(mock_site):
    """Test product update with optimistic concurrency control."""
    from datetime import datetime
    
    with patch('structura_ai.services.woocommerce_repository.get_woocommerce_connection'):
        repo = WooCommerceRepository(mock_site)
        
        # Mock successful update
        mock_session = Mock()
        mock_result = Mock()
        mock_result.rowcount = 1
        mock_session.execute.return_value = mock_result
        
        repo.connection.get_transaction = Mock()
        repo.connection.get_transaction.return_value.__enter__ = Mock(return_value=mock_session)
        repo.connection.get_transaction.return_value.__exit__ = Mock(return_value=None)
        
        expected_modified = datetime(2023, 8, 5, 10, 0, 0)
        result = repo.update_product_content(
            post_id=123,
            new_content="<p>Updated content</p>",
            expected_modified=expected_modified
        )
        
        assert result is True
        
        # Verify query includes concurrency check
        args, kwargs = mock_session.execute.call_args
        query = str(args[0])
        params = args[1]
        
        assert "post_modified = :expected_modified" in query
        assert params['expected_modified'] == expected_modified
        assert params['post_id'] == 123
        assert params['new_content'] == "<p>Updated content</p>"


def test_product_update_concurrency_conflict(mock_site):
    """Test product update with concurrency conflict."""
    from datetime import datetime
    
    with patch('structura_ai.services.woocommerce_repository.get_woocommerce_connection'):
        repo = WooCommerceRepository(mock_site)
        
        # Mock concurrency conflict (no rows updated)
        mock_session = Mock()
        mock_result = Mock()
        mock_result.rowcount = 0  # No rows updated = conflict
        mock_session.execute.return_value = mock_result
        
        repo.connection.get_transaction = Mock()
        repo.connection.get_transaction.return_value.__enter__ = Mock(return_value=mock_session)
        repo.connection.get_transaction.return_value.__exit__ = Mock(return_value=None)
        
        expected_modified = datetime(2023, 8, 5, 10, 0, 0)
        result = repo.update_product_content(
            post_id=123,
            new_content="<p>Updated content</p>",
            expected_modified=expected_modified
        )
        
        assert result is False  # Should indicate conflict