"""Tests for Phase 5: Pipeline Processing and Operational Modes."""

import json
import pytest
from datetime import datetime, timezone
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any

from src.structura_ai.services.processing_pipeline import (
    ProcessingPipeline,
    ProcessingMode, 
    ProcessingResultStatus,
    ProductProcessingResult,
    BatchProcessingResult
)
from src.structura_ai.services.woocommerce_repository import WooCommerceProduct
from src.structura_ai.services.content_processor import ContentProcessor, ContentValidationError
from src.structura_ai.models import Site, Profile


class TestProcessingPipeline:
    """Test processing pipeline functionality."""
    
    @pytest.fixture
    def mock_site(self):
        """Mock WooCommerce site."""
        site = Mock(spec=Site)
        site.id = 1
        site.name = "test_site"
        site.db_host = "localhost"
        site.db_name = "test_wp"
        site.db_table_prefix = "wp_"
        return site
    
    @pytest.fixture
    def mock_profile(self):
        """Mock processing profile."""
        profile = Mock(spec=Profile)
        profile.id = 1
        profile.name = "test_profile"
        profile.primary_model = "test/model"
        profile.prompt_template = "Format this: {content}"
        profile.temperature = 0.0
        return profile
    
    @pytest.fixture
    def mock_ai_service(self):
        """Mock AI service."""
        ai_service = Mock()
        ai_service.format_product_content.return_value = {
            'output_html': '<p>Formatted content</p>',
            'model_name': 'test/model',
            'temperature': 0.0,
            'prompt_template': 'Format this: {content}',
            'cache_hit': False,
            'processing_time': 0.5
        }
        return ai_service
    
    @pytest.fixture
    def mock_content_processor(self):
        """Mock content processor."""
        processor = Mock(spec=ContentProcessor)
        processor.generate_content_hash.return_value = "test_hash_123"
        processor.process_with_sanitization.return_value = {
            'final_content': '<p>Formatted and sanitized content</p>',
            'guard_rail_validation': {'similarity_passed': True, 'length_passed': True},
            'html_sanitization': {'is_valid': True, 'tags_removed': 0},
            'processing_errors': []
        }
        return processor
    
    @pytest.fixture
    def mock_wc_product(self):
        """Mock WooCommerce product."""
        product_data = {
            'ID': 123,
            'post_title': 'Test Product',
            'post_content': '<p>Original content</p>',
            'post_status': 'publish',
            'post_modified': datetime.now(),
            'post_modified_gmt': datetime.now()
        }
        product = WooCommerceProduct(product_data)
        product.meta = {}  # No existing metadata
        return product
    
    @pytest.fixture
    def pipeline(self, mock_site, mock_profile, mock_ai_service, mock_content_processor):
        """Create processing pipeline with mocked dependencies."""
        with patch('src.structura_ai.services.processing_pipeline.WooCommerceRepository') as mock_repo_class:
            mock_repo = Mock()
            mock_repo_class.return_value = mock_repo
            
            pipeline = ProcessingPipeline(
                site=mock_site,
                profile=mock_profile,
                ai_service=mock_ai_service,
                content_processor=mock_content_processor,
                skip_if_processed=True
            )
            pipeline.wc_repo = mock_repo
            return pipeline
    
    def test_process_single_product_success_preview(self, pipeline, mock_wc_product):
        """Test successful processing in preview mode."""
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.SUCCESS
        assert result.product_id == 123
        assert result.product_title == 'Test Product'
        assert result.original_content == '<p>Original content</p>'
        assert result.final_content == '<p>Formatted and sanitized content</p>'
        assert not result.backup_created  # No backup in preview mode
        
        # Verify AI service was called
        pipeline.ai_service.format_product_content.assert_called_once()
        
        # Verify content processor was called
        pipeline.content_processor.process_with_sanitization.assert_called_once()
    
    def test_process_single_product_success_run(self, pipeline, mock_wc_product):
        """Test successful processing in run mode."""
        # Mock successful database operations
        pipeline.wc_repo.set_product_meta.return_value = True
        pipeline.wc_repo.update_product_content.return_value = True
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.RUN)
        
        assert result.status == ProcessingResultStatus.SUCCESS
        assert result.backup_created
        
        # Verify backup was created
        backup_calls = [call for call in pipeline.wc_repo.set_product_meta.call_args_list 
                       if call[0][1] == ProcessingPipeline.META_ORIGINAL_BACKUP]
        assert len(backup_calls) == 1
        
        # Verify content was updated
        pipeline.wc_repo.update_product_content.assert_called_once()
        
        # Verify processing metadata was set
        meta_calls = [call for call in pipeline.wc_repo.set_product_meta.call_args_list 
                     if call[0][1] == ProcessingPipeline.META_PROCESSED]
        assert len(meta_calls) == 1
    
    def test_process_product_no_content_skip(self, pipeline):
        """Test skipping product with no content."""
        product_data = {
            'ID': 124,
            'post_title': 'Empty Product',
            'post_content': '',
            'post_status': 'publish',
            'post_modified': datetime.now(),
            'post_modified_gmt': datetime.now()
        }
        empty_product = WooCommerceProduct(product_data)
        
        result = pipeline._process_single_product(empty_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.SKIPPED
        assert result.skip_reason == "Product has no content"
        
        # Verify AI service was not called
        pipeline.ai_service.format_product_content.assert_not_called()
    
    def test_process_product_already_processed_skip(self, pipeline, mock_wc_product):
        """Test skipping already processed product."""
        # Mock existing processing metadata
        processed_data = {
            'input_hash': 'test_hash_123',
            'processed_at': datetime.now(timezone.utc).isoformat(),
            'version': 1
        }
        mock_wc_product.meta = {
            ProcessingPipeline.META_PROCESSED: json.dumps(processed_data)
        }
        
        # Mock content processor to return same hash
        pipeline.content_processor.generate_content_hash.return_value = 'test_hash_123'
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.SKIPPED
        assert "Already processed with same content" in result.skip_reason
        
        # Verify AI service was not called
        pipeline.ai_service.format_product_content.assert_not_called()
    
    def test_process_product_content_changed_reprocess(self, pipeline, mock_wc_product):
        """Test reprocessing when content has changed."""
        # Mock existing processing metadata with different hash
        processed_data = {
            'input_hash': 'old_hash_456',
            'processed_at': datetime.now(timezone.utc).isoformat(),
            'version': 1
        }
        mock_wc_product.meta = {
            ProcessingPipeline.META_PROCESSED: json.dumps(processed_data)
        }
        
        # Mock content processor to return different hash
        pipeline.content_processor.generate_content_hash.return_value = 'new_hash_789'
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.SUCCESS
        
        # Verify AI service was called (reprocessing)
        pipeline.ai_service.format_product_content.assert_called_once()
    
    def test_process_product_guard_rail_rejection(self, pipeline, mock_wc_product):
        """Test guard-rail rejection."""
        # Mock content processor to raise validation error
        pipeline.content_processor.process_with_sanitization.side_effect = ContentValidationError(
            "Similarity too low", "similarity_too_low"
        )
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.GUARD_RAIL_REJECTED
        assert "Similarity too low" in result.error_message
    
    def test_process_product_backup_failure(self, pipeline, mock_wc_product):
        """Test backup creation failure."""
        # Mock backup creation failure with exception
        pipeline.wc_repo.set_product_meta.side_effect = [Exception("Database connection failed")]  # Backup fails
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.RUN)
        
        assert result.status == ProcessingResultStatus.BACKUP_FAILED
        assert "Failed to create original content backup" in result.error_message
    
    def test_process_product_cache_hit(self, pipeline, mock_wc_product):
        """Test cache hit scenario."""
        # Mock AI service returning cache hit
        pipeline.ai_service.format_product_content.return_value = {
            'output_html': '<p>Cached content</p>',
            'model_name': 'test/model',
            'cache_hit': True,
            'cached_at': datetime.now(timezone.utc).isoformat(),
            'similarity_score': 0.99
        }
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.PREVIEW)
        
        assert result.status == ProcessingResultStatus.CACHE_HIT
        assert result.cache_hit
        assert result.final_content == '<p>Cached content</p>'
        
        # Verify content processor sanitization was not called for cache hit
        pipeline.content_processor.process_with_sanitization.assert_not_called()
    
    def test_create_original_backup_write_once(self, pipeline, mock_wc_product):
        """Test write-once backup creation."""
        pipeline.wc_repo.set_product_meta.return_value = True
        
        success = pipeline._create_original_backup(mock_wc_product)
        
        assert success
        
        # Verify backup metadata structure
        call_args = pipeline.wc_repo.set_product_meta.call_args
        assert call_args[0][0] == 123  # product ID
        assert call_args[0][1] == ProcessingPipeline.META_ORIGINAL_BACKUP
        assert call_args[1]['write_once'] is True
        
        # Verify backup data structure
        backup_data = json.loads(call_args[0][2])
        assert 'original_content' in backup_data
        assert 'backed_up_at' in backup_data
        assert 'version' in backup_data
        assert 'content_hash' in backup_data
    
    def test_create_original_backup_already_exists(self, pipeline, mock_wc_product):
        """Test backup when it already exists."""
        # Mock existing backup
        mock_wc_product.meta = {
            ProcessingPipeline.META_ORIGINAL_BACKUP: json.dumps({
                'original_content': '<p>Original</p>',
                'backed_up_at': datetime.now(timezone.utc).isoformat()
            })
        }
        
        success = pipeline._create_original_backup(mock_wc_product)
        
        assert success
        
        # Verify no new backup was attempted
        pipeline.wc_repo.set_product_meta.assert_not_called()
    
    def test_batch_processing_preview_mode(self, pipeline):
        """Test batch processing in preview mode."""
        # Mock products
        products = []
        for i in range(3):
            product_data = {
                'ID': 100 + i,
                'post_title': f'Product {i}',
                'post_content': f'<p>Content {i}</p>',
                'post_status': 'publish',
                'post_modified': datetime.now(),
                'post_modified_gmt': datetime.now()
            }
            product = WooCommerceProduct(product_data)
            product.meta = {}
            products.append(product)
        
        pipeline.wc_repo.get_products.return_value = products
        
        result = pipeline.process_batch(ProcessingMode.PREVIEW, batch_size=10)
        
        assert result.mode == ProcessingMode.PREVIEW
        assert result.total_processed == 3
        assert result.successful == 3
        assert result.errors == 0
        assert len(result.product_results) == 3
        
        # Verify no database updates in preview mode
        pipeline.wc_repo.update_product_content.assert_not_called()
        pipeline.wc_repo.set_product_meta.assert_not_called()
    
    def test_batch_processing_run_mode(self, pipeline):
        """Test batch processing in run mode."""
        # Mock single product
        product_data = {
            'ID': 200,
            'post_title': 'Test Product',
            'post_content': '<p>Original</p>',
            'post_status': 'publish',
            'post_modified': datetime.now(),
            'post_modified_gmt': datetime.now()
        }
        product = WooCommerceProduct(product_data)
        product.meta = {}
        
        pipeline.wc_repo.get_products.return_value = [product]
        pipeline.wc_repo.set_product_meta.return_value = True
        pipeline.wc_repo.update_product_content.return_value = True
        
        result = pipeline.process_batch(ProcessingMode.RUN, batch_size=10)
        
        assert result.mode == ProcessingMode.RUN
        assert result.total_processed == 1
        assert result.successful == 1
        
        # Verify database updates in run mode
        pipeline.wc_repo.update_product_content.assert_called_once()
        # Should have 2 calls: backup + processing metadata
        assert pipeline.wc_repo.set_product_meta.call_count == 2
    
    def test_batch_processing_with_filters(self, pipeline):
        """Test batch processing with category and post ID filters."""
        pipeline.wc_repo.get_products.return_value = []
        
        result = pipeline.process_batch(
            ProcessingMode.PREVIEW,
            categories=[1, 2, 3],
            post_ids=[100, 101, 102],
            batch_size=5,
            offset=10
        )
        
        # Verify filters were passed to repository
        pipeline.wc_repo.get_products.assert_called_once_with(
            categories=[1, 2, 3],
            post_ids=[100, 101, 102],
            batch_size=5,
            offset=10,
            include_meta=True
        )
    
    def test_batch_processing_error_handling(self, pipeline):
        """Test error handling in batch processing."""
        # Mock repository error
        pipeline.wc_repo.get_products.side_effect = Exception("Database error")
        
        result = pipeline.process_batch(ProcessingMode.PREVIEW)
        
        assert result.total_processed == 0
        assert result.errors == 1
        assert result.successful == 0
    
    def test_diff_report_generation(self, pipeline):
        """Test diff report generation."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test Product",
            status=ProcessingResultStatus.SUCCESS,
            original_content="<p>Original</p>",
            final_content="<p>Modified</p>"
        )
        
        with patch.object(pipeline, 'diff_visualizer') as mock_visualizer:
            mock_visualizer.generate_diff.return_value = Mock()
            mock_visualizer.format_diff.return_value = "Diff output"
            
            diff = pipeline.generate_diff_report(result)
            
            assert diff == "Diff output"
            mock_visualizer.generate_diff.assert_called_once()
            mock_visualizer.format_diff.assert_called_once()
    
    def test_diff_report_no_changes(self, pipeline):
        """Test diff report when no changes."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test Product",
            status=ProcessingResultStatus.SUCCESS,
            original_content="<p>Same</p>",
            final_content="<p>Same</p>"
        )
        
        diff = pipeline.generate_diff_report(result)
        
        assert diff == "No content changes detected."
    
    def test_batch_report_json_format(self, pipeline):
        """Test JSON batch report generation."""
        result = BatchProcessingResult(
            mode=ProcessingMode.PREVIEW,
            batch_size=10,
            total_processed=1,
            successful=1,
            errors=0,
            skipped=0,
            cache_hits=0,
            guard_rail_rejections=0,
            backup_failures=0,
            processing_time_seconds=1.5,
            product_results=[
                ProductProcessingResult(
                    product_id=123,
                    product_title="Test",
                    status=ProcessingResultStatus.SUCCESS
                )
            ]
        )
        
        report = pipeline.generate_batch_report(result, report_format="json")
        
        report_data = json.loads(report)
        assert report_data['mode'] == 'preview'
        assert report_data['total_processed'] == 1
        assert report_data['successful'] == 1
        assert len(report_data['product_results']) == 1
    
    def test_processing_metadata_structure(self, pipeline, mock_wc_product):
        """Test processing metadata structure."""
        pipeline.wc_repo.set_product_meta.return_value = True
        pipeline.wc_repo.update_product_content.return_value = True
        
        result = pipeline._process_single_product(mock_wc_product, ProcessingMode.RUN)
        
        # Find the processing metadata call
        meta_calls = [call for call in pipeline.wc_repo.set_product_meta.call_args_list 
                     if call[0][1] == ProcessingPipeline.META_PROCESSED]
        
        assert len(meta_calls) == 1
        metadata_json = meta_calls[0][0][2]
        metadata = json.loads(metadata_json)
        
        # Verify metadata structure
        required_fields = ['input_hash', 'output_hash', 'processed_at', 'version', 'model_name']
        for field in required_fields:
            assert field in metadata
        
        assert metadata['version'] == 1
        assert isinstance(metadata['processed_at'], str)  # ISO format timestamp


class TestProductProcessingResult:
    """Test product processing result data class."""
    
    def test_result_success_properties(self):
        """Test result properties for successful processing."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test",
            status=ProcessingResultStatus.SUCCESS,
            original_content="<p>Original</p>",
            final_content="<p>Modified</p>"
        )
        
        assert result.success
        assert result.content_changed
    
    def test_result_no_change_properties(self):
        """Test result properties for no content change."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test",
            status=ProcessingResultStatus.SUCCESS,
            original_content="<p>Same</p>",
            final_content="<p>Same</p>"
        )
        
        assert result.success
        assert not result.content_changed
    
    def test_result_error_properties(self):
        """Test result properties for error status."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test",
            status=ProcessingResultStatus.ERROR,
            error_message="Processing failed"
        )
        
        assert not result.success
    
    def test_result_to_dict(self):
        """Test conversion to dictionary."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test",
            status=ProcessingResultStatus.SUCCESS,
            processing_metadata={"key": "value"}
        )
        
        result_dict = result.to_dict()
        
        assert result_dict['product_id'] == 123
        assert result_dict['status'] == 'success'
        assert result_dict['processing_metadata'] == {"key": "value"}


class TestBatchProcessingResult:
    """Test batch processing result data class."""
    
    def test_success_rate_calculation(self):
        """Test success rate calculation."""
        result = BatchProcessingResult(
            mode=ProcessingMode.PREVIEW,
            batch_size=10,
            total_processed=10,
            successful=8,
            errors=2,
            skipped=0,
            cache_hits=0,
            guard_rail_rejections=0,
            backup_failures=0,
            processing_time_seconds=5.0,
            product_results=[]
        )
        
        assert result.success_rate == 0.8
    
    def test_success_rate_zero_processed(self):
        """Test success rate with zero processed."""
        result = BatchProcessingResult(
            mode=ProcessingMode.PREVIEW,
            batch_size=10,
            total_processed=0,
            successful=0,
            errors=0,
            skipped=0,
            cache_hits=0,
            guard_rail_rejections=0,
            backup_failures=0,
            processing_time_seconds=0.0,
            product_results=[]
        )
        
        assert result.success_rate == 0.0
    
    def test_batch_to_dict(self):
        """Test batch result conversion to dictionary."""
        result = BatchProcessingResult(
            mode=ProcessingMode.RUN,
            batch_size=5,
            total_processed=5,
            successful=4,
            errors=1,
            skipped=0,
            cache_hits=0,
            guard_rail_rejections=0,
            backup_failures=0,
            processing_time_seconds=10.0,
            product_results=[
                ProductProcessingResult(
                    product_id=123,
                    product_title="Test",
                    status=ProcessingResultStatus.SUCCESS
                )
            ]
        )
        
        result_dict = result.to_dict()
        
        assert result_dict['mode'] == 'run'
        assert result_dict['total_processed'] == 5
        assert result_dict['successful'] == 4
        assert len(result_dict['product_results']) == 1
        assert result_dict['product_results'][0]['status'] == 'success'


if __name__ == '__main__':
    pytest.main([__file__])