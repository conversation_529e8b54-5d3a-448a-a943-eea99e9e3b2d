"""
Tests for the report generation module.

Tests cover JSON/CSV report generation, job statistics calculation,
and report comparison functionality.
"""

import csv
import json
import pytest
from datetime import datetime, timedelta
from pathlib import Path
from tempfile import TemporaryDirectory

from src.structura_ai.services.report_generator import (
    ReportGenerator,
    ReportFormat,
    ProcessingStatus,
    ProductProcessingResult,
    JobStatistics
)


class TestProductProcessingResult:
    """Test ProductProcessingResult dataclass."""
    
    def test_basic_creation(self):
        """Test basic result creation."""
        result = ProductProcessingResult(
            product_id=123,
            product_title="Test Product",
            product_slug="test-product",
            status=ProcessingStatus.SUCCESS,
            timestamp=datetime.utcnow(),
            original_length=100,
            processed_length=150
        )
        
        assert result.product_id == 123
        assert result.product_title == "Test Product"
        assert result.status == ProcessingStatus.SUCCESS
        assert result.length_difference == 50  # Calculated in __post_init__
    
    def test_post_init_calculations(self):
        """Test post-init calculations."""
        result = ProductProcessingResult(
            product_id=1,
            product_title="Test",
            product_slug="test",
            status=ProcessingStatus.SUCCESS,
            timestamp=datetime.utcnow(),
            original_length=200,
            processed_length=180
        )
        
        assert result.length_difference == 20
        assert result.html_validation_errors == []  # Default initialized
    
    def test_all_processing_statuses(self):
        """Test all processing status values."""
        statuses = [
            ProcessingStatus.SUCCESS,
            ProcessingStatus.ERROR,
            ProcessingStatus.SKIPPED,
            ProcessingStatus.CACHE_HIT,
            ProcessingStatus.VALIDATION_FAILED
        ]
        
        for status in statuses:
            result = ProductProcessingResult(
                product_id=1,
                product_title="Test",
                product_slug="test",
                status=status,
                timestamp=datetime.utcnow(),
                original_length=100,
                processed_length=100
            )
            assert result.status == status


class TestJobStatistics:
    """Test JobStatistics dataclass."""
    
    def test_basic_creation(self):
        """Test basic job statistics creation."""
        now = datetime.utcnow()
        stats = JobStatistics(
            job_id="test-job-1",
            site_name="test-site",
            profile_name="test-profile",
            started_at=now,
            total_products=10,
            successful_count=8,
            error_count=2
        )
        
        assert stats.job_id == "test-job-1"
        assert stats.total_products == 10
        assert stats.successful_count == 8
        assert stats.success_rate == 80.0  # Calculated in __post_init__
    
    def test_post_init_calculations(self):
        """Test post-init calculations."""
        stats = JobStatistics(
            job_id="test",
            site_name="test",
            profile_name="test",
            started_at=datetime.utcnow(),
            total_products=100,
            cache_hit_count=25,
            total_processing_time_ms=5000.0
        )
        
        assert stats.average_processing_time_ms == 50.0  # 5000 / 100
        assert stats.cache_hit_rate == 25.0  # 25 / 100 * 100
    
    def test_zero_products_handling(self):
        """Test handling of zero products."""
        stats = JobStatistics(
            job_id="test",
            site_name="test",
            profile_name="test",
            started_at=datetime.utcnow(),
            total_products=0
        )
        
        assert stats.average_processing_time_ms == 0.0
        assert stats.cache_hit_rate == 0.0
        assert stats.success_rate == 0.0


class TestReportGenerator:
    """Test report generation functionality."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.temp_dir = TemporaryDirectory()
        self.output_dir = Path(self.temp_dir.name)
        self.generator = ReportGenerator(output_dir=self.output_dir)
    
    def teardown_method(self):
        """Clean up test fixtures."""
        self.temp_dir.cleanup()
    
    def create_sample_results(self) -> list:
        """Create sample product processing results for testing."""
        now = datetime.utcnow()
        
        return [
            ProductProcessingResult(
                product_id=1,
                product_title="Test Product 1",
                product_slug="test-product-1",
                status=ProcessingStatus.SUCCESS,
                timestamp=now,
                original_length=100,
                processed_length=120,
                similarity_score=0.95,
                processing_time_ms=150.0,
                cache_hit=False,
                model_name="test-model"
            ),
            ProductProcessingResult(
                product_id=2,
                product_title="Test Product 2",
                product_slug="test-product-2",
                status=ProcessingStatus.CACHE_HIT,
                timestamp=now + timedelta(seconds=1),
                original_length=200,
                processed_length=200,
                similarity_score=1.0,
                processing_time_ms=5.0,
                cache_hit=True,
                model_name="test-model"
            ),
            ProductProcessingResult(
                product_id=3,
                product_title="Test Product 3",
                product_slug="test-product-3",
                status=ProcessingStatus.ERROR,
                timestamp=now + timedelta(seconds=2),
                original_length=150,
                processed_length=0,
                processing_time_ms=200.0,
                cache_hit=False,
                error_message="Test error",
                error_type="test_error"
            ),
            ProductProcessingResult(
                product_id=4,
                product_title="Test Product 4",
                product_slug="test-product-4",
                status=ProcessingStatus.SKIPPED,
                timestamp=now + timedelta(seconds=3),
                original_length=80,
                processed_length=80,
                processing_time_ms=10.0,
                cache_hit=False
            )
        ]
    
    def create_sample_job_stats(self) -> JobStatistics:
        """Create sample job statistics for testing."""
        now = datetime.utcnow()
        
        return JobStatistics(
            job_id="test-job-123",
            site_name="test-site",
            profile_name="test-profile",
            started_at=now,
            completed_at=now + timedelta(minutes=5),
            total_products=4,
            successful_count=1,
            error_count=1,
            skipped_count=1,
            cache_hit_count=1,
            total_processing_time_ms=365.0,
            llm_calls_made=3,
            llm_calls_cached=1,
            error_types={"test_error": 1}
        )
    
    def test_init_creates_output_directory(self):
        """Test that initialization creates output directory."""
        assert self.output_dir.exists()
        assert self.output_dir.is_dir()
    
    def test_generate_json_report(self):
        """Test JSON report generation."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.JSON,
            output_file="test_report"
        )
        
        assert "json" in files
        json_file = Path(files["json"])
        assert json_file.exists()
        
        # Verify JSON content
        with open(json_file, 'r') as f:
            data = json.load(f)
        
        assert "report_metadata" in data
        assert "job_statistics" in data
        assert "product_results" in data
        assert "summary" in data
        
        assert len(data["product_results"]) == 4
        assert data["job_statistics"]["job_id"] == "test-job-123"
        assert data["report_metadata"]["total_products"] == 4
    
    def test_generate_csv_report(self):
        """Test CSV report generation."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.CSV,
            output_file="test_report"
        )
        
        assert "csv" in files
        csv_file = Path(files["csv"])
        assert csv_file.exists()
        
        # Verify CSV content
        with open(csv_file, 'r') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        assert len(rows) == 4
        
        # Check first row
        first_row = rows[0]
        assert first_row["product_id"] == "1"
        assert first_row["product_title"] == "Test Product 1"
        assert first_row["status"] == "success"
        assert first_row["similarity_score"] == "0.95"
    
    def test_generate_both_formats(self):
        """Test generating both JSON and CSV reports."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.BOTH,
            output_file="test_report"
        )
        
        assert "json" in files
        assert "csv" in files
        assert Path(files["json"]).exists()
        assert Path(files["csv"]).exists()
    
    def test_auto_generated_filename(self):
        """Test auto-generated filename with timestamp."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.JSON
        )
        
        json_file = Path(files["json"])
        assert "job_report_" in json_file.name
        assert json_file.name.endswith(".json")
    
    def test_generate_summary_stats(self):
        """Test generating summary statistics from results."""
        results = self.create_sample_results()
        
        stats = self.generator.generate_summary_stats(results)
        
        assert stats.total_products == 4
        assert stats.successful_count == 1
        assert stats.error_count == 1
        assert stats.skipped_count == 1
        assert stats.cache_hit_count == 1
        assert stats.total_processing_time_ms == 365.0
        assert stats.average_processing_time_ms == 91.25  # 365 / 4
        
        # Check error types
        assert "test_error" in stats.error_types
        assert stats.error_types["test_error"] == 1
    
    def test_generate_summary_stats_empty_results(self):
        """Test generating summary stats from empty results."""
        stats = self.generator.generate_summary_stats([])
        
        assert stats.total_products == 0
        assert stats.successful_count == 0
        assert stats.average_processing_time_ms == 0.0
    
    def test_load_report_from_json(self):
        """Test loading previously generated JSON report."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        # Generate report
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.JSON,
            output_file="test_load"
        )
        
        # Load report
        json_file = Path(files["json"])
        loaded_data = self.generator.load_report_from_json(json_file)
        
        assert loaded_data["job_statistics"]["job_id"] == "test-job-123"
        assert len(loaded_data["product_results"]) == 4
    
    def test_compare_reports(self):
        """Test report comparison functionality."""
        results1 = self.create_sample_results()
        job_stats1 = self.create_sample_job_stats()
        
        # Create second set of results with different metrics
        results2 = self.create_sample_results()
        results2[0].processing_time_ms = 100.0  # Different processing time
        job_stats2 = self.create_sample_job_stats()
        job_stats2.job_id = "test-job-456"
        job_stats2.total_processing_time_ms = 300.0
        
        # Generate both reports
        files1 = self.generator.generate_job_report(
            job_stats=job_stats1,
            product_results=results1,
            report_format=ReportFormat.JSON,
            output_file="report1"
        )
        
        files2 = self.generator.generate_job_report(
            job_stats=job_stats2,
            product_results=results2,
            report_format=ReportFormat.JSON,
            output_file="report2"
        )
        
        # Compare reports
        comparison = self.generator.compare_reports(
            Path(files1["json"]),
            Path(files2["json"])
        )
        
        assert "report_files" in comparison
        assert "metrics_comparison" in comparison
        assert "generated_at" in comparison
        
        # Check metrics comparison structure
        metrics = comparison["metrics_comparison"]
        assert "total_products" in metrics
        assert "success_rate" in metrics
        assert "cache_hit_rate" in metrics
        assert "avg_processing_time" in metrics
        
        # Check that differences are calculated
        for metric_name, metric_data in metrics.items():
            assert "baseline" in metric_data
            assert "comparison" in metric_data
            assert "difference" in metric_data
    
    def test_json_serialization_with_datetime(self):
        """Test JSON serialization handles datetime objects."""
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.JSON,
            output_file="datetime_test"
        )
        
        # Verify JSON can be loaded (datetime converted to ISO string)
        with open(files["json"], 'r') as f:
            data = json.load(f)
        
        # Check that timestamps are ISO strings
        assert isinstance(data["job_statistics"]["started_at"], str)
        assert "T" in data["job_statistics"]["started_at"]  # ISO format marker
        
        for result in data["product_results"]:
            assert isinstance(result["timestamp"], str)
    
    def test_csv_report_with_unicode_content(self):
        """Test CSV report generation with Unicode content."""
        now = datetime.utcnow()
        
        results = [
            ProductProcessingResult(
                product_id=1,
                product_title="Produit français avec accents",
                product_slug="produit-francais",
                status=ProcessingStatus.SUCCESS,
                timestamp=now,
                original_length=100,
                processed_length=120,
                error_message="Erreur: caractères spéciaux €£¥"
            )
        ]
        
        job_stats = JobStatistics(
            job_id="unicode-test",
            site_name="Site français",
            profile_name="Profil test",
            started_at=now,
            total_products=1
        )
        
        files = self.generator.generate_job_report(
            job_stats=job_stats,
            product_results=results,
            report_format=ReportFormat.CSV,
            output_file="unicode_test"
        )
        
        # Verify CSV can be read with Unicode content
        with open(files["csv"], 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            rows = list(reader)
        
        assert len(rows) == 1
        assert "français" in rows[0]["product_title"]
        assert "€£¥" in rows[0]["error_message"]
    
    def test_report_generation_error_handling(self):
        """Test error handling in report generation."""
        # Test with invalid output directory
        invalid_generator = ReportGenerator(output_dir=Path("/invalid/path/that/does/not/exist"))
        
        results = self.create_sample_results()
        job_stats = self.create_sample_job_stats()
        
        with pytest.raises(Exception):
            invalid_generator.generate_job_report(
                job_stats=job_stats,
                product_results=results,
                report_format=ReportFormat.JSON
            )


class TestReportFormats:
    """Test report format enums and constants."""
    
    def test_report_format_enum_values(self):
        """Test ReportFormat enum values."""
        assert ReportFormat.JSON.value == "json"
        assert ReportFormat.CSV.value == "csv"
        assert ReportFormat.BOTH.value == "both"
    
    def test_processing_status_enum_values(self):
        """Test ProcessingStatus enum values."""
        assert ProcessingStatus.SUCCESS.value == "success"
        assert ProcessingStatus.ERROR.value == "error"
        assert ProcessingStatus.SKIPPED.value == "skipped"
        assert ProcessingStatus.CACHE_HIT.value == "cache_hit"
        assert ProcessingStatus.VALIDATION_FAILED.value == "validation_failed"


class TestReportIntegration:
    """Integration tests for report generation."""
    
    def test_end_to_end_report_workflow(self):
        """Test complete end-to-end report generation workflow."""
        with TemporaryDirectory() as temp_dir:
            generator = ReportGenerator(output_dir=Path(temp_dir))
            
            # Create realistic processing results
            now = datetime.utcnow()
            results = []
            
            # Simulate processing 50 products with various outcomes
            for i in range(1, 51):
                if i <= 40:
                    status = ProcessingStatus.SUCCESS
                    similarity = 0.95 + (i % 5) * 0.01  # Vary similarity
                    cache_hit = i % 4 == 0  # Every 4th is cache hit
                    error_msg = None
                    error_type = None
                elif i <= 45:
                    status = ProcessingStatus.ERROR
                    similarity = None
                    cache_hit = False
                    error_msg = f"Processing error for product {i}"
                    error_type = "processing_error"
                else:
                    status = ProcessingStatus.SKIPPED
                    similarity = None
                    cache_hit = False
                    error_msg = None
                    error_type = None
                
                result = ProductProcessingResult(
                    product_id=i,
                    product_title=f"Product {i}",
                    product_slug=f"product-{i}",
                    status=status,
                    timestamp=now + timedelta(seconds=i),
                    original_length=100 + i * 10,
                    processed_length=120 + i * 10 if status == ProcessingStatus.SUCCESS else 0,
                    similarity_score=similarity,
                    processing_time_ms=50.0 + i * 2,
                    cache_hit=cache_hit,
                    error_message=error_msg,
                    error_type=error_type,
                    model_name="test-model-v1"
                )
                results.append(result)
            
            # Generate job statistics
            job_stats = generator.generate_summary_stats(results)
            job_stats.job_id = "integration-test-job"
            job_stats.site_name = "integration-test-site"
            job_stats.profile_name = "integration-test-profile"
            
            # Generate both report formats
            files = generator.generate_job_report(
                job_stats=job_stats,
                product_results=results,
                report_format=ReportFormat.BOTH,
                output_file="integration_test"
            )
            
            # Verify both files were created
            assert "json" in files
            assert "csv" in files
            json_file = Path(files["json"])
            csv_file = Path(files["csv"])
            assert json_file.exists()
            assert csv_file.exists()
            
            # Verify JSON report content
            with open(json_file, 'r') as f:
                json_data = json.load(f)
            
            assert json_data["job_statistics"]["total_products"] == 50
            assert json_data["job_statistics"]["successful_count"] == 40
            assert json_data["job_statistics"]["error_count"] == 5
            assert json_data["job_statistics"]["skipped_count"] == 5
            assert len(json_data["product_results"]) == 50
            
            # Verify CSV report content
            with open(csv_file, 'r') as f:
                reader = csv.DictReader(f)
                csv_rows = list(reader)
            
            assert len(csv_rows) == 50
            
            # Check specific row data
            first_success = next(row for row in csv_rows if row["status"] == "success")
            assert first_success["similarity_score"] is not None
            assert first_success["similarity_score"] != ""
            
            first_error = next(row for row in csv_rows if row["status"] == "error")
            assert first_error["error_message"].startswith("Processing error")
            assert first_error["error_type"] == "processing_error"